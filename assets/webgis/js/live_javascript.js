$(".contacts").click(function(){
     //alert($(".mdl-edit-detail").find('input').val());    
});


function extract_tags(){
 var arreditrams    =[];
 var arreditirmsv3  =[];
 var arreditrenstra =[];
 var arrediteprog   =[];
 var arreditdpr	    =[]; 
 var objedittag     ={};
 var data_tag=$("#md-edit-penandaan").find('.tag').not($("#md-edit-penandaan").find('.tag').children());
 var i=0;
 
 data_tag.each(function() {
    
	    	var data_text = $(this).text();
           	var tagkey=data_text.split("|")[0].trim();
            var tagvalue=data_text.split("|")[1].trim();
            
            
            var find = '\n';
            var re = new RegExp(find, 'g');
            tagvalue = tagvalue.replace(re, '');
            
            tagvalue = tagvalue.replace("X","");
            
            if(tagkey=="RAMS" && tagvalue){
	          arreditrams[i] = tagvalue;
	          objedittag[tagkey]=Object.assign({},arreditrams);
	         
	        
	    	}else if(tagkey == 'IRMS' && tagvalue){
	    	  arreditirmsv3[i] = tagvalue;
	    	  objedittag[tagkey]=Object.assign({},arreditirmsv3);
	    	 
	    	  
	    	}else if(tagkey == 'RENSTRA' && tagvalue){
	    	  arreditrenstra[i] =tagvalue;
	    	  objedittag[tagkey]=arreditrenstra;
	    	  objedittag[tagkey]=Object.assign({},arreditrenstra);
	    	  
	    	}else if(tagkey == 'Eprog' && tagvalue){
	    	  arrediteprog[i] =tagvalue;
	    	  objedittag[tagkey]=arrediteprog;
	    	  objedittag[tagkey]=Object.assign({},arrediteprog);
	    	 
	    	}else if(tagkey == 'DPR' && tagvalue){
	   
	    	      arreditdpr[i] =tagvalue;
    	    	  var find = ' ';
                  var re = new RegExp(find, 'g');
    
                  tagvalue = tagvalue.replace(re, '');
                  objedittag[tagkey]=objedittag[tagkey]=Object.assign({},arreditdpr);;
                  
             
              
	    	}
	    
	    	
	    	i++;
		});
	
		
		return objedittag;
}

function setSumberDana(){
    var objsumberdana=JSON.parse($("#objsumberdana").val());
    var html_select=$("#wkdsdana").html();
    const keys = Object.keys(objsumberdana);
    
    $(".contacts").empty();
    var i=0;
    for (const key of keys) {
        var btnlabelclass='';
        if(key=='phln'){
            btnlabelclass='primary';
        }else if(key=='rmp'){
            btnlabelclass='success'
        }else if(key=='pnpb'){
            btnlabelclass='danger'
        }else if(key=='blu'){
            btnlabelclass='warning'
        }else if(key=='sbsn'){
            btnlabelclass='info'
        }else if(key== 'opr'){
            btnlabelclass= 'dark'
        }else if(key== 'pdp'){
            btnlabelclass= 'secondary'
        }else if(key== 'pdn'){
            btnlabelclass= 'outline'
        }
        
        var sdalabel=key.toUpperCase();
        var sdavalue=objsumberdana[key];
        var x= Object.entries(objsumberdana);
        var buttonactionclass='';
        var buttonactionicon='-';
         //alert(i +'=>'+keys.length-1);
         if(i != keys.length-1){
        
            buttonactionclass="danger btn-remove-sda";
            buttonactionicon ="-";
         }else{
            buttonactionclass="success btn-add-sda";
            buttonactionicon ="+";
         }
          var html_container=['<div class="form-group multiple-form-group input-group">',
                                '<div class="input-group-btn input-group-select">',
                                    '<button type="button" class="btn btn-'+btnlabelclass+' dropdown-toggle" data-toggle="dropdown">',
                                        '<span class="concept">'+sdalabel+'</span> <span class="caret"></span>',
                                    '</button>',
                                    '<ul class="dropdown-menu" role="menu" id="kdsdana">',
                                        html_select,
                                    '</ul>',
                                    '</div>',
                                    '<input value='+numberWithCommas(sdavalue)+' onchange="getValue(this);" id="xrm" name="xrm" class="form-control valuesdana" type="text">',
                                    '<span class="input-group-btn">',
                                       '<button id="btn-action-sda-'+i+'" type="button" class="btn btn-'+buttonactionclass+'">'+buttonactionicon+'</button>',
                                    '</span>',
                                '</div>',
                           ].join('\n')
           $(".contacts").append(html_container);
           console.log(i);
           i++;
    }
    
    $(".btn-add-sda").unbind( "click" );
    $(".btn-remove-sda").unbind( "click" );
    $(".btn-add-sda").click(function(){
        var buttonid="#"+this.id;
        $(buttonid).parents('.input-group').remove();
        $(buttonid).remove();
        $(buttonid).removeClass("btn-add-sda");
        $(buttonid).removeClass("btn-success");
          
        $(buttonid).addClass("btn-danger");
        $(buttonid).addClass("btn-remove-sda");
        $(buttonid).text("-");
        setSingleSumberDana(buttonid);
    });
    
    $(".btn-remove-sda").click(function(){
     var buttonid="#"+this.id;
    
     $(buttonid).parents('.form-group.multiple-form-group.input-group').remove();
    });
}

function setSingleSumberDana(lastid){
     //alert(lastid.split('-')[3]);
     var idplus=lastid.split('-')[3];
     var newid= parseInt(idplus)+1;
     var htmldropdownkdsdana=$("#htmldropdownsdana").val();
     var html_container=['<div class="form-group multiple-form-group input-group">',
                                '<div class="input-group-btn input-group-select">',
                                    '<button type="button" class="btn btn-'+''+' dropdown-toggle" data-toggle="dropdown">',
                                        '<span class="concept">'+''+'</span> <span class="caret"></span>',
                                    '</button>',
                                    '<ul class="dropdown-menu" role="menu" id="wkdsdana">',
                                        htmldropdownkdsdana,
                                    '</ul>',
                                    '</div>',
                                    '<input onchange="getValue(this);" id="xrm" name="xrm" class="form-control valuesdana" type="text">',
                                    '<span class="input-group-btn">',
                                       '<button id="btn-action-sda-'+newid+'" type="button" class="btn btn-success btn-add-sda">'+'+'+'</button>',
                                    '</span>',
                                '</div>',
                           ].join('\n')
           $(".contacts").append(html_container);
           
    $(".btn-add-sda").unbind( "click" );
    $(".btn-remove-sda").unbind( "click" );
    $(".btn-add-sda").click(function(){
        var buttonid="#"+this.id;
        $(buttonid).parents('.input-group').remove();
        $(buttonid).remove();
        $(buttonid).removeClass("btn-add-sda");
        $(buttonid).removeClass("btn-success");
          
        $(buttonid).addClass("btn-danger");
        $(buttonid).addClass("btn-remove-sda");
        $(buttonid).text("-");
        setSingleSumberDana(buttonid);
    });
    
     $(".btn-remove-sda").click(function(){
         var buttonid="#"+this.id;
         //alert(buttonid);
         $(buttonid).parents('.form-group.multiple-form-group.input-group').remove();
     });
}

function numberWithCommas(x) {
        //alert(x);
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

    setSumberDana();
    
    
    

