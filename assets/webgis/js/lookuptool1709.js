//function initSelectCombobox(divname, refindex)
//{
//    url = "lookup/fieldlookSearch/" + refindex;
//
//    var options = {
//        ajax: {
//            url: url,
//            type: 'GET',
//            dataType: 'json',
//            // Use "{{{q}}}" as a placeholder and Ajax Bootstrap Select will
//            // automatically replace it with the value of the search query.
//            data: {
//                q: '{{{q}}}'
//            }
//        },
//        locale: {
//            emptyTitle: '<PERSON><PERSON><PERSON>'
//        },
//        log: 3,
//        preprocessData: function (data) {
//            var i, l = data.length, array = [];
//            if (l) {
//                for (i = 0; i < l; i++) {
//                    array.push($.extend(true, data[i], {
//                        text: data[i].val,
//                        value: data[i].id,
//                        data: {
//                            subtext: data[i].id
//                        }
//                    }));
//                }
//            }
//            // You must always return a valid array when processing data. The
//            // data argument passed is a clone and cannot be modified directly.
//            return array;
//        },
//        preserveSelected: true
//    };
//
////    $('.selectpicker-'+divname).selectpicker({
////        liveSearch: true
////    });
//    $('.selectpicker-' + divname).selectpicker({liveSearch: true}).filter('.with-ajax').ajaxSelectPicker(options);
//    //$('.selectpicker-'+divname).append('<option value="#" data-subtext="--Pilih--" selected="selected">--Pilih--</option>').selectpicker('refresh'); 
//    $('#' + divname).selectpicker('refresh');
//    $('#' + divname).trigger('change');
//}
//
//function refreshSelectCombobox(divname, refindex, refresh_field, refresh_value)
//{
//
//    console.log("from refreshselectCombobox>>>>>>>>>>>>>>" + refresh_value);
//
//    //urlstring = "lookup/refreshlookSearch?ix=" + refindex + "&f=" + refresh_field + "&v=" + refresh_value;
//
//    //var refresh_value = (new URL(urlstring)).searchParams.get("v");
//
//    url = "lookup/refreshlookSearch";
//
//    var options = {
//        ajax: {
//            url: url,
//            type: 'GET',
//            dataType: 'json',
//            cache: false,
//            // Use "{{{q}}}" as a placeholder and Ajax Bootstrap Select will
//            // automatically replace it with the value of the search query.
//            data: {
//                q: '{{{q}}}',
//                ix: refindex,
//                f: refresh_field,
//                v: refresh_value
//            }
//        },
//        locale: {
//            emptyTitle: 'Pilih Ketikan Kata'
//        },
//        log: 3,
//        preprocessData: function (data) {
//            var i, l = data.length, array = [];
//            if (l) {
//                for (i = 0; i < l; i++) {
//                    array.push($.extend(true, data[i], {
//                        text: data[i].val,
//                        value: data[i].id,
//                        data: {
//                            subtext: data[i].id
//                        }
//                    }));
//                }
//            }
//            // You must always return a valid array when processing data. The
//            // data argument passed is a clone and cannot be modified directly.
//            return array;
//        },
//        preserveSelected: true
//    };
//
////    $('.selectpicker-'+divname).selectpicker({
////        liveSearch: true
////    });
//    $('.selectpicker-' + divname).selectpicker({liveSearch: true}).filter('.with-ajax').ajaxSelectPicker(options);
//    //$('.selectpicker-'+divname).append('<option value="#" data-subtext="--Pilih--" selected="selected">--Pilih--</option>').selectpicker('refresh'); 
//    $('#' + divname).trigger('change');
//}
function setCoordSTA(divname, selvalue, sta) {
    // url = "exercise_wp/get_lonlat/"+selvalue1+"/"+selvalue2;

    url = "bm_lrs/lrs_tools/lrs_coord_sta/" + selvalue;


    $.get(url).done(function (data) {
        //alert(data);

        jdata = JSON.parse(data);
        console.log(jdata);
//            
//            $('#'+divname).empty();
        var lonlat = jdata.val;
        var lon = lonlat.split("||")[0];
        var lat = lonlat.split("||")[1];

        if (divname == 'longitude') {
            $('#' + divname).val(lon);
        } else if (divname == 'latitude')
        {
            $('#' + divname).val(lat);
        } else if (divname == 'ylongitude')
        {
            $('#' + divname).val(lon);
        } else if (divname == 'ylatitude')
        {
            $('#' + divname).val(lat);
        } else if (divname == 'wlongitude')
        {
            $('#' + divname).val(lon);
        } else if (divname == 'wlatitude')
        {
            $('#' + divname).val(lat);
        }

    })
    .fail(function () {
        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
    })
    .always(function () {
        // alert("finished");
    });
}

function setSTACoord(id, sta, ruas, divname) {
    // url = "exercise_wp/get_lonlat/"+selvalue1+"/"+selvalue2;

    url = "bm_lrs/lrs_tools/lrs_sta_coord/" + ruas + "/" + sta;

    $.ajax({
        type: 'GET',
        url: url,
        dataType: 'json',
        async: false,
        //data: {postVar1: 'theValue1', postVar2: 'theValue2'},
        beforeSend: function () {
            // this is where we append a loading image
            //$('#ajax-panel').html('<div class="loading"><img src="/images/loading.gif" alt="Loading..." /></div>');
        },
        success: function (data) {
            // successful request; do something with the data
            //console.log(data);

            var lon = data.lon;
            var lat = data.lat;
            
            if (id == 'akhir')
            {

                switch (divname) {
                    case 'longitude2' :
                    case 'ylongitude2' :
                    case 'wlongitude2' :
                        $('#' + divname).val(lon);
                        break;
                    case 'latitude2' :
                    case 'ylatitude2' :
                    case 'wlatitude2' :
                        $('#' + divname).val(lat);
                        break;
                }
            } else {
                switch (divname) {
                    case 'longitude' :
                    case 'ylongitude' :
                    case 'wlongitude' :
                        $('#' + divname).val(lon);
                        break;
                    case 'latitude' :
                    case 'ylatitude' :
                    case 'wlatitude' :
                        $('#' + divname).val(lat);
                        break;
                }

            }

        },
        error: function () {
            // failed request; give feedback to user
           // $('#ajax-panel').html('<p class="error"><strong>Oops!</strong> Try that again in a few moments.</p>');
        }
    });
//            $.get(url).done(function (data) {
//    //alert(data);
//
//    jdata = JSON.parse(data);
//            console.log(jdata);
////            
////            $('#'+divname).empty();
//            // var lonlat = jdata.val;
//            var lon = jdata.lon;
//            var lat = jdata.lat;
//            if (id == 'akhir')
//    {
//
//    switch (divname) {
//    case 'longitude2' :
//            $('#' + divname).val(lon);
//            break;
//            case 'latitude2' :
//            $('#' + divname).val(lat);
//            break;
//            case 'ylongitude2' :
//            $('#' + divname).val(lon);
//            break;
//            case 'ylatitude2' :
//            $('#' + divname).val(lat);
//            break;
//            case 'wlongitude2' :
//            $('#' + divname).val(lon);
//            break;
//            case 'wlatitude2' :
//            $('#' + divname).val(lat);
//            break;
//    }
//         if(divname=='longitude2') {
//            $('#'+ divname).val(lon);
//        } 
//        else if(divname=='latitude2')
//        {    
//            $('#'+ divname).val(lat);
//        }
//        else if(divname=='ylongitude2')
//        {    
//            $('#'+ divname).val(lon);
//        }   
//        else if(divname=='ylatitude2')
//        {    
//            $('#'+ divname).val(lat);
//        }  
//        else if(divname=='wlongitude2')
//        {    
//            $('#'+ divname).val(lon);
//        }   
//        else if(divname=='wlatitude2')
//        {    
//            $('#'+ divname).val(lat);
//        }   

//    } else {
//    switch (divname) {
//    case 'longitude' :
//            $('#' + divname).val(lon);
//            break;
//            case 'latitude' :
//            $('#' + divname).val(lat);
//            break;
//            case 'ylongitude' :
//            $('#' + divname).val(lon);
//            break;
//            case 'ylatitude' :
//            $('#' + divname).val(lat);
//            break;
//            case 'wlongitude' :
//            $('#' + divname).val(lon);
//            break;
//            case 'wlatitude' :
//            $('#' + divname).val(lat);
//            break;
//    }
//        if(divname=='longitude') {
//            $('#'+ divname).val(lon);
//        } 
//        else if(divname=='latitude')
//        {    
//            $('#'+ divname).val(lat);
//        }
//        else if(divname=='ylongitude')
//        {    
//            $('#'+ divname).val(lon);
//        }   
//        else if(divname=='ylatitude')
//        {    
//            $('#'+ divname).val(lat);
//        }          
//        else if(divname=='wlongitude')
//        {    
//            $('#'+ divname).val(lon);
//        }   
//        else if(divname=='wlatitude')
//        {    
//            $('#'+ divname).val(lat);
//        }   
//    }
//
//    })
//            .fail(function () {
//            alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
//            })
//            .always(function () {
//            // alert("finished");
//            });
}


function setlonlat(divname, selvalue) {
    // url = "exercise_wp/get_lonlat/"+selvalue1+"/"+selvalue2;

    url = "bm_lrs/lrs_tools/lrs_lonlat/" + selvalue;
    
    
        $.ajax({
        type: 'GET',
        url: url,
        dataType: 'json',
        async: false,
        //data: {postVar1: 'theValue1', postVar2: 'theValue2'},
        beforeSend: function () {
            // this is where we append a loading image
            //$('#ajax-panel').html('<div class="loading"><img src="/images/loading.gif" alt="Loading..." /></div>');
        },
        success: function (data) {
            // successful request; do something with the data
            //console.log(data);
           var lonlat = data.val;
        var lon = lonlat.split("||")[0];
        var lat = lonlat.split("||")[1];


                switch (divname) {
                    case 'longitude' :
                    case 'ylongitude' :
                    case 'wlongitude' :
                        $('#' + divname).val(lon);
                        break;
                    case 'latitude' :
                    case 'ylatitude' :
                    case 'wlatitude' :
                        $('#' + divname).val(lat);
                        break;  
                }


        },
        error: function () {
            // failed request; give feedback to user
           // $('#ajax-panel').html('<p class="error"><strong>Oops!</strong> Try that again in a few moments.</p>');
        }
    });


//    $.get(url).done(function (data) {
//        //alert(data);
//
//        jdata = JSON.parse(data);
//        console.log(jdata);
////            
////            $('#'+divname).empty();
//        var lonlat = jdata.val;
//        var lon = lonlat.split("||")[0];
//        var lat = lonlat.split("||")[1];
//
//        if (divname == 'longitude') {
//            $('#' + divname).val(lon);
//        } else if (divname == 'latitude')
//        {
//            $('#' + divname).val(lat);
//        } else if (divname == 'ylongitude')
//        {
//            $('#' + divname).val(lon);
//        } else if (divname == 'ylatitude')
//        {
//            $('#' + divname).val(lat);
//        } else if (divname == 'wlongitude')
//        {
//            $('#' + divname).val(lon);
//        } else if (divname == 'wlatitude')
//        {
//            $('#' + divname).val(lat);
//        }


//            $.each(jdata, function (i, el) {
        //$('#' + divname).val(jdata.val);
//            });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
//    })
//            .fail(function () {
//                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
//            })
//            .always(function () {
//                // alert("finished");
//            });
}


function setComboVal(divname, newval) {
    if (newval != '') {
        $('#' + divname).val(newval);
    }
}


function setSelCharVal(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/setSelChar/" + refindex + "/" + refresh_field + "/" + refresh_value;
    $.get(url).done(function (data) {
        //alert(data);

        jdata = JSON.parse(data);
        console.log(jdata);
//            
//            $('#'+divname).empty();

//            $.each(jdata, function (i, el) {
        $('#' + divname).val(jdata.val);
//            });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function setInputVal(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/setinput/" + refindex + "/" + refresh_field + "/" + refresh_value;
    $.get(url).done(function (data) {
        //alert(data);

        jdata = JSON.parse(data);
        console.log(jdata);
//            
//            $('#'+divname).empty();

//            $.each(jdata, function (i, el) {
        $('#' + divname).val(jdata.val);
//            });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function setInputVal2(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/setinput2/" + refindex + "/" + refresh_field + "/" + refresh_value;
    $.get(url).done(function (data) {
        //alert(data);

        jdata = JSON.parse(data);
        console.log(jdata);
//            
//            $('#'+divname).empty();

//            $.each(jdata, function (i, el) {
        $('#' + divname).val(jdata.val);
//            });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}



function setInputVal3(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/setinput3/" + refindex + "/" + refresh_field + "/" + refresh_value;
    $.get(url).done(function (data) {
        //alert(data);

        jdata = JSON.parse(data);
        console.log(jdata);
//            
//            $('#'+divname).empty();

//            $.each(jdata, function (i, el) {
        $('#' + divname).val(jdata.val);
//            });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function setInputVal10(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/setinput4/" + refindex + "/" + refresh_field + "/" + refresh_value;
    $.get(url).done(function (data) {
        //alert(data);

        jdata = JSON.parse(data);
        console.log(jdata);
//            
//            $('#'+divname).empty();

//            $.each(jdata, function (i, el) {
        $('#' + divname).val(jdata.val);
//            });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}


//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
//selindes: selected value
function updateCombobox(divname, refindex, selvalue) {
    url = "lookup/fieldlook/" + refindex;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        if (selvalue != '')
            $('#' + divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function updateCombobox2(divname, refindex, selvalue1, selvalue2) {
    url = "lookup/fieldlook/" + refindex;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        if (selvalue2 != '')
            $('#' + divname).val(selvalue2)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function updateComboboxtree(divname, refindex, selvalue, selrvalue) {
    url = "lookup/fieldlooktree/" + refindex;
    alert(selvalue);
    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
        if (selvalue != '')
            $('#' + divname).append("<option value=" + selrvalue + " selected>" + selvalue + "</option>")
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshComboboxSTASatker(divname, selvalue, setval) {
    url2 = "bm_lrs/lrs_tools/lrs_sta_ruas/" + selvalue;

    $.get(url2).done(function (data2) {
        jdata2 = JSON.parse(data2);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));

        $.each(jdata2, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });
        // if (setval) $('#'+divname).val(setval);
        if (setval != 0) {
            $('#' + divname).val(setval)
        } else {
            $('#' + divname).val(0)
        }
        //*/

    }).fail(function () {
        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
    })
            .always(function () {
                // alert("finished");
            });

}


function initComboboxRuasSatker(divname, value) {

    url = "bm_lrs/lrs_tools/lrs_ruas/" + value;

    $.get(url).done(function (data) {
        console.log(data);

        jdata = JSON.parse(data);
        //console.log(jdata);

        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));



        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });


        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("cek kembali service ruas");
            })
            .always(function () {
                // alert("finished");
            });
}

function initComboboxJembatan(divname, selvalue) {
    url = "bm_lrs/lrs_tools/lrs_bridge/" + selvalue;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));

        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });

    }).fail(function () {
        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
    }).always(function () {
        // alert("finished");
    });
}

function refreshComboboxJembatan(divname, selvalue, setval) {
    url = "bm_lrs/lrs_tools/lrs_bridge/" + selvalue;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));

        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });

        if (setval != '')
            $('#' + divname).val(setval)

    }).fail(function () {
        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
    }).always(function () {
        // alert("finished");
    });
}


function refreshComboboxRuasSatker(divname, value, setval) {
    url = "bm_lrs/lrs_tools/lrs_ruas/" + value;

    $.get(url).done(function (data) {
        console.log(data);

        jdata = JSON.parse(data);
        //console.log(jdata);

        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));



        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });



        if (setval != '')
            $('#' + divname).val(setval)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });

    $("#ruas_loading").ajaxStart(function () {
        $(this).css("display", "block");
    });

    $("#ruas_loading").ajaxComplete(function () {
        $(this).css("display", "none");
    });
}

//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
function initCombobox(divname, refindex) {
    url = "lookup/fieldlook/" + refindex;

    $.get(url).done(function (data) {
        //alert(data);

        jdata = JSON.parse(data);
        console.log(jdata);

        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}
//reff_index= table_name
function refreshCombobox(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/refreshlook/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshComboboxSearch(divname, refindex, refresh_field, refresh_value, selvalue) {
    url = "lookup/refreshlook/" + refindex + "/" + refresh_field + "/" + refresh_value;



    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });


        $('#' + divname).addClass('selectpicker');
        $('.selectpicker').selectpicker({
            liveSearch: true,
            dropupAuto: false
        });

        if (selvalue != '') {
            var text = $('select[name=' + divname + '] option[value=' + selvalue + ']').text();
            //We need to show the text inside the span that the plugin show
            $('.selectpicker .filter-option').text(text);
            //Check the selected attribute for the real select
            $('select[name=' + divname + ']').val(selvalue);
        } else
        {
            $('.selectpicker .filter-option').text('--Pilih--');
            //Check the selected attribute for the real select
            $('select[name=' + divname + ']').val('#');
        }

        $('#' + divname).selectpicker('refresh');
        $('#' + divname).trigger('change');



    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function updateComboboxSearch(divname, refindex, selvalue) {
    url = "lookup/fieldlook/" + refindex;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        $('#' + divname).addClass('selectpicker');
        $('.selectpicker').selectpicker({
            liveSearch: true,
            dropupAuto: false
        });
        $('#' + divname).selectpicker('refresh');
        $('#' + divname).trigger('change');

        if (selvalue != '')
            $('#' + divname).val(selvalue);
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

//reff_index= table_name
function refreshComboboxString(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/refreshlookstringid/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshComboboxOutput(divname, refindex, refresh_field, refresh_value, selvalue) {
    url = "lookup/refreshlook/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        if (selvalue != '')
            $('#' + divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshComboboxOutput(divname, refindex, refresh_field, refresh_value, selvalue) {
    url = "lookup/refreshlook/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        if (selvalue != '')
            $('#' + divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshCombobox2(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/refreshlook2/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshComboboxJBT(divname, refindex, refresh_field, refresh_value, selvalue) {
    url = "lookup/refreshlook2/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });
        if (selvalue != '')
            $('#' + divname).val(selvalue)
        //if (typeof(variable) == "undefined" && variable === null) $('#'+divname).val(-1)
        // else $('#'+divname).val(selvalue);    
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}
function refreshComboboxOutput4(divname, refindex, refresh_field, refresh_value, selvalue) {
    url = "lookup/refreshlook4/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        if (selvalue != '')
            $('#' + divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}
function refreshComboboxSTA(divname, refindex, refresh_field, refresh_value, selvalue) {
    url = "lookup/refreshlook3/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        if (selvalue == 0)
            $('#' + divname).val(0)
        else
            $('#' + divname).val(selvalue);
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function initComboboxSTA(divname, refindex, refresh_value, selvalue) {
    url = "lookup/refreshlookSTA/" + refindex + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });
        
         if (selvalue == 0)
            $('#' + divname).val(0)
        else
            $('#' + divname).val(selvalue);

    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshCombobox3(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/refreshlook3/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));
        $.each(jdata, function (i, el) {

            //console.log(el.val);
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshCombobox4(divname, refindex, refresh_field, refresh_value, selvalue) {
    url = "lookup/refreshlook4/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {

            //console.log(el.val);
            $('#' + divname).append(new Option(el.val, el.id));
        });

        if (selvalue != '')
            $('#' + divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshCombobox5(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/get_ruas_from_balai/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {

            //console.log(el.val);
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}



//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
function initTreeCombobox(divname, refindex) {
    url = "lookup/fieldlooktree/" + refindex;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        console.log("---parent--");
        console.log(data);
        $('#' + divname).empty();
        $('#' + divname).prepend("<option selected='selected'>--Pilih--</option>");
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}


function multipleSelect(divname, refindex) {
    url = "lookup/fieldlook/" + refindex;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
//            console.log("---parent--");
//            console.log(data);
        //$('#'+divname).empty();
        //$('#'+divname).prepend("<li>--Pilih--</li>");
        $.each(jdata, function (i, el) {
            $('#' + divname).append("<li><a href='#" + el.id + "'>" + el.val + "</a></li>");
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}

function refreshComboboxKPPN(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/refreshlook/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ''));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });
//        $('#' + divname).append(new Option("175 - KANWIL JAKARTA XI - JAKARTA VI", 175));
        $('#' + divname).append(new Option("140 - KHUSUS PINJAMAN DAN HIBAH", 140));
        $('#' + divname).append(new Option("999 - DIREKTORAT PENGELOLAAN KAS NEGARA", 999));

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
            })
            .always(function () {
                // alert("finished");
            });
}
