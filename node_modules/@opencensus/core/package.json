{"name": "@opencensus/core", "version": "0.0.9", "description": "OpenCensus is a toolkit for collecting application performance and behavior data.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "census-instrumentation/opencensus-node", "scripts": {"test": "nyc mocha build/test/**/*.js", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json", "clean": "rimraf build/*", "check": "gts check", "compile": "tsc -p .", "compile:release": "tsc -p tsconfig-release.json", "fix": "gts fix", "prepare": "npm run compile:release", "pretest": "npm run compile", "posttest": "npm run check"}, "keywords": ["opencensus", "nodejs", "tracing", "profiling", "metrics", "stats"], "author": "Google Inc.", "license": "Apache-2.0", "engines": {"node": ">=6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.d.ts", "doc", "CHANGELOG.md", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@types/continuation-local-storage": "^3.2.1", "@types/mocha": "^5.2.5", "@types/node": "^10.12.12", "@types/once": "^1.4.0", "@types/semver": "^5.5.0", "@types/shimmer": "^1.0.1", "@types/uuid": "^3.4.3", "codecov": "^3.1.0", "gts": "^0.9.0", "intercept-stdout": "^0.1.2", "mocha": "^5.0.4", "ncp": "^2.0.0", "nyc": "13.1.0", "ts-node": "^7.0.1", "typescript": "~2.9.0"}, "dependencies": {"continuation-local-storage": "^3.2.1", "log-driver": "^1.2.7", "semver": "^5.5.0", "shimmer": "^1.2.0", "uuid": "^3.2.1"}}