{"name": "@opencensus/propagation-b3", "version": "0.0.8", "description": "Opencensus propagation package for B3 format.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "census-instrumentation/opencensus-node", "scripts": {"test": "nyc -x '**/test/**' --reporter=html --reporter=text mocha 'build/test/**/*.js'", "clean": "rimraf build/*", "check": "gts check", "compile": "tsc -p .", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "posttest": "npm run check"}, "keywords": ["opencensus", "nodejs", "tracing", "profiling"], "author": "Google Inc.", "license": "Apache-2.0", "engines": {"node": ">=6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.d.ts", "doc", "CHANGELOG.md", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@types/mocha": "^5.2.5", "@types/node": "^10.12.12", "@types/uuid": "^3.4.3", "gts": "^0.9.0", "mocha": "^5.0.4", "ncp": "^2.0.0", "nyc": "^11.7.1", "rimraf": "^2.6.2", "ts-node": "^7.0.1", "typescript": "~2.7.2"}, "dependencies": {"@opencensus/core": "^0.0.8", "uuid": "^3.2.1"}}