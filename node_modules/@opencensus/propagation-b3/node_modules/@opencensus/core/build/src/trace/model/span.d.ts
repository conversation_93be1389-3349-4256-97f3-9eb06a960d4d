import { SpanBase } from './span-base';
import * as types from './types';
/** Defines a Span. */
export declare class Span extends SpanBase implements types.Span {
    private root;
    /** set isRootSpan = true */
    readonly isRootSpan: boolean;
    /**
     * Constructs a new SpanImpl instance.
     * @param root
     */
    constructor(root: types.RootSpan);
    /** Gets trace id of span. */
    readonly traceId: string;
    readonly traceState: string;
    /** Starts the span instance. */
    start(): void;
    /** Ends the span. */
    end(): void;
}
