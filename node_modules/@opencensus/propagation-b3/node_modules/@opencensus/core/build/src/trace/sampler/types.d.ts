/**
 * Copyright 2018, OpenCensus Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/** This interface represent a sampler . */
export interface Sampler {
    /**
     * A string that uniquely describes the sampling behavior of this instance.
     */
    readonly description: string;
    /**
     * Checks if trace belong the sample.
     * @param traceId Used to check the probability.
     * @returns a boolean. True if the traceId is in probability
     * False if the traceId is not in probability.
     */
    shouldSample(traceId: string): boolean;
}
