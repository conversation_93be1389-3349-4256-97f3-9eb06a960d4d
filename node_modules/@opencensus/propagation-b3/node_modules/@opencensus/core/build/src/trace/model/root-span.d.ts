import { SpanBase } from './span-base';
import * as types from './types';
/** Defines a root span */
export declare class RootSpan extends SpanBase implements types.RootSpan {
    /** A tracer object */
    private tracer;
    /** A list of child spans. */
    private spansLocal;
    /** Its trace ID. */
    private traceIdLocal;
    /** Its trace state. */
    private traceStateLocal;
    /** set isRootSpan = true */
    readonly isRootSpan: boolean;
    /**
     * Constructs a new RootSpanImpl instance.
     * @param tracer A tracer object.
     * @param context A trace options object to build the root span.
     */
    constructor(tracer: types.Tracer, context?: types.TraceOptions);
    /** Gets span list from rootspan instance. */
    readonly spans: types.Span[];
    /** Gets trace id from rootspan instance. */
    readonly traceId: string;
    /** Gets trace state from rootspan instance */
    readonly traceState: types.TraceState;
    /** Starts a rootspan instance. */
    start(): void;
    /** Ends a rootspan instance. */
    end(): void;
    /**
     * Starts a new child span in the root span.
     * @param name Span name.
     * @param kind Span kind.
     * @param parentSpanId Span parent ID.
     */
    startChildSpan(name: string, kind: string, parentSpanId?: string): types.Span;
}
