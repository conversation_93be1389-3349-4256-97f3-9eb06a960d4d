{"name": "@pm2/agent", "version": "0.5.26", "description": "PM2.io Agent Daemon", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha test/**/*.mocha.js"}, "keywords": ["keymetrics", "agent", "daemon", "pm2"], "dependencies": {"async": "^2.6.0", "chalk": "^2.3.2", "eventemitter2": "^5.0.1", "fclone": "^1.0.11", "moment": "^2.21.0", "nssocket": "^0.6.0", "pm2-axon": "^3.2.0", "pm2-axon-rpc": "^0.5.0", "proxy-agent": "^3.1.0", "semver": "^5.5.0", "ws": "^5.1.0"}, "devDependencies": {"clone": "^2.1.1", "mocha": "^3.2", "nock": "^9.2.3", "simple-socks": "^1.0.2", "v8-coverage": "^1.0.4"}, "author": "Keymetrics Team", "license": "AGPL-3.0"}