{"name": "@pm2/agent-node", "version": "1.1.10", "description": "PM2.io Agent Standalone for NodeJS", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/keymetrics/pm2-io-agent-node.git"}, "author": "Keymetrics <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/keymetrics/pm2-io-agent-node/issues"}, "homepage": "https://github.com/keymetrics/pm2-io-agent-node#readme", "dependencies": {"debug": "^3.1.0", "eventemitter2": "^5.0.1", "proxy-agent": "^3.0.3", "ws": "^6.0.0"}, "devDependencies": {"async": "^2.6.1", "mocha": "^5.2.0", "simple-socks": "^0.3.0", "v8-coverage": "^1.0.7"}}