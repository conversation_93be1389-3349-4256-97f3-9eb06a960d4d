"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const v8 = require("v8");
const debug_1 = require("debug");
const serviceManager_1 = require("../serviceManager");
class V8MetricsConfig {
}
exports.V8MetricsConfig = V8MetricsConfig;
const defaultOptions = {
    new_space: false,
    old_space: false,
    map_space: false,
    code_space: false,
    large_object_space: false,
    heap_total_size: true,
    heap_used_size: true,
    heap_used_percent: true
};
class V8Metric {
    constructor() {
        this.TIME_INTERVAL = 800;
        this.logger = debug_1.default('axm:features:metrics:v8');
        this.metricStore = new Map();
        this.unitKB = 'MiB';
        this.metricsDefinitions = {
            total_heap_size: {
                name: 'Heap Size',
                id: 'internal/v8/heap/total',
                unit: this.unitKB,
                historic: true
            },
            heap_used_percent: {
                name: 'Heap Usage',
                id: 'internal/v8/heap/usage',
                unit: '%',
                historic: true
            },
            used_heap_size: {
                name: 'Used Heap Size',
                id: 'internal/v8/heap/used',
                unit: this.unitKB,
                historic: true
            }
        };
    }
    init(config) {
        if (config === false)
            return;
        if (config === undefined) {
            config = defaultOptions;
        }
        if (config === true) {
            config = defaultOptions;
        }
        this.metricService = serviceManager_1.ServiceManager.get('metrics');
        if (this.metricService === undefined)
            return this.logger('Failed to load metric service');
        this.logger('init');
        if (!v8.hasOwnProperty('getHeapStatistics')) {
            return this.logger(`V8.getHeapStatistics is not available, aborting`);
        }
        for (let metricName in this.metricsDefinitions) {
            if (config[metricName] === false)
                continue;
            const isEnabled = config[metricName];
            if (isEnabled === false)
                continue;
            let metric = this.metricsDefinitions[metricName];
            this.metricStore.set(metricName, this.metricService.metric(metric));
        }
        this.timer = setInterval(() => {
            const stats = v8.getHeapStatistics();
            for (let metricName in this.metricsDefinitions) {
                if (typeof stats[metricName] !== 'number')
                    continue;
                const gauge = this.metricStore.get(metricName);
                if (gauge === undefined)
                    continue;
                gauge.set(this.formatMiBytes(stats[metricName]));
            }
            const usage = (stats.used_heap_size / stats.total_heap_size * 100).toFixed(2);
            const usageMetric = this.metricStore.get('heap_used_percent');
            if (usageMetric !== undefined) {
                usageMetric.set(parseFloat(usage));
            }
        }, this.TIME_INTERVAL);
        this.timer.unref();
    }
    destroy() {
        if (this.timer !== undefined) {
            clearInterval(this.timer);
        }
        this.logger('destroy');
    }
    formatMiBytes(val) {
        return (val / 1024 / 1024).toFixed(2);
    }
}
exports.default = V8Metric;
//# sourceMappingURL=data:application/json;base64,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