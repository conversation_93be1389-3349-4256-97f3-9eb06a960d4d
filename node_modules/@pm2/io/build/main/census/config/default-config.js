"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const constants_1 = require("../constants");
exports.defaultConfig = {
    logLevel: 1,
    maximumLabelValueSize: 150,
    plugins: {},
    bufferSize: constants_1.Constants.DEFAULT_BUFFER_SIZE,
    bufferTimeout: constants_1.Constants.DEFAULT_BUFFER_TIMEOUT,
    samplingRate: 1
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGVmYXVsdC1jb25maWcuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi9zcmMvY2Vuc3VzL2NvbmZpZy9kZWZhdWx0LWNvbmZpZy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQWdCQSw0Q0FBd0M7QUFTM0IsUUFBQSxhQUFhLEdBQUc7SUFDM0IsUUFBUSxFQUFFLENBQUM7SUFDWCxxQkFBcUIsRUFBRSxHQUFHO0lBQzFCLE9BQU8sRUFBRSxFQUFFO0lBQ1gsVUFBVSxFQUFFLHFCQUFTLENBQUMsbUJBQW1CO0lBQ3pDLGFBQWEsRUFBRSxxQkFBUyxDQUFDLHNCQUFzQjtJQUMvQyxZQUFZLEVBQUUsQ0FBQztDQUNoQixDQUFBIn0=