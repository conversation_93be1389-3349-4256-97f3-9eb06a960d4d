{"name": "@pm2/io", "version": "4.3.5", "description": "PM2.io NodeJS APM", "main": "build/main/index.js", "typings": "build/main/index.d.ts", "types": "build/main/index.d.ts", "module": "build/module/index.js", "repository": "https://github.com/keymetrics/pm2-io-apm", "author": {"name": "PM2.io tech team", "email": "<EMAIL>", "url": "https://pm2.io"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/wallet77"}], "license": "Apache-2", "scripts": {"build": "tsc -p tsconfig.json", "build:module": "tsc -p config/exports/tsconfig.module.json", "lint": "tslint --project . src/**/*.ts", "unit": "npm run build && bash test.sh", "mono": "mocha  --exit --require ts-node/register", "test": "npm run unit", "watch": "tsc -w", "prepublishOnly": "npm run build"}, "scripts-info": {"build": "(Trash and re)build the library", "lint": "Lint all typescript source files", "unit": "Build the library and run unit tests", "test": "Lint, build, and test the library", "watch": "Watch source files, rebuild library on changes, rerun relevant tests"}, "engines": {"node": ">=6.0"}, "devDependencies": {"@types/chai": "4.1.4", "@types/express": "~4.16.1", "@types/ioredis": "~4.0.6", "@types/mocha": "5.2.5", "@types/mongodb": "~3.1.19", "@types/node": "~10.12.21", "@types/redis": "~2.8.10", "chai": "4.1.2", "mocha": "~7.1.0", "nock": "~10.0.6", "nyc": "~13.1.0", "source-map-support": "~0.5.9", "ts-node": "~7.0.1", "tslint": "~5.11.0", "tslint-config-standard": "~8.0.1", "typescript": "~3.2.2", "@pm2/node-runtime-stats": "^0.3.2", "express": "^4.17.1", "ioredis": "^4.16.3", "koa": "^2.11.0", "mongodb-core": "^3.2.7", "mysql": "^2.18.1", "mysql2": "^2.1.0", "pg": "^7.18.2", "redis": "^3.0.2", "vue": "^2.6.11", "vue-server-renderer": "^2.6.11"}, "keywords": [], "nyc": {"extension": [".ts"], "exclude": ["build/", "config/", "examples/", "test/"], "cache": true, "all": true}, "dependencies": {"@opencensus/core": "0.0.9", "@opencensus/propagation-b3": "0.0.8", "@pm2/agent-node": "^1.1.10", "async": "~2.6.1", "debug": "4.1.1", "eventemitter2": "^6.3.1", "require-in-the-middle": "^5.0.0", "semver": "6.3.0", "shimmer": "^1.2.0", "signal-exit": "^3.0.3", "tslib": "1.9.3"}}