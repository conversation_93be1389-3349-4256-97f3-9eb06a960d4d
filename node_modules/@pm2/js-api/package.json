{"name": "@pm2/js-api", "version": "0.5.63", "description": "PM2.io API Client for Javascript", "main": "index.js", "unpkg": "dist/keymetrics.es5.min.js", "engines": {"node": ">=4.0"}, "scripts": {"test": "mocha test/*", "build": "mkdir -p dist; browserify -s Keymetrics -r ./ > ./dist/keymetrics.es5.js", "dist": "mkdir -p dist; browserify -s Keymetrics -r ./ | uglifyjs -c warnings=false -m > ./dist/keymetrics.es5.min.js", "doc": "jsdoc -r ./src --readme ./README.md -d doc -t ./node_modules/minami", "clean": "rm dist/*", "prepublish": "npm run build && npm run dist"}, "repository": {"type": "git", "url": "git+https://github.com/keymetrics/km.js.git"}, "keywords": ["keymetrics", "api", "dashboard", "monitoring", "wrapper"], "author": "Keymetrics Team", "license": "Apache-2", "bugs": {"url": "https://github.com/keymetrics/km.js/issues"}, "homepage": "https://github.com/keymetrics/km.js#readme", "dependencies": {"async": "^2.6.3", "axios": "^0.19.0", "debug": "^2.6.8", "eventemitter2": "^4.1.0", "ws": "^3.0.0"}, "devDependencies": {"babel-core": "6.26.0", "babel-preset-es2015": "*", "babel-preset-stage-2": "6.24.1", "babelify": "8.0.0", "browserify": "^13.1.0", "jsdoc": "^3.4.2", "minami": "^1.1.1", "mocha": "^3.0.2", "pm2": "^4.1.2", "should": "*", "standard": "^10.0.2", "uglify-js": "~3.3.7"}, "browserify": {"debug": "true", "transform": [["babe<PERSON>", {"presets": [["babel-preset-es2015", {"debug": "true", "sourceMaps": "true"}]]}]]}, "browser": {"./src/auth_strategies/embed_strategy.js": false, "ws": false}, "standard": {"ignore": ["dist/**", "examples/**", "test/**"]}}