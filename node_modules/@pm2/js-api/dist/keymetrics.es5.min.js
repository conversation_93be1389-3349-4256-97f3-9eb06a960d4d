!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Keymetrics=e()}}(function(){return function o(r,s,l){function p(t,e){if(!s[t]){if(!r[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(u)return u(t,!0);var i=new Error("Cannot find module '"+t+"'");throw i.code="MODULE_NOT_FOUND",i}var a=s[t]={exports:{}};r[t][0].call(a.exports,function(e){return p(r[t][1][e]||e)},a,a.exports,o,r,s,l)}return s[t].exports}for(var u="function"==typeof require&&require,e=0;e<l.length;e++)p(l[e]);return p}({1:[function(i,a,e){(function(e){"use strict";var t=i("./package.json"),n={headers:{"X-JS-API-Version":t.version},services:{API:"https://app.keymetrics.io",OAUTH:"https://id.keymetrics.io"},OAUTH_AUTHORIZE_ENDPOINT:"/api/oauth/authorize",OAUTH_CLIENT_ID:"795984050",ENVIRONNEMENT:e&&e.versions&&e.versions.node?"node":"browser",VERSION:t.version,IS_DEBUG:"undefined"!=typeof window&&window.location.host.match(/km.(io|local)/)||void 0!==e&&"true"===e.env.DEBUG};a.exports=Object.assign({},n)}).call(this,i("_process"))},{"./package.json":36,_process:34}],2:[function(e,Kn,t){(function(Xn,Jn,$n){var e;e=this,function(e){"use strict";function y(e,t){t|=0;for(var n=Math.max(e.length-t,0),i=Array(n),a=0;a<n;a++)i[a]=e[t+a];return i}var t=function(t){var n=y(arguments,1);return function(){var e=y(arguments);return t.apply(null,n.concat(e))}},l=function(n){return function(){var e=y(arguments),t=e.pop();n.call(this,e,t)}};function a(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}var n="function"==typeof $n&&$n,i="object"==typeof Xn&&"function"==typeof Xn.nextTick;function o(e){setTimeout(e,0)}function r(n){return function(e){var t=y(arguments,1);n(function(){e.apply(null,t)})}}var f=r(n?$n:i?Xn.nextTick:o);function s(i){return l(function(e,t){var n;try{n=i.apply(this,e)}catch(e){return t(e)}a(n)&&"function"==typeof n.then?n.then(function(e){p(t,null,e)},function(e){p(t,e.message?e:new Error(e))}):t(null,n)})}function p(e,t,n){try{e(t,n)}catch(e){f(u,e)}}function u(e){throw e}var c="function"==typeof Symbol;function d(e){return c&&"AsyncFunction"===e[Symbol.toStringTag]}function h(e){return d(e)?s(e):e}function m(a){return function(t){var e=y(arguments,1),n=l(function(n,e){var i=this;return a(t,function(e,t){h(e).apply(i,n.concat(t))},e)});return e.length?n.apply(this,e):n}}var v="object"==typeof Jn&&Jn&&Jn.Object===Object&&Jn,g="object"==typeof self&&self&&self.Object===Object&&self,b=v||g||Function("return this")(),k=b.Symbol,w=Object.prototype,_=w.hasOwnProperty,T=w.toString,A=k?k.toStringTag:void 0;var j=Object.prototype.toString;var E="[object Null]",S="[object Undefined]",x=k?k.toStringTag:void 0;function O(e){return null==e?void 0===e?S:E:x&&x in Object(e)?function(e){var t=_.call(e,A),n=e[A];try{var i=!(e[A]=void 0)}catch(e){}var a=T.call(e);return i&&(t?e[A]=n:delete e[A]),a}(e):(t=e,j.call(t));var t}var P="[object AsyncFunction]",z="[object Function]",L="[object GeneratorFunction]",B="[object Proxy]";var C=9007199254740991;function D(e){return"number"==typeof e&&-1<e&&e%1==0&&e<=C}function U(e){return null!=e&&D(e.length)&&!function(e){if(!a(e))return!1;var t=O(e);return t==z||t==L||t==P||t==B}(e)}var R={};function I(){}function N(t){return function(){if(null!==t){var e=t;t=null,e.apply(this,arguments)}}}var q="function"==typeof Symbol&&Symbol.iterator,M=function(e){return q&&e[q]&&e[q]()};function G(e){return null!=e&&"object"==typeof e}function F(e){return G(e)&&"[object Arguments]"==O(e)}var H=Object.prototype,W=H.hasOwnProperty,V=H.propertyIsEnumerable,Q=F(function(){return arguments}())?F:function(e){return G(e)&&W.call(e,"callee")&&!V.call(e,"callee")},X=Array.isArray;var J="object"==typeof e&&e&&!e.nodeType&&e,$=J&&"object"==typeof Kn&&Kn&&!Kn.nodeType&&Kn,K=$&&$.exports===J?b.Buffer:void 0,Y=(K?K.isBuffer:void 0)||function(){return!1},Z=9007199254740991,ee=/^(?:0|[1-9]\d*)$/;var te={};te["[object Float32Array]"]=te["[object Float64Array]"]=te["[object Int8Array]"]=te["[object Int16Array]"]=te["[object Int32Array]"]=te["[object Uint8Array]"]=te["[object Uint8ClampedArray]"]=te["[object Uint16Array]"]=te["[object Uint32Array]"]=!0,te["[object Arguments]"]=te["[object Array]"]=te["[object ArrayBuffer]"]=te["[object Boolean]"]=te["[object DataView]"]=te["[object Date]"]=te["[object Error]"]=te["[object Function]"]=te["[object Map]"]=te["[object Number]"]=te["[object Object]"]=te["[object RegExp]"]=te["[object Set]"]=te["[object String]"]=te["[object WeakMap]"]=!1;var ne,ie="object"==typeof e&&e&&!e.nodeType&&e,ae=ie&&"object"==typeof Kn&&Kn&&!Kn.nodeType&&Kn,oe=ae&&ae.exports===ie&&v.process,re=function(){try{var e=ae&&ae.require&&ae.require("util").types;return e||oe&&oe.binding&&oe.binding("util")}catch(e){}}(),se=re&&re.isTypedArray,le=se?(ne=se,function(e){return ne(e)}):function(e){return G(e)&&D(e.length)&&!!te[O(e)]},pe=Object.prototype.hasOwnProperty;function ue(e,t){var n,i,a,o=X(e),r=!o&&Q(e),s=!o&&!r&&Y(e),l=!o&&!r&&!s&&le(e),p=o||r||s||l,u=p?function(e,t){for(var n=-1,i=Array(e);++n<e;)i[n]=t(n);return i}(e.length,String):[],c=u.length;for(var d in e)!t&&!pe.call(e,d)||p&&("length"==d||s&&("offset"==d||"parent"==d)||l&&("buffer"==d||"byteLength"==d||"byteOffset"==d)||(void 0,a=typeof(n=d),(i=null==(i=c)?Z:i)&&("number"==a||"symbol"!=a&&ee.test(n))&&-1<n&&n%1==0&&n<i))||u.push(d);return u}var ce=Object.prototype;var de,fe,me=(de=Object.keys,fe=Object,function(e){return de(fe(e))}),ye=Object.prototype.hasOwnProperty;function he(e){if(n=(t=e)&&t.constructor,t!==("function"==typeof n&&n.prototype||ce))return me(e);var t,n,i=[];for(var a in Object(e))ye.call(e,a)&&"constructor"!=a&&i.push(a);return i}function ve(e){return U(e)?ue(e):he(e)}function ge(e){if(U(e))return n=-1,i=(t=e).length,function(){return++n<i?{value:t[n],key:n}:null};var t,n,i,a,o,r,s,l,p,u=M(e);return u?(l=u,p=-1,function(){var e=l.next();return e.done?null:(p++,{value:e.value,key:p})}):(o=ve(a=e),r=-1,s=o.length,function(){var e=o[++r];return r<s?{value:a[e],key:e}:null})}function be(t){return function(){if(null===t)throw new Error("Callback was already called.");var e=t;t=null,e.apply(this,arguments)}}function ke(p){return function(e,t,n){if(n=N(n||I),p<=0||!e)return n(null);var i=ge(e),a=!1,o=0,r=!1;function s(e,t){if(o-=1,e)a=!0,n(e);else{if(t===R||a&&o<=0)return a=!0,n(null);r||l()}}function l(){for(r=!0;o<p&&!a;){var e=i();if(null===e)return a=!0,void(o<=0&&n(null));o+=1,t(e.value,e.key,be(s))}r=!1}l()}}function we(e,t,n,i){ke(t)(e,h(n),i)}function _e(i,a){return function(e,t,n){return i(e,a,t,n)}}function Te(e,t,n){n=N(n||I);var i=0,a=0,o=e.length;function r(e,t){e?n(e):++a!==o&&t!==R||n(null)}for(0===o&&n(null);i<o;i++)t(e[i],i,be(r))}var Ae=_e(we,1/0),je=function(e,t,n){(U(e)?Te:Ae)(e,h(t),n)};function Ee(i){return function(e,t,n){return i(je,e,h(t),n)}}function Se(e,t,n,i){i=i||I,t=t||[];var a=[],o=0,r=h(n);e(t,function(e,t,n){var i=o++;r(e,function(e,t){a[i]=t,n(e)})},function(e){i(e,a)})}var xe=Ee(Se),Oe=m(xe);function Pe(a){return function(e,t,n,i){return a(ke(t),e,h(n),i)}}var ze=Pe(Se),Le=_e(ze,1),Be=m(Le);function Ce(e,t){for(var n=-1,i=null==e?0:e.length;++n<i&&!1!==t(e[n],n,e););return e}var De,Ue=function(e,t,n){for(var i=-1,a=Object(e),o=n(e),r=o.length;r--;){var s=o[De?r:++i];if(!1===t(a[s],s,a))break}return e};function Re(e,t){return e&&Ue(e,t,ve)}function Ie(e){return e!=e}function Ne(e,t,n){return t==t?function(e,t,n){for(var i=n-1,a=e.length;++i<a;)if(e[i]===t)return i;return-1}(e,t,n):function(e,t,n,i){for(var a=e.length,o=n+(i?1:-1);i?o--:++o<a;)if(t(e[o],o,e))return o;return-1}(e,Ie,n)}var qe=function(o,e,a){"function"==typeof e&&(a=e,e=null),a=N(a||I);var n=ve(o).length;if(!n)return a(null);e||(e=n);var r={},s=0,l=!1,p=Object.create(null),i=[],u=[],c={};function d(e,t){i.push(function(){!function(i,e){if(l)return;var t=be(function(e,t){if(s--,2<arguments.length&&(t=y(arguments,1)),e){var n={};Re(r,function(e,t){n[t]=e}),n[i]=t,l=!0,p=Object.create(null),a(e,n)}else r[i]=t,Ce(p[i]||[],function(e){e()}),f()});s++;var n=h(e[e.length-1]);1<e.length?n(r,t):n(t)}(e,t)})}function f(){if(0===i.length&&0===s)return a(null,r);for(;i.length&&s<e;){i.shift()()}}function m(n){var i=[];return Re(o,function(e,t){X(e)&&0<=Ne(e,n,0)&&i.push(t)}),i}Re(o,function(t,n){if(!X(t))return d(n,[t]),void u.push(n);var i=t.slice(0,t.length-1),a=i.length;if(0===a)return d(n,t),void u.push(n);c[n]=a,Ce(i,function(e){if(!o[e])throw new Error("async.auto task `"+n+"` has a non-existent dependency `"+e+"` in "+i.join(", "));!function(e,t){var n=p[e];n||(n=p[e]=[]);n.push(t)}(e,function(){0===--a&&d(n,t)})})}),function(){var e,t=0;for(;u.length;)e=u.pop(),t++,Ce(m(e),function(e){0==--c[e]&&u.push(e)});if(t!==n)throw new Error("async.auto cannot execute tasks due to a recursive dependency")}(),f()};function Me(e,t){for(var n=-1,i=null==e?0:e.length,a=Array(i);++n<i;)a[n]=t(e[n],n,e);return a}var Ge="[object Symbol]";var Fe=1/0,He=k?k.prototype:void 0,We=He?He.toString:void 0;function Ve(e){if("string"==typeof e)return e;if(X(e))return Me(e,Ve)+"";if("symbol"==typeof(t=e)||G(t)&&O(t)==Ge)return We?We.call(e):"";var t,n=e+"";return"0"==n&&1/e==-Fe?"-0":n}function Qe(e,t,n){var i=e.length;return n=void 0===n?i:n,!t&&i<=n?e:function(e,t,n){var i=-1,a=e.length;t<0&&(t=a<-t?0:a+t),(n=a<n?a:n)<0&&(n+=a),a=n<t?0:n-t>>>0,t>>>=0;for(var o=Array(a);++i<a;)o[i]=e[i+t];return o}(e,t,n)}var Xe=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");var Je="\\ud800-\\udfff",$e="["+Je+"]",Ke="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Ye="\\ud83c[\\udffb-\\udfff]",Ze="[^"+Je+"]",et="(?:\\ud83c[\\udde6-\\uddff]){2}",tt="[\\ud800-\\udbff][\\udc00-\\udfff]",nt="(?:"+Ke+"|"+Ye+")"+"?",it="[\\ufe0e\\ufe0f]?",at=it+nt+("(?:\\u200d(?:"+[Ze,et,tt].join("|")+")"+it+nt+")*"),ot="(?:"+[Ze+Ke+"?",Ke,et,tt,$e].join("|")+")",rt=RegExp(Ye+"(?="+Ye+")|"+ot+at,"g");function st(e){return t=e,Xe.test(t)?e.match(rt)||[]:e.split("");var t}var lt=/^\s+|\s+$/g;function pt(e,t,n){var i;if((e=null==(i=e)?"":Ve(i))&&(n||void 0===t))return e.replace(lt,"");if(!e||!(t=Ve(t)))return e;var a=st(e),o=st(t);return Qe(a,function(e,t){for(var n=-1,i=e.length;++n<i&&-1<Ne(t,e[n],0););return n}(a,o),function(e,t){for(var n=e.length;n--&&-1<Ne(t,e[n],0););return n}(a,o)+1).join("")}var ut=/^(?:async\s+)?(function)?\s*[^\(]*\(\s*([^\)]*)\)/m,ct=/,/,dt=/(=.+)?(\s*)$/,ft=/((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm;function mt(e,t){var s={};Re(e,function(i,e){var a,t,n=d(i),o=!n&&1===i.length||n&&0===i.length;if(X(i))a=i.slice(0,-1),i=i[i.length-1],s[e]=a.concat(0<a.length?r:i);else if(o)s[e]=i;else{if(a=t=(t=(t=(t=(t=i).toString().replace(ft,"")).match(ut)[2].replace(" ",""))?t.split(ct):[]).map(function(e){return pt(e.replace(dt,""))}),0===i.length&&!n&&0===a.length)throw new Error("autoInject task functions require explicit parameters.");n||a.pop(),s[e]=a.concat(r)}function r(t,e){var n=Me(a,function(e){return t[e]});n.push(e),h(i).apply(null,n)}}),qe(s,t)}function yt(){this.head=this.tail=null,this.length=0}function ht(e,t){e.length=1,e.head=e.tail=t}function vt(e,t,n){if(null==t)t=1;else if(0===t)throw new Error("Concurrency must not be zero");var r=h(e),s=0,l=[],p=!1;function i(e,t,n){if(null!=n&&"function"!=typeof n)throw new Error("task callback must be a function");if(d.started=!0,X(e)||(e=[e]),0===e.length&&d.idle())return f(function(){d.drain()});for(var i=0,a=e.length;i<a;i++){var o={data:e[i],callback:n||I};t?d._tasks.unshift(o):d._tasks.push(o)}p||(p=!0,f(function(){p=!1,d.process()}))}function u(o){return function(e){s-=1;for(var t=0,n=o.length;t<n;t++){var i=o[t],a=Ne(l,i,0);0===a?l.shift():0<a&&l.splice(a,1),i.callback.apply(i,arguments),null!=e&&d.error(e,i.data)}s<=d.concurrency-d.buffer&&d.unsaturated(),d.idle()&&d.drain(),d.process()}}var c=!1,d={_tasks:new yt,concurrency:t,payload:n,saturated:I,unsaturated:I,buffer:t/4,empty:I,drain:I,error:I,started:!1,paused:!1,push:function(e,t){i(e,!1,t)},kill:function(){d.drain=I,d._tasks.empty()},unshift:function(e,t){i(e,!0,t)},remove:function(e){d._tasks.remove(e)},process:function(){if(!c){for(c=!0;!d.paused&&s<d.concurrency&&d._tasks.length;){var e=[],t=[],n=d._tasks.length;d.payload&&(n=Math.min(n,d.payload));for(var i=0;i<n;i++){var a=d._tasks.shift();e.push(a),l.push(a),t.push(a.data)}s+=1,0===d._tasks.length&&d.empty(),s===d.concurrency&&d.saturated();var o=be(u(e));r(t,o)}c=!1}},length:function(){return d._tasks.length},running:function(){return s},workersList:function(){return l},idle:function(){return d._tasks.length+s===0},pause:function(){d.paused=!0},resume:function(){!1!==d.paused&&(d.paused=!1,f(d.process))}};return d}function gt(e,t){return vt(e,1,t)}yt.prototype.removeLink=function(e){return e.prev?e.prev.next=e.next:this.head=e.next,e.next?e.next.prev=e.prev:this.tail=e.prev,e.prev=e.next=null,this.length-=1,e},yt.prototype.empty=function(){for(;this.head;)this.shift();return this},yt.prototype.insertAfter=function(e,t){t.prev=e,t.next=e.next,e.next?e.next.prev=t:this.tail=t,e.next=t,this.length+=1},yt.prototype.insertBefore=function(e,t){t.prev=e.prev,(t.next=e).prev?e.prev.next=t:this.head=t,e.prev=t,this.length+=1},yt.prototype.unshift=function(e){this.head?this.insertBefore(this.head,e):ht(this,e)},yt.prototype.push=function(e){this.tail?this.insertAfter(this.tail,e):ht(this,e)},yt.prototype.shift=function(){return this.head&&this.removeLink(this.head)},yt.prototype.pop=function(){return this.tail&&this.removeLink(this.tail)},yt.prototype.toArray=function(){for(var e=Array(this.length),t=this.head,n=0;n<this.length;n++)e[n]=t.data,t=t.next;return e},yt.prototype.remove=function(e){for(var t=this.head;t;){var n=t.next;e(t)&&this.removeLink(t),t=n}return this};var bt=_e(we,1);function kt(e,i,t,n){n=N(n||I);var a=h(t);bt(e,function(e,t,n){a(i,e,function(e,t){i=t,n(e)})},function(e){n(e,i)})}function wt(){var t=Me(arguments,h);return function(){var e=y(arguments),i=this,n=e[e.length-1];"function"==typeof n?e.pop():n=I,kt(t,e,function(e,t,n){t.apply(i,e.concat(function(e){var t=y(arguments,1);n(e,t)}))},function(e,t){n.apply(i,[e].concat(t))})}}var _t=function(){return wt.apply(null,y(arguments).reverse())},Tt=Array.prototype.concat,At=function(e,t,n,a){a=a||I;var i=h(n);ze(e,t,function(e,t){i(e,function(e){return e?t(e):t(null,y(arguments,1))})},function(e,t){for(var n=[],i=0;i<t.length;i++)t[i]&&(n=Tt.apply(n,t[i]));return a(e,n)})},jt=_e(At,1/0),Et=_e(At,1),St=function(){var e=y(arguments),t=[null].concat(e);return function(){return arguments[arguments.length-1].apply(this,t)}};function xt(e){return e}function Ot(s,l){return function(e,t,a,n){n=n||I;var o,r=!1;e(t,function(n,e,i){a(n,function(e,t){e?i(e):s(t)&&!o?(o=l(r=!0,n),i(null,R)):i()})},function(e){e?n(e):n(null,r?o:l(!1))})}}function Pt(e,t){return t}var zt=Ee(Ot(xt,Pt)),Lt=Pe(Ot(xt,Pt)),Bt=_e(Lt,1);function Ct(n){return function(e){var t=y(arguments,1);t.push(function(e){var t=y(arguments,1);"object"==typeof console&&(e?console.error&&console.error(e):console[n]&&Ce(t,function(e){console[n](e)}))}),h(e).apply(null,t)}}var Dt=Ct("dir");function Ut(e,t,n){n=be(n||I);var i=h(e),a=h(t);function o(e){if(e)return n(e);var t=y(arguments,1);t.push(r),a.apply(this,t)}function r(e,t){return e?n(e):t?void i(o):n(null)}r(null,!0)}function Rt(e,n,i){i=be(i||I);var a=h(e),o=function(e){if(e)return i(e);var t=y(arguments,1);if(n.apply(this,t))return a(o);i.apply(null,[null].concat(t))};a(o)}function It(e,t,n){Rt(e,function(){return!t.apply(this,arguments)},n)}function Nt(e,t,n){n=be(n||I);var i=h(t),a=h(e);function o(e){if(e)return n(e);a(r)}function r(e,t){return e?n(e):t?void i(o):n(null)}a(r)}function qt(i){return function(e,t,n){return i(e,n)}}function Mt(e,t,n){je(e,qt(h(t)),n)}function Gt(e,t,n,i){ke(t)(e,qt(h(n)),i)}var Ft=_e(Gt,1);function Ht(i){return d(i)?i:l(function(e,t){var n=!0;e.push(function(){var e=arguments;n?f(function(){t.apply(null,e)}):t.apply(null,e)}),i.apply(this,e),n=!1})}function Wt(e){return!e}var Vt=Ee(Ot(Wt,Wt)),Qt=Pe(Ot(Wt,Wt)),Xt=_e(Qt,1);function Jt(t){return function(e){return null==e?void 0:e[t]}}function $t(e,i,t,a){var o=new Array(i.length);e(i,function(e,n,i){t(e,function(e,t){o[n]=!!t,i(e)})},function(e){if(e)return a(e);for(var t=[],n=0;n<i.length;n++)o[n]&&t.push(i[n]);a(null,t)})}function Kt(e,t,o,n){var r=[];e(t,function(n,i,a){o(n,function(e,t){e?a(e):(t&&r.push({index:i,value:n}),a())})},function(e){e?n(e):n(null,Me(r.sort(function(e,t){return e.index-t.index}),Jt("value")))})}function Yt(e,t,n,i){(U(t)?$t:Kt)(e,t,h(n),i||I)}var Zt=Ee(Yt),en=Pe(Yt),tn=_e(en,1);function nn(e,t){var n=be(t||I),i=h(Ht(e));!function e(t){if(t)return n(t);i(e)}()}var an=function(e,t,n,s){s=s||I;var a=h(n);ze(e,t,function(n,i){a(n,function(e,t){return e?i(e):i(null,{key:t,val:n})})},function(e,t){for(var n={},i=Object.prototype.hasOwnProperty,a=0;a<t.length;a++)if(t[a]){var o=t[a].key,r=t[a].val;i.call(n,o)?n[o].push(r):n[o]=[r]}return s(e,n)})},on=_e(an,1/0),rn=_e(an,1),sn=Ct("log");function ln(e,t,n,i){i=N(i||I);var a={},o=h(n);we(e,t,function(e,n,i){o(e,n,function(e,t){if(e)return i(e);a[n]=t,i()})},function(e){i(e,a)})}var pn=_e(ln,1/0),un=_e(ln,1);function cn(e,t){return t in e}function dn(e,n){var o=Object.create(null),r=Object.create(null);n=n||xt;var i=h(e),t=l(function(e,t){var a=n.apply(null,e);cn(o,a)?f(function(){t.apply(null,o[a])}):cn(r,a)?r[a].push(t):(r[a]=[t],i.apply(null,e.concat(function(){var e=y(arguments);o[a]=e;var t=r[a];delete r[a];for(var n=0,i=t.length;n<i;n++)t[n].apply(null,e)})))});return t.memo=o,t.unmemoized=e,t}var fn=r(i?Xn.nextTick:n?$n:o);function mn(e,t,n){n=n||I;var a=U(t)?[]:{};e(t,function(e,n,i){h(e)(function(e,t){2<arguments.length&&(t=y(arguments,1)),a[n]=t,i(e)})},function(e){n(e,a)})}function yn(e,t){mn(je,e,t)}function hn(e,t,n){mn(ke(t),e,n)}var vn=function(e,t){var n=h(e);return vt(function(e,t){n(e[0],t)},t,1)},gn=function(e,t){var s=vn(e,t);return s.push=function(e,t,n){if(null==n&&(n=I),"function"!=typeof n)throw new Error("task callback must be a function");if(s.started=!0,X(e)||(e=[e]),0===e.length)return f(function(){s.drain()});t=t||0;for(var i=s._tasks.head;i&&t>=i.priority;)i=i.next;for(var a=0,o=e.length;a<o;a++){var r={data:e[a],priority:t,callback:n};i?s._tasks.insertBefore(i,r):s._tasks.push(r)}f(s.process)},delete s.unshift,s};function bn(e,t){if(t=N(t||I),!X(e))return t(new TypeError("First argument to race must be an array of functions"));if(!e.length)return t();for(var n=0,i=e.length;n<i;n++)h(e[n])(t)}function kn(e,t,n,i){kt(y(e).reverse(),t,n,i)}function wn(e){var t=h(e);return l(function(e,i){return e.push(function(e,t){var n;e?i(null,{error:e}):(n=arguments.length<=2?t:y(arguments,1),i(null,{value:n}))}),t.apply(this,e)})}function _n(e){var n;return X(e)?n=Me(e,wn):(n={},Re(e,function(e,t){n[t]=wn.call(this,e)})),n}function Tn(e,t,i,n){Yt(e,t,function(e,n){i(e,function(e,t){n(e,!t)})},n)}var An=Ee(Tn),jn=Pe(Tn),En=_e(jn,1);function Sn(e){return function(){return e}}function xn(e,t,n){var i={times:5,intervalFunc:Sn(0)};if(arguments.length<3&&"function"==typeof e?(n=t||I,t=e):(!function(e,t){if("object"==typeof t)e.times=+t.times||5,e.intervalFunc="function"==typeof t.interval?t.interval:Sn(+t.interval||0),e.errorFilter=t.errorFilter;else{if("number"!=typeof t&&"string"!=typeof t)throw new Error("Invalid arguments for async.retry");e.times=+t||5}}(i,e),n=n||I),"function"!=typeof t)throw new Error("Invalid arguments for async.retry");var a=h(t),o=1;!function t(){a(function(e){e&&o++<i.times&&("function"!=typeof i.errorFilter||i.errorFilter(e))?setTimeout(t,i.intervalFunc(o)):n.apply(null,arguments)})}()}var On=function(i,e){e||(e=i,i=null);var a=h(e);return l(function(t,e){function n(e){a.apply(null,t.concat(e))}i?xn(i,n,e):xn(n,e)})};function Pn(e,t){mn(bt,e,t)}var zn=Ee(Ot(Boolean,xt)),Ln=Pe(Ot(Boolean,xt)),Bn=_e(Ln,1);function Cn(e,t,n){var a=h(t);function i(e,t){var n=e.criteria,i=t.criteria;return n<i?-1:i<n?1:0}xe(e,function(n,i){a(n,function(e,t){if(e)return i(e);i(null,{value:n,criteria:t})})},function(e,t){if(e)return n(e);n(null,Me(t.sort(i),Jt("value")))})}function Dn(a,o,r){var s=h(a);return l(function(e,n){var t,i=!1;e.push(function(){i||(n.apply(null,arguments),clearTimeout(t))}),t=setTimeout(function(){var e=a.name||"anonymous",t=new Error('Callback function "'+e+'" timed out.');t.code="ETIMEDOUT",r&&(t.info=r),i=!0,n(t)},o),s.apply(null,e)})}var Un=Math.ceil,Rn=Math.max;function In(e,t,n,i){var a=h(n);ze(function(e,t,n,i){for(var a=-1,o=Rn(Un((t-e)/(n||1)),0),r=Array(o);o--;)r[i?o:++a]=e,e+=n;return r}(0,e,1),t,a,i)}var Nn=_e(In,1/0),qn=_e(In,1);function Mn(e,i,t,n){arguments.length<=3&&(n=t,t=i,i=X(e)?[]:{}),n=N(n||I);var a=h(t);je(e,function(e,t,n){a(i,e,t,n)},function(e){n(e,i)})}function Gn(e,t){var i,a=null;t=t||I,Ft(e,function(e,n){h(e)(function(e,t){i=2<arguments.length?y(arguments,1):t,n(!(a=e))})},function(){t(a,i)})}function Fn(e){return function(){return(e.unmemoized||e).apply(null,arguments)}}function Hn(n,e,i){i=be(i||I);var a=h(e);if(!n())return i(null);var o=function(e){if(e)return i(e);if(n())return a(o);var t=y(arguments,1);i.apply(null,[null].concat(t))};a(o)}function Wn(e,t,n){Hn(function(){return!e.apply(this,arguments)},t,n)}var Vn=function(n,t){if(t=N(t||I),!X(n))return t(new Error("First argument to waterfall must be an array of functions"));if(!n.length)return t();var i=0;function a(e){var t=h(n[i++]);e.push(be(o)),t.apply(null,e)}function o(e){if(e||i===n.length)return t.apply(null,arguments);a(y(arguments,1))}a([])},Qn={apply:t,applyEach:Oe,applyEachSeries:Be,asyncify:s,auto:qe,autoInject:mt,cargo:gt,compose:_t,concat:jt,concatLimit:At,concatSeries:Et,constant:St,detect:zt,detectLimit:Lt,detectSeries:Bt,dir:Dt,doDuring:Ut,doUntil:It,doWhilst:Rt,during:Nt,each:Mt,eachLimit:Gt,eachOf:je,eachOfLimit:we,eachOfSeries:bt,eachSeries:Ft,ensureAsync:Ht,every:Vt,everyLimit:Qt,everySeries:Xt,filter:Zt,filterLimit:en,filterSeries:tn,forever:nn,groupBy:on,groupByLimit:an,groupBySeries:rn,log:sn,map:xe,mapLimit:ze,mapSeries:Le,mapValues:pn,mapValuesLimit:ln,mapValuesSeries:un,memoize:dn,nextTick:fn,parallel:yn,parallelLimit:hn,priorityQueue:gn,queue:vn,race:bn,reduce:kt,reduceRight:kn,reflect:wn,reflectAll:_n,reject:An,rejectLimit:jn,rejectSeries:En,retry:xn,retryable:On,seq:wt,series:Pn,setImmediate:f,some:zn,someLimit:Ln,someSeries:Bn,sortBy:Cn,timeout:Dn,times:Nn,timesLimit:In,timesSeries:qn,transform:Mn,tryEach:Gn,unmemoize:Fn,until:Wn,waterfall:Vn,whilst:Hn,all:Vt,allLimit:Qt,allSeries:Xt,any:zn,anyLimit:Ln,anySeries:Bn,find:zt,findLimit:Lt,findSeries:Bt,forEach:Mt,forEachSeries:Ft,forEachLimit:Gt,forEachOf:je,forEachOfSeries:bt,forEachOfLimit:we,inject:kt,foldl:kt,foldr:kn,select:Zt,selectLimit:en,selectSeries:tn,wrapSync:s};e.default=Qn,e.apply=t,e.applyEach=Oe,e.applyEachSeries=Be,e.asyncify=s,e.auto=qe,e.autoInject=mt,e.cargo=gt,e.compose=_t,e.concat=jt,e.concatLimit=At,e.concatSeries=Et,e.constant=St,e.detect=zt,e.detectLimit=Lt,e.detectSeries=Bt,e.dir=Dt,e.doDuring=Ut,e.doUntil=It,e.doWhilst=Rt,e.during=Nt,e.each=Mt,e.eachLimit=Gt,e.eachOf=je,e.eachOfLimit=we,e.eachOfSeries=bt,e.eachSeries=Ft,e.ensureAsync=Ht,e.every=Vt,e.everyLimit=Qt,e.everySeries=Xt,e.filter=Zt,e.filterLimit=en,e.filterSeries=tn,e.forever=nn,e.groupBy=on,e.groupByLimit=an,e.groupBySeries=rn,e.log=sn,e.map=xe,e.mapLimit=ze,e.mapSeries=Le,e.mapValues=pn,e.mapValuesLimit=ln,e.mapValuesSeries=un,e.memoize=dn,e.nextTick=fn,e.parallel=yn,e.parallelLimit=hn,e.priorityQueue=gn,e.queue=vn,e.race=bn,e.reduce=kt,e.reduceRight=kn,e.reflect=wn,e.reflectAll=_n,e.reject=An,e.rejectLimit=jn,e.rejectSeries=En,e.retry=xn,e.retryable=On,e.seq=wt,e.series=Pn,e.setImmediate=f,e.some=zn,e.someLimit=Ln,e.someSeries=Bn,e.sortBy=Cn,e.timeout=Dn,e.times=Nn,e.timesLimit=In,e.timesSeries=qn,e.transform=Mn,e.tryEach=Gn,e.unmemoize=Fn,e.until=Wn,e.waterfall=Vn,e.whilst=Hn,e.all=Vt,e.allLimit=Qt,e.allSeries=Xt,e.any=zn,e.anyLimit=Ln,e.anySeries=Bn,e.find=zt,e.findLimit=Lt,e.findSeries=Bt,e.forEach=Mt,e.forEachSeries=Ft,e.forEachLimit=Gt,e.forEachOf=je,e.forEachOfSeries=bt,e.forEachOfLimit=we,e.inject=kt,e.foldl=kt,e.foldr=kn,e.select=Zt,e.selectLimit=en,e.selectSeries=tn,e.wrapSync=s,Object.defineProperty(e,"__esModule",{value:!0})}("object"==typeof t&&void 0!==Kn?t:e.async=e.async||{})}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},e("timers").setImmediate)},{_process:34,timers:35}],3:[function(e,t,n){t.exports=e("./lib/axios")},{"./lib/axios":5}],4:[function(u,e,t){"use strict";var c=u("./../utils"),d=u("./../core/settle"),f=u("./../helpers/buildURL"),m=u("./../helpers/parseHeaders"),y=u("./../helpers/isURLSameOrigin"),h=u("../core/createError");e.exports=function(p){return new Promise(function(n,i){var a=p.data,o=p.headers;c.isFormData(a)&&delete o["Content-Type"];var r=new XMLHttpRequest;if(p.auth){var e=p.auth.username||"",t=p.auth.password||"";o.Authorization="Basic "+btoa(e+":"+t)}if(r.open(p.method.toUpperCase(),f(p.url,p.params,p.paramsSerializer),!0),r.timeout=p.timeout,r.onreadystatechange=function(){if(r&&4===r.readyState&&(0!==r.status||r.responseURL&&0===r.responseURL.indexOf("file:"))){var e="getAllResponseHeaders"in r?m(r.getAllResponseHeaders()):null,t={data:p.responseType&&"text"!==p.responseType?r.response:r.responseText,status:r.status,statusText:r.statusText,headers:e,config:p,request:r};d(n,i,t),r=null}},r.onabort=function(){r&&(i(h("Request aborted",p,"ECONNABORTED",r)),r=null)},r.onerror=function(){i(h("Network Error",p,null,r)),r=null},r.ontimeout=function(){i(h("timeout of "+p.timeout+"ms exceeded",p,"ECONNABORTED",r)),r=null},c.isStandardBrowserEnv()){var s=u("./../helpers/cookies"),l=(p.withCredentials||y(p.url))&&p.xsrfCookieName?s.read(p.xsrfCookieName):void 0;l&&(o[p.xsrfHeaderName]=l)}if("setRequestHeader"in r&&c.forEach(o,function(e,t){void 0===a&&"content-type"===t.toLowerCase()?delete o[t]:r.setRequestHeader(t,e)}),p.withCredentials&&(r.withCredentials=!0),p.responseType)try{r.responseType=p.responseType}catch(e){if("json"!==p.responseType)throw e}"function"==typeof p.onDownloadProgress&&r.addEventListener("progress",p.onDownloadProgress),"function"==typeof p.onUploadProgress&&r.upload&&r.upload.addEventListener("progress",p.onUploadProgress),p.cancelToken&&p.cancelToken.promise.then(function(e){r&&(r.abort(),i(e),r=null)}),void 0===a&&(a=null),r.send(a)})}},{"../core/createError":11,"./../core/settle":15,"./../helpers/buildURL":19,"./../helpers/cookies":21,"./../helpers/isURLSameOrigin":23,"./../helpers/parseHeaders":25,"./../utils":27}],5:[function(e,t,n){"use strict";var i=e("./utils"),a=e("./helpers/bind"),o=e("./core/Axios"),r=e("./core/mergeConfig");function s(e){var t=new o(e),n=a(o.prototype.request,t);return i.extend(n,o.prototype,t),i.extend(n,t),n}var l=s(e("./defaults"));l.Axios=o,l.create=function(e){return s(r(l.defaults,e))},l.Cancel=e("./cancel/Cancel"),l.CancelToken=e("./cancel/CancelToken"),l.isCancel=e("./cancel/isCancel"),l.all=function(e){return Promise.all(e)},l.spread=e("./helpers/spread"),t.exports=l,t.exports.default=l},{"./cancel/Cancel":6,"./cancel/CancelToken":7,"./cancel/isCancel":8,"./core/Axios":9,"./core/mergeConfig":14,"./defaults":17,"./helpers/bind":18,"./helpers/spread":26,"./utils":27}],6:[function(e,t,n){"use strict";function i(e){this.message=e}i.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},i.prototype.__CANCEL__=!0,t.exports=i},{}],7:[function(e,t,n){"use strict";var i=e("./Cancel");function a(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new i(e),t(n.reason))})}a.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},a.source=function(){var t;return{token:new a(function(e){t=e}),cancel:t}},t.exports=a},{"./Cancel":6}],8:[function(e,t,n){"use strict";t.exports=function(e){return!(!e||!e.__CANCEL__)}},{}],9:[function(e,t,n){"use strict";var a=e("./../utils"),i=e("../helpers/buildURL"),o=e("./InterceptorManager"),r=e("./dispatchRequest"),s=e("./mergeConfig");function l(e){this.defaults=e,this.interceptors={request:new o,response:new o}}l.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=e:e=e||{},(e=s(this.defaults,e)).method=e.method?e.method.toLowerCase():"get";var t=[r,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},l.prototype.getUri=function(e){return e=s(this.defaults,e),i(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},a.forEach(["delete","get","head","options"],function(n){l.prototype[n]=function(e,t){return this.request(a.merge(t||{},{method:n,url:e}))}}),a.forEach(["post","put","patch"],function(i){l.prototype[i]=function(e,t,n){return this.request(a.merge(n||{},{method:i,url:e,data:t}))}}),t.exports=l},{"../helpers/buildURL":19,"./../utils":27,"./InterceptorManager":10,"./dispatchRequest":12,"./mergeConfig":14}],10:[function(e,t,n){"use strict";var i=e("./../utils");function a(){this.handlers=[]}a.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},a.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},a.prototype.forEach=function(t){i.forEach(this.handlers,function(e){null!==e&&t(e)})},t.exports=a},{"./../utils":27}],11:[function(e,t,n){"use strict";var r=e("./enhanceError");t.exports=function(e,t,n,i,a){var o=new Error(e);return r(o,t,n,i,a)}},{"./enhanceError":13}],12:[function(e,t,n){"use strict";var i=e("./../utils"),a=e("./transformData"),o=e("../cancel/isCancel"),r=e("../defaults"),s=e("./../helpers/isAbsoluteURL"),l=e("./../helpers/combineURLs");function p(e){e.cancelToken&&e.cancelToken.throwIfRequested()}t.exports=function(t){return p(t),t.baseURL&&!s(t.url)&&(t.url=l(t.baseURL,t.url)),t.headers=t.headers||{},t.data=a(t.data,t.headers,t.transformRequest),t.headers=i.merge(t.headers.common||{},t.headers[t.method]||{},t.headers||{}),i.forEach(["delete","get","head","post","put","patch","common"],function(e){delete t.headers[e]}),(t.adapter||r.adapter)(t).then(function(e){return p(t),e.data=a(e.data,e.headers,t.transformResponse),e},function(e){return o(e)||(p(t),e&&e.response&&(e.response.data=a(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)})}},{"../cancel/isCancel":8,"../defaults":17,"./../helpers/combineURLs":20,"./../helpers/isAbsoluteURL":22,"./../utils":27,"./transformData":16}],13:[function(e,t,n){"use strict";t.exports=function(e,t,n,i,a){return e.config=t,n&&(e.code=n),e.request=i,e.response=a,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},{}],14:[function(e,t,n){"use strict";var a=e("../utils");t.exports=function(t,n){n=n||{};var i={};return a.forEach(["url","method","params","data"],function(e){void 0!==n[e]&&(i[e]=n[e])}),a.forEach(["headers","auth","proxy"],function(e){a.isObject(n[e])?i[e]=a.deepMerge(t[e],n[e]):void 0!==n[e]?i[e]=n[e]:a.isObject(t[e])?i[e]=a.deepMerge(t[e]):void 0!==t[e]&&(i[e]=t[e])}),a.forEach(["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"],function(e){void 0!==n[e]?i[e]=n[e]:void 0!==t[e]&&(i[e]=t[e])}),i}},{"../utils":27}],15:[function(e,t,n){"use strict";var a=e("./createError");t.exports=function(e,t,n){var i=n.config.validateStatus;!i||i(n.status)?e(n):t(a("Request failed with status code "+n.status,n.config,null,n.request,n))}},{"./createError":11}],16:[function(e,t,n){"use strict";var i=e("./../utils");t.exports=function(t,n,e){return i.forEach(e,function(e){t=e(t,n)}),t}},{"./../utils":27}],17:[function(s,l,e){(function(e){"use strict";var n=s("./utils"),i=s("./helpers/normalizeHeaderName"),t={"Content-Type":"application/x-www-form-urlencoded"};function a(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var o,r={adapter:(void 0!==e&&"[object process]"===Object.prototype.toString.call(e)?o=s("./adapters/http"):"undefined"!=typeof XMLHttpRequest&&(o=s("./adapters/xhr")),o),transformRequest:[function(e,t){return i(t,"Accept"),i(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e)?e:n.isArrayBufferView(e)?e.buffer:n.isURLSearchParams(e)?(a(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):n.isObject(e)?(a(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return 200<=e&&e<300}};r.headers={common:{Accept:"application/json, text/plain, */*"}},n.forEach(["delete","get","head"],function(e){r.headers[e]={}}),n.forEach(["post","put","patch"],function(e){r.headers[e]=n.merge(t)}),l.exports=r}).call(this,s("_process"))},{"./adapters/http":4,"./adapters/xhr":4,"./helpers/normalizeHeaderName":24,"./utils":27,_process:34}],18:[function(e,t,n){"use strict";t.exports=function(n,i){return function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];return n.apply(i,e)}}},{}],19:[function(e,t,n){"use strict";var r=e("./../utils");function s(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(e,t,n){if(!t)return e;var i;if(n)i=n(t);else if(r.isURLSearchParams(t))i=t.toString();else{var a=[];r.forEach(t,function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),a.push(s(t)+"="+s(e))}))}),i=a.join("&")}if(i){var o=e.indexOf("#");-1!==o&&(e=e.slice(0,o)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},{"./../utils":27}],20:[function(e,t,n){"use strict";t.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},{}],21:[function(e,t,n){"use strict";var s=e("./../utils");t.exports=s.isStandardBrowserEnv()?{write:function(e,t,n,i,a,o){var r=[];r.push(e+"="+encodeURIComponent(t)),s.isNumber(n)&&r.push("expires="+new Date(n).toGMTString()),s.isString(i)&&r.push("path="+i),s.isString(a)&&r.push("domain="+a),!0===o&&r.push("secure"),document.cookie=r.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},{"./../utils":27}],22:[function(e,t,n){"use strict";t.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},{}],23:[function(e,t,n){"use strict";var r=e("./../utils");t.exports=r.isStandardBrowserEnv()?function(){var n,i=/(msie|trident)/i.test(navigator.userAgent),a=document.createElement("a");function o(e){var t=e;return i&&(a.setAttribute("href",t),t=a.href),a.setAttribute("href",t),{href:a.href,protocol:a.protocol?a.protocol.replace(/:$/,""):"",host:a.host,search:a.search?a.search.replace(/^\?/,""):"",hash:a.hash?a.hash.replace(/^#/,""):"",hostname:a.hostname,port:a.port,pathname:"/"===a.pathname.charAt(0)?a.pathname:"/"+a.pathname}}return n=o(window.location.href),function(e){var t=r.isString(e)?o(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return!0}},{"./../utils":27}],24:[function(e,t,n){"use strict";var a=e("../utils");t.exports=function(n,i){a.forEach(n,function(e,t){t!==i&&t.toUpperCase()===i.toUpperCase()&&(n[i]=e,delete n[t])})}},{"../utils":27}],25:[function(e,t,n){"use strict";var o=e("./../utils"),r=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];t.exports=function(e){var t,n,i,a={};return e&&o.forEach(e.split("\n"),function(e){if(i=e.indexOf(":"),t=o.trim(e.substr(0,i)).toLowerCase(),n=o.trim(e.substr(i+1)),t){if(a[t]&&0<=r.indexOf(t))return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([n]):a[t]?a[t]+", "+n:n}}),a}},{"./../utils":27}],26:[function(e,t,n){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},{}],27:[function(e,t,n){"use strict";var a=e("./helpers/bind"),i=e("is-buffer"),o=Object.prototype.toString;function r(e){return"[object Array]"===o.call(e)}function s(e){return null!==e&&"object"==typeof e}function l(e){return"[object Function]"===o.call(e)}function p(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),r(e))for(var n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.call(null,e[a],a,e)}t.exports={isArray:r,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:i,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:l,isStream:function(e){return s(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:p,merge:function n(){var i={};function e(e,t){"object"==typeof i[t]&&"object"==typeof e?i[t]=n(i[t],e):i[t]=e}for(var t=0,a=arguments.length;t<a;t++)p(arguments[t],e);return i},deepMerge:function n(){var i={};function e(e,t){"object"==typeof i[t]&&"object"==typeof e?i[t]=n(i[t],e):i[t]="object"==typeof e?n({},e):e}for(var t=0,a=arguments.length;t<a;t++)p(arguments[t],e);return i},extend:function(n,e,i){return p(e,function(e,t){n[t]=i&&"function"==typeof e?a(e,i):e}),n},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},{"./helpers/bind":18,"is-buffer":32}],28:[function(e,t,n){},{}],29:[function(n,i,o){(function(t){function e(){var e;try{e=o.storage.debug}catch(e){}return!e&&void 0!==t&&"env"in t&&(e=t.env.DEBUG),e}(o=i.exports=n("./debug")).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},o.formatArgs=function(e){var t=this.useColors;if(e[0]=(t?"%c":"")+this.namespace+(t?" %c":" ")+e[0]+(t?"%c ":" ")+"+"+o.humanize(this.diff),!t)return;var n="color: "+this.color;e.splice(1,0,n,"color: inherit");var i=0,a=0;e[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&(i++,"%c"===e&&(a=i))}),e.splice(a,0,n)},o.save=function(e){try{null==e?o.storage.removeItem("debug"):o.storage.debug=e}catch(e){}},o.load=e,o.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},o.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),o.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],o.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},o.enable(e())}).call(this,n("_process"))},{"./debug":30,_process:34}],30:[function(e,t,s){var l;function n(e){function i(){if(i.enabled){var a=i,e=+new Date,t=e-(l||e);a.diff=t,a.prev=l,a.curr=e,l=e;for(var o=new Array(arguments.length),n=0;n<o.length;n++)o[n]=arguments[n];o[0]=s.coerce(o[0]),"string"!=typeof o[0]&&o.unshift("%O");var r=0;o[0]=o[0].replace(/%([a-zA-Z%])/g,function(e,t){if("%%"===e)return e;r++;var n=s.formatters[t];if("function"==typeof n){var i=o[r];e=n.call(a,i),o.splice(r,1),r--}return e}),s.formatArgs.call(a,o),(i.log||s.log||console.log.bind(console)).apply(a,o)}}return i.namespace=e,i.enabled=s.enabled(e),i.useColors=s.useColors(),i.color=function(e){var t,n=0;for(t in e)n=(n<<5)-n+e.charCodeAt(t),n|=0;return s.colors[Math.abs(n)%s.colors.length]}(e),"function"==typeof s.init&&s.init(i),i}(s=t.exports=n.debug=n.default=n).coerce=function(e){return e instanceof Error?e.stack||e.message:e},s.disable=function(){s.enable("")},s.enable=function(e){s.save(e),s.names=[],s.skips=[];for(var t=("string"==typeof e?e:"").split(/[\s,]+/),n=t.length,i=0;i<n;i++)t[i]&&("-"===(e=t[i].replace(/\*/g,".*?"))[0]?s.skips.push(new RegExp("^"+e.substr(1)+"$")):s.names.push(new RegExp("^"+e+"$")))},s.enabled=function(e){var t,n;for(t=0,n=s.skips.length;t<n;t++)if(s.skips[t].test(e))return!1;for(t=0,n=s.names.length;t<n;t++)if(s.names[t].test(e))return!0;return!1},s.humanize=e("ms"),s.names=[],s.skips=[],s.formatters={}},{ms:33}],31:[function(e,i,o){(function(a){!function(u){var c=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},t=10;function p(){this._events={},this._conf&&n.call(this,this._conf)}function n(e){e?((this._conf=e).delimiter&&(this.delimiter=e.delimiter),this._maxListeners=e.maxListeners!==u?e.maxListeners:t,e.wildcard&&(this.wildcard=e.wildcard),e.newListener&&(this.newListener=e.newListener),e.verboseMemoryLeak&&(this.verboseMemoryLeak=e.verboseMemoryLeak),this.wildcard&&(this.listenerTree={})):this._maxListeners=t}function r(e,t){var n="(node) warning: possible EventEmitter memory leak detected. "+e+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(n+=" Event name: "+t+"."),void 0!==a&&a.emitWarning){var i=new Error(n);i.name="MaxListenersExceededWarning",i.emitter=this,i.count=e,a.emitWarning(i)}else console.error(n),console.trace&&console.trace()}function e(e){this._events={},this.newListener=!1,this.verboseMemoryLeak=!1,n.call(this,e)}function y(e,t,n,i){if(!n)return[];var a,o,r,s,l,p,u,c=[],d=t.length,f=t[i],m=t[i+1];if(i===d&&n._listeners){if("function"==typeof n._listeners)return e&&e.push(n._listeners),[n];for(a=0,o=n._listeners.length;a<o;a++)e&&e.push(n._listeners[a]);return[n]}if("*"===f||"**"===f||n[f]){if("*"===f){for(r in n)"_listeners"!==r&&n.hasOwnProperty(r)&&(c=c.concat(y(e,t,n[r],i+1)));return c}if("**"===f){for(r in(u=i+1===d||i+2===d&&"*"===m)&&n._listeners&&(c=c.concat(y(e,t,n,d))),n)"_listeners"!==r&&n.hasOwnProperty(r)&&("*"===r||"**"===r?(n[r]._listeners&&!u&&(c=c.concat(y(e,t,n[r],d))),c=c.concat(y(e,t,n[r],i))):c=r===m?c.concat(y(e,t,n[r],i+2)):c.concat(y(e,t,n[r],i)));return c}c=c.concat(y(e,t,n[f],i+1))}if((s=n["*"])&&y(e,t,s,i+1),l=n["**"])if(i<d)for(r in l._listeners&&y(e,t,l,d),l)"_listeners"!==r&&l.hasOwnProperty(r)&&(r===m?y(e,t,l[r],i+2):r===f?y(e,t,l[r],i+1):((p={})[r]=l[r],y(e,t,{"**":p},i+1)));else l._listeners?y(e,t,l,d):l["*"]&&l["*"]._listeners&&y(e,t,l["*"],d);return c}(e.EventEmitter2=e).prototype.delimiter=".",e.prototype.setMaxListeners=function(e){e!==u&&(this._maxListeners=e,this._conf||(this._conf={}),this._conf.maxListeners=e)},e.prototype.event="",e.prototype.once=function(e,t){return this._once(e,t,!1)},e.prototype.prependOnceListener=function(e,t){return this._once(e,t,!0)},e.prototype._once=function(e,t,n){return this._many(e,1,t,n),this},e.prototype.many=function(e,t,n){return this._many(e,t,n,!1)},e.prototype.prependMany=function(e,t,n){return this._many(e,t,n,!0)},e.prototype._many=function(e,t,n,i){var a=this;if("function"!=typeof n)throw new Error("many only accepts instances of Function");function o(){return 0==--t&&a.off(e,o),n.apply(this,arguments)}return o._origin=n,this._on(e,o,i),a},e.prototype.emit=function(){this._events||p.call(this);var e=arguments[0];if("newListener"===e&&!this.newListener&&!this._events.newListener)return!1;var t,n,i,a,o,r=arguments.length;if(this._all&&this._all.length){if(o=this._all.slice(),3<r)for(t=new Array(r),a=0;a<r;a++)t[a]=arguments[a];for(i=0,n=o.length;i<n;i++)switch(this.event=e,r){case 1:o[i].call(this,e);break;case 2:o[i].call(this,e,arguments[1]);break;case 3:o[i].call(this,e,arguments[1],arguments[2]);break;default:o[i].apply(this,t)}}if(this.wildcard){o=[];var s="string"==typeof e?e.split(this.delimiter):e.slice();y.call(this,o,s,this.listenerTree,0)}else{if("function"==typeof(o=this._events[e])){switch(this.event=e,r){case 1:o.call(this);break;case 2:o.call(this,arguments[1]);break;case 3:o.call(this,arguments[1],arguments[2]);break;default:for(t=new Array(r-1),a=1;a<r;a++)t[a-1]=arguments[a];o.apply(this,t)}return!0}o&&(o=o.slice())}if(o&&o.length){if(3<r)for(t=new Array(r-1),a=1;a<r;a++)t[a-1]=arguments[a];for(i=0,n=o.length;i<n;i++)switch(this.event=e,r){case 1:o[i].call(this);break;case 2:o[i].call(this,arguments[1]);break;case 3:o[i].call(this,arguments[1],arguments[2]);break;default:o[i].apply(this,t)}return!0}if(!this._all&&"error"===e)throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},e.prototype.emitAsync=function(){this._events||p.call(this);var e=arguments[0];if("newListener"===e&&!this.newListener&&!this._events.newListener)return Promise.resolve([!1]);var t,n,i,a,o,r=[],s=arguments.length;if(this._all){if(3<s)for(t=new Array(s),a=1;a<s;a++)t[a]=arguments[a];for(i=0,n=this._all.length;i<n;i++)switch(this.event=e,s){case 1:r.push(this._all[i].call(this,e));break;case 2:r.push(this._all[i].call(this,e,arguments[1]));break;case 3:r.push(this._all[i].call(this,e,arguments[1],arguments[2]));break;default:r.push(this._all[i].apply(this,t))}}if(this.wildcard){o=[];var l="string"==typeof e?e.split(this.delimiter):e.slice();y.call(this,o,l,this.listenerTree,0)}else o=this._events[e];if("function"==typeof o)switch(this.event=e,s){case 1:r.push(o.call(this));break;case 2:r.push(o.call(this,arguments[1]));break;case 3:r.push(o.call(this,arguments[1],arguments[2]));break;default:for(t=new Array(s-1),a=1;a<s;a++)t[a-1]=arguments[a];r.push(o.apply(this,t))}else if(o&&o.length){if(o=o.slice(),3<s)for(t=new Array(s-1),a=1;a<s;a++)t[a-1]=arguments[a];for(i=0,n=o.length;i<n;i++)switch(this.event=e,s){case 1:r.push(o[i].call(this));break;case 2:r.push(o[i].call(this,arguments[1]));break;case 3:r.push(o[i].call(this,arguments[1],arguments[2]));break;default:r.push(o[i].apply(this,t))}}else if(!this._all&&"error"===e)return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(r)},e.prototype.on=function(e,t){return this._on(e,t,!1)},e.prototype.prependListener=function(e,t){return this._on(e,t,!0)},e.prototype.onAny=function(e){return this._onAny(e,!1)},e.prototype.prependAny=function(e){return this._onAny(e,!0)},e.prototype.addListener=e.prototype.on,e.prototype._onAny=function(e,t){if("function"!=typeof e)throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),t?this._all.unshift(e):this._all.push(e),this},e.prototype._on=function(e,t,n){if("function"==typeof e)return this._onAny(e,t),this;if("function"!=typeof t)throw new Error("on only accepts instances of Function");return this._events||p.call(this),this.emit("newListener",e,t),this.wildcard?function(e,t){for(var n=0,i=(e="string"==typeof e?e.split(this.delimiter):e.slice()).length;n+1<i;n++)if("**"===e[n]&&"**"===e[n+1])return;for(var a=this.listenerTree,o=e.shift();o!==u;){if(a[o]||(a[o]={}),a=a[o],0===e.length)return a._listeners?("function"==typeof a._listeners&&(a._listeners=[a._listeners]),a._listeners.push(t),!a._listeners.warned&&0<this._maxListeners&&a._listeners.length>this._maxListeners&&(a._listeners.warned=!0,r.call(this,a._listeners.length,o))):a._listeners=t,!0;o=e.shift()}return!0}.call(this,e,t):this._events[e]?("function"==typeof this._events[e]&&(this._events[e]=[this._events[e]]),n?this._events[e].unshift(t):this._events[e].push(t),!this._events[e].warned&&0<this._maxListeners&&this._events[e].length>this._maxListeners&&(this._events[e].warned=!0,r.call(this,this._events[e].length,e))):this._events[e]=t,this},e.prototype.off=function(e,t){if("function"!=typeof t)throw new Error("removeListener only takes instances of Function");var n,i=[];if(this.wildcard){var a="string"==typeof e?e.split(this.delimiter):e.slice();i=y.call(this,null,a,this.listenerTree,0)}else{if(!this._events[e])return this;n=this._events[e],i.push({_listeners:n})}for(var o=0;o<i.length;o++){var r=i[o];if(n=r._listeners,c(n)){for(var s=-1,l=0,p=n.length;l<p;l++)if(n[l]===t||n[l].listener&&n[l].listener===t||n[l]._origin&&n[l]._origin===t){s=l;break}if(s<0)continue;return this.wildcard?r._listeners.splice(s,1):this._events[e].splice(s,1),0===n.length&&(this.wildcard?delete r._listeners:delete this._events[e]),this.emit("removeListener",e,t),this}(n===t||n.listener&&n.listener===t||n._origin&&n._origin===t)&&(this.wildcard?delete r._listeners:delete this._events[e],this.emit("removeListener",e,t))}return function e(t){if(t!==u){var n=Object.keys(t);for(var i in n){var a=n[i],o=t[a];o instanceof Function||"object"!=typeof o||null===o||(0<Object.keys(o).length&&e(t[a]),0===Object.keys(o).length&&delete t[a])}}}(this.listenerTree),this},e.prototype.offAny=function(e){var t,n=0,i=0;if(e&&this._all&&0<this._all.length){for(n=0,i=(t=this._all).length;n<i;n++)if(e===t[n])return t.splice(n,1),this.emit("removeListenerAny",e),this}else{for(n=0,i=(t=this._all).length;n<i;n++)this.emit("removeListenerAny",t[n]);this._all=[]}return this},e.prototype.removeListener=e.prototype.off,e.prototype.removeAllListeners=function(e){if(0===arguments.length)return!this._events||p.call(this),this;if(this.wildcard)for(var t="string"==typeof e?e.split(this.delimiter):e.slice(),n=y.call(this,null,t,this.listenerTree,0),i=0;i<n.length;i++){n[i]._listeners=null}else this._events&&(this._events[e]=null);return this},e.prototype.listeners=function(e){if(this.wildcard){var t=[],n="string"==typeof e?e.split(this.delimiter):e.slice();return y.call(this,t,n,this.listenerTree,0),t}return this._events||p.call(this),this._events[e]||(this._events[e]=[]),c(this._events[e])||(this._events[e]=[this._events[e]]),this._events[e]},e.prototype.eventNames=function(){return Object.keys(this._events)},e.prototype.listenerCount=function(e){return this.listeners(e).length},e.prototype.listenersAny=function(){return this._all?this._all:[]},"object"==typeof o?i.exports=e:window.EventEmitter2=e}()}).call(this,e("_process"))},{_process:34}],32:[function(e,t,n){t.exports=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}},{}],33:[function(e,t,n){var a=36e5,o=864e5;function r(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}t.exports=function(e,t){t=t||{};var n,i=typeof e;if("string"===i&&0<e.length)return function(e){if(100<(e=String(e)).length)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var n=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*n;case"days":case"day":case"d":return n*o;case"hours":case"hour":case"hrs":case"hr":case"h":return n*a;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*n;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}(e);if("number"===i&&!1===isNaN(e))return t.long?r(n=e,o,"day")||r(n,a,"hour")||r(n,6e4,"minute")||r(n,1e3,"second")||n+" ms":function(e){if(o<=e)return Math.round(e/o)+"d";if(a<=e)return Math.round(e/a)+"h";if(6e4<=e)return Math.round(e/6e4)+"m";if(1e3<=e)return Math.round(e/1e3)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},{}],34:[function(e,t,n){var i,a,o=t.exports={};function r(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function l(t){if(i===setTimeout)return setTimeout(t,0);if((i===r||!i)&&setTimeout)return i=setTimeout,setTimeout(t,0);try{return i(t,0)}catch(e){try{return i.call(null,t,0)}catch(e){return i.call(this,t,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:r}catch(e){i=r}try{a="function"==typeof clearTimeout?clearTimeout:s}catch(e){a=s}}();var p,u=[],c=!1,d=-1;function f(){c&&p&&(c=!1,p.length?u=p.concat(u):d=-1,u.length&&m())}function m(){if(!c){var e=l(f);c=!0;for(var t=u.length;t;){for(p=u,u=[];++d<t;)p&&p[d].run();d=-1,t=u.length}p=null,c=!1,function(t){if(a===clearTimeout)return clearTimeout(t);if((a===s||!a)&&clearTimeout)return a=clearTimeout,clearTimeout(t);try{a(t)}catch(e){try{return a.call(null,t)}catch(e){return a.call(this,t)}}}(e)}}function y(e,t){this.fun=e,this.array=t}function h(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new y(e,t)),1!==u.length||c||l(m)},y.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=h,o.addListener=h,o.once=h,o.off=h,o.removeListener=h,o.removeAllListeners=h,o.emit=h,o.prependListener=h,o.prependOnceListener=h,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],35:[function(l,e,p){(function(e,t){var i=l("process/browser.js").nextTick,n=Function.prototype.apply,a=Array.prototype.slice,o={},r=0;function s(e,t){this._id=e,this._clearFn=t}p.setTimeout=function(){return new s(n.call(setTimeout,window,arguments),clearTimeout)},p.setInterval=function(){return new s(n.call(setInterval,window,arguments),clearInterval)},p.clearTimeout=p.clearInterval=function(e){e.close()},s.prototype.unref=s.prototype.ref=function(){},s.prototype.close=function(){this._clearFn.call(window,this._id)},p.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},p.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},p._unrefActive=p.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;0<=t&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},p.setImmediate="function"==typeof e?e:function(e){var t=r++,n=!(arguments.length<2)&&a.call(arguments,1);return o[t]=!0,i(function(){o[t]&&(n?e.apply(null,n):e.call(null),p.clearImmediate(t))}),t},p.clearImmediate="function"==typeof t?t:function(e){delete o[e]}}).call(this,l("timers").setImmediate,l("timers").clearImmediate)},{"process/browser.js":34,timers:35}],36:[function(e,t,n){t.exports={name:"@pm2/js-api",version:"0.5.63",description:"PM2.io API Client for Javascript",main:"index.js",unpkg:"dist/keymetrics.es5.min.js",engines:{node:">=4.0"},scripts:{test:"mocha test/*",build:"mkdir -p dist; browserify -s Keymetrics -r ./ > ./dist/keymetrics.es5.js",dist:"mkdir -p dist; browserify -s Keymetrics -r ./ | uglifyjs -c warnings=false -m > ./dist/keymetrics.es5.min.js",doc:"jsdoc -r ./src --readme ./README.md -d doc -t ./node_modules/minami",clean:"rm dist/*",prepublish:"npm run build && npm run dist"},repository:{type:"git",url:"git+https://github.com/keymetrics/km.js.git"},keywords:["keymetrics","api","dashboard","monitoring","wrapper"],author:"Keymetrics Team",license:"Apache-2",bugs:{url:"https://github.com/keymetrics/km.js/issues"},homepage:"https://github.com/keymetrics/km.js#readme",dependencies:{async:"^2.6.3",axios:"^0.19.0",debug:"^2.6.8",eventemitter2:"^4.1.0",ws:"^3.0.0"},devDependencies:{"babel-core":"6.26.0","babel-preset-es2015":"*","babel-preset-stage-2":"6.24.1",babelify:"8.0.0",browserify:"^13.1.0",jsdoc:"^3.4.2",minami:"^1.1.1",mocha:"^3.0.2",pm2:"^4.1.2",should:"*",standard:"^10.0.2","uglify-js":"~3.3.7"},browserify:{debug:"true",transform:[["babelify",{presets:[["babel-preset-es2015",{debug:"true",sourceMaps:"true"}]]}]]},browser:{"./src/auth_strategies/embed_strategy.js":!1,ws:!1},standard:{ignore:["dist/**","examples/**","test/**"]}}},{}],37:[function(e,t,n){t.exports={actions:[{route:{name:"/api/bucket/:id/actions/trigger",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of the server",optional:!1,defaultvalue:null},{name:"process_id",type:"number",description:"the id of the process",optional:!0,defaultvalue:null},{name:"app_name",type:"number",description:"the name of the process",optional:!0,defaultvalue:null},{name:"action_name",type:"string",description:"the name of the action to trigger",optional:!1,defaultvalue:null},{name:"opts",type:"object",description:"any specific options to be passed to the function",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully run the action",optional:!1}],response:[{name:"success",type:"boolean",description:"succesully sended the action to PM2",optional:!1,defaultvalue:null}],name:"triggerAction",longname:"Actions.triggerAction",scope:"route"},{route:{name:"/api/bucket/:id/actions/triggerPM2",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of the server",optional:!1,defaultvalue:null},{name:"method_name",type:"string",description:"the name of the pm2 method to trigger",optional:!1,defaultvalue:null},{name:"app_name",type:"string",description:"the name of the application",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"failed action",optional:!1},{type:"200",description:"succesfully run the action",optional:!1}],response:[{name:"success",type:"boolean",description:"succesully sended the action to PM2",optional:!1,defaultvalue:null}],name:"triggerPM2Action",longname:"Actions.triggerPM2Action",scope:"route"},{route:{name:"/api/bucket/:id/actions/triggerScopedAction",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of the server",optional:!1,defaultvalue:null},{name:"action_name",type:"string",description:"the name of the pm2 method to trigger",optional:!1,defaultvalue:null},{name:"app_name",type:"string",description:"the name of the application",optional:!1,defaultvalue:null},{name:"pm_id",type:"number",description:"the id of the process",optional:!1,defaultvalue:null},{name:"opts",type:"object",description:"custom parameters to give to the action",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully run the action",optional:!1}],response:[{name:".",type:"object",description:"the action sended to the process",optional:!1,defaultvalue:null}],name:"triggerScopedAction",longname:"Actions.triggerScopedAction",scope:"route"}],bucket:{alert:{analyzer:[{route:{name:"/api/bucket/:id/alerts/analyzer",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"size",type:"integer",description:"line limit, default to 20",optional:!0,defaultvalue:null},{name:"from",type:"integer",description:"offset limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"list all alerts",optional:!1}],name:"list",longname:"Bucket.alert.analyzer.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/alerts/analyzer/:alert",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],body:[{name:"useful",type:"boolean",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"content modified",optional:!1}],name:"editState",longname:"Bucket.alert.analyzer.editState",scope:"route"},{route:{name:"/api/bucket/:id/alerts/analyzer/:analyzer/config",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":analyzer",type:"string",description:"analyzer name",optional:!1}],body:[{name:"blacklist",type:"object",description:"",optional:!1,defaultvalue:null},{name:"blacklist.apps",type:"array",description:"",optional:!0,defaultvalue:null},{name:"blacklist.servers",type:"array",description:"",optional:!0,defaultvalue:null},{name:"blacklist.metrics",type:"array",description:"",optional:!0,defaultvalue:null},{name:"threshold",type:"number",description:"",optional:!1,defaultvalue:null},{name:"window",type:"number",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"content modified",optional:!1}],name:"updateConfig",longname:"Bucket.alert.analyzer.updateConfig",scope:"route"}],default:[{route:{name:"/api/bucket/:id/alerts",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"Alert name",optional:!1,defaultvalue:null},{name:"enabled",type:"boolean",description:"Alert's state",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"Should be `metric`, `event` or `webcheck`",optional:!1,defaultvalue:null},{name:"initiator",type:"string",description:"Should be metric name or event name",optional:!1,defaultvalue:null},{name:"options",type:"object",description:"",optional:!1,defaultvalue:null},{name:"options.operator",type:"string",description:"Should be `>`, `<`, `=`, `>=` or `<=`",optional:!0,defaultvalue:null},{name:"options.threshold",type:"number",description:"Value to reach to send an alert",optional:!0,defaultvalue:null},{name:"options.act",type:"string",description:"Should be `always`, `opposite`, `first` or `diff`",optional:!0,defaultvalue:null},{name:"options.timerange",type:"number",description:"Timerange to check, in seconds",optional:!0,defaultvalue:null},{name:"scope",type:"object",description:"",optional:!1,defaultvalue:null},{name:"scope.apps",type:"object",description:"Array of strings with apps name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.servers",type:"object",description:"Array of strings with servers name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.initiators",type:"object",description:"Array of strings with initiators name (need to be set if no apps or servers)",optional:!0,defaultvalue:null},{name:"scope.sources",type:"object",description:"Array of strings with sources name (can be empty)",optional:!0,defaultvalue:null},{name:"actions",type:"object",description:"List of actions to trigger",optional:!1,defaultvalue:null},{name:"actions[].type",type:"string",description:"Type of action",optional:!0,defaultvalue:null},{name:"actions[].params",type:"object",description:"Params for action",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly created alert",optional:!1}],name:"create",longname:"Bucket.alert.create",scope:"route"},{route:{name:"/api/bucket/:id/alerts/:alert",type:"DELETE"},params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"204",description:"successfuly deleted alert",optional:!1}],name:"delete",longname:"Bucket.alert.delete",scope:"route",authentication:!1},{route:{name:"/api/bucket/:id/alerts/",type:"GET"},params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"list all alerts",optional:!1}],name:"list",longname:"Bucket.alert.list",scope:"route",authentication:!1},{route:{name:"/api/bucket/:id/alerts/:alert",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],body:[{name:"name",type:"string",description:"Alert name",optional:!0,defaultvalue:null},{name:"enabled",type:"boolean",description:"Alert's state",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"Should be `metric`, `event` or `webcheck`",optional:!0,defaultvalue:null},{name:"initiator",type:"string",description:"Should be metric name or event name",optional:!0,defaultvalue:null},{name:"options",type:"object",description:"",optional:!0,defaultvalue:null},{name:"options.operator",type:"string",description:"Should be `>`, `<`, `=`, `<=` or `>=`",optional:!0,defaultvalue:null},{name:"options.threshold",type:"number",description:"Value to reach to send an alert",optional:!0,defaultvalue:null},{name:"options.act",type:"string",description:"Should be `always`, `opposite`, `first` or `diff`",optional:!0,defaultvalue:null},{name:"options.timerange",type:"number",description:"Timerange to check, in seconds",optional:!0,defaultvalue:null},{name:"scope",type:"object",description:"",optional:!0,defaultvalue:null},{name:"scope.apps",type:"array",description:"Array of strings with apps name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.servers",type:"array",description:"Array of strings with servers name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.initiators",type:"object",description:"Array of strings with initiators name (need to be set if no apps or servers)",optional:!0,defaultvalue:null},{name:"scope.sources",type:"object",description:"Array of strings with sources name (can be empty)",optional:!0,defaultvalue:null},{name:"actions",type:"array",description:"List of actions to trigger",optional:!0,defaultvalue:null},{name:"actions[].type",type:"string",description:"Type of action",optional:!0,defaultvalue:null},{name:"actions[].params",type:"object",description:"Params for action",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"404",description:"alert not found",optional:!1},{type:"200",description:"successfuly created alert",optional:!1}],name:"updateAlert",longname:"Bucket.alert.updateAlert",scope:"route"},{route:{name:"/api/bucket/:id/alerts/:alert",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"alert not found",optional:!1},{type:"200",description:"successfuly returned alert",optional:!1}],name:"get",longname:"Bucket.alert.get",scope:"route",async:!0},{route:{name:"/api/bucket/:id/alerts/:alert/sample",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"alert not found",optional:!1},{type:"202",description:"successfuly sended alert actions",optional:!1}],name:"triggerSample",longname:"Bucket.alert.triggerSample",scope:"route"},{route:{name:"/api/bucket/:id/alerts/update",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"triggers",type:"object",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing triggers parameter",optional:!1},{type:"200",description:"succesfully update triggers",optional:!1}],response:[{name:"triggers",type:"object",description:"new triggers object",optional:!1,defaultvalue:null}],name:"update",longname:"Bucket.alert.update",scope:"route"},{route:{name:"/api/bucket/:id/alerts/updateSlack",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"slack",type:"object",description:"",optional:!1,defaultvalue:null},{name:"slack.active",type:"boolean",description:"",optional:!0,defaultvalue:null},{name:"slack.url",type:"boolean",description:"needed if active is set to true",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing triggers parameter",optional:!1},{type:"200",description:"succesfully update triggers",optional:!1}],response:[{name:"bucket",type:"object",description:"",optional:!1,defaultvalue:null}],name:"updateSlack",longname:"Bucket.alert.updateSlack",scope:"route"},{route:{name:"/api/bucket/:id/alerts/updateWebhooks",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"webhooks",type:"object",description:"",optional:!1,defaultvalue:null},{name:"webhooks.active",type:"boolean",description:"",optional:!0,defaultvalue:null},{name:"webhooks.url",type:"boolean",description:"needed if active is set to true",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing triggers parameter",optional:!1},{type:"200",description:"succesfully update triggers",optional:!1}],response:[{name:"bucket",type:"object",description:"",optional:!1,defaultvalue:null}],name:"updateWebhooks",longname:"Bucket.alert.updateWebhooks",scope:"route"}]},application:[{route:{name:"/api/bucket/:id/applications",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved applications",optional:!1}],name:"list",longname:"Bucket.application.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"application not found",optional:!1},{type:"200",description:"successfuly retrieved application",optional:!1}],name:"get",longname:"Bucket.application.get",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"",optional:!1,defaultvalue:null},{name:"domains",type:"object",description:"Array of string with domains",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly created application",optional:!1}],name:"create",longname:"Bucket.application.create",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],body:[{name:"name",type:"string",description:"",optional:!0,defaultvalue:null},{name:"domains",type:"object",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly updated application",optional:!1}],name:"update",longname:"Bucket.application.update",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application",type:"DELETE"},params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"204",description:"successfuly deleted application",optional:!1}],name:"delete",longname:"Bucket.application.delete",scope:"route",async:!0,authentication:!1},{route:{name:"/api/bucket/:id/applications/:application/preview",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"preview not found",optional:!1},{type:"200",description:"successfuly retrieved application screenshot",optional:!1}],name:"getPreview",longname:"Bucket.application.getPreview",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application/report",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"report not found",optional:!1},{type:"200",description:"successfuly retrieved application report",optional:!1}],name:"getReports",longname:"Bucket.application.getReports",scope:"route",async:!0}],billing:[{route:{name:"/api/bucket/:id/payment/subscribe",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"plan",type:"string",description:"name of the plan to upgrade to",optional:!1,defaultvalue:null},{name:"stripe_token",type:"string",description:"a card token created by stripe",optional:!0,defaultvalue:null},{name:"coupon_id",type:"string",description:"the id of the stripe coupon",optional:!0,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"403",description:"need a credit card OR not allowed to subscribe to the plan",optional:!1},{type:"500",description:"stripe/database error",optional:!1},{type:"200",description:"succesfully upgraded",optional:!1}],response:[{name:"bucket",type:"object",description:"the bucket object",optional:!1,defaultvalue:null},{name:"subscription",type:"object",description:"the subscription object attached to the subscription",optional:!1,defaultvalue:null}],name:"subscribe",longname:"Bucket.billing.subscribe",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/subscribe/:paymentIntent",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":paymentIntent",type:"string",description:"paymentIntent id",optional:!1}],body:[{name:"plan",type:"string",description:"name of the plan to upgrade to",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"500",description:"stripe/database error",optional:!1},{type:"200",description:"succesfully upgraded",optional:!1}],response:[{name:"bucket",type:"object",description:"the bucket object",optional:!1,defaultvalue:null}],name:"paymentIntentSucceed",longname:"Bucket.billing.paymentIntentSucceed",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/trial",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"plan",type:"string",description:"Plan to trial",optional:!1,defaultvalue:null}],code:[{type:"400",description:"can't claim trial",optional:!1},{type:"200",description:"trial launched",optional:!1}],response:[{name:"duration",type:"string",description:"the duration of the trial",optional:!1,defaultvalue:null},{name:"plan",type:"string",description:"the plan of the trial",optional:!1,defaultvalue:null}],name:"startTrial",longname:"Bucket.billing.startTrial",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/invoices",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"400",description:"Missing/invalid parameters",optional:!1},{type:"404",description:"This bucket hasn't invoices",optional:!1},{type:"200",description:"succesfully returns invoices",optional:!1}],response:[{name:".",type:"array",description:"array of invoices",optional:!1,defaultvalue:null}],name:"getInvoices",longname:"Bucket.billing.getInvoices",scope:"route"},{route:{name:"/api/bucket/:id/payment/receipts",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"400",description:"Missing/invalid parameters",optional:!1},{type:"404",description:"This bucket hasn't receipts",optional:!1},{type:"200",description:"succesfully returns receipts",optional:!1}],response:[{name:".",type:"array",description:"array of receipts",optional:!1,defaultvalue:null}],name:"getReceipts",longname:"Bucket.billing.getReceipts",scope:"route"},{route:{name:"/api/bucket/:id/payment/subscription",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"404",description:"the bucket doesnt have any subscription",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved the subscription",optional:!1}],response:[{name:".",type:"object",description:"subscription object",optional:!1,defaultvalue:null}],name:"getSubcription",longname:"Bucket.billing.getSubcription",scope:"route"},{route:{name:"/api/bucket/:id/payment/subscription/state",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"404",description:"the bucket doesnt have any subscription",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved the subscription",optional:!1}],response:[{name:"status",type:"string",description:"stripe state of the subscription",optional:!1,defaultvalue:null},{name:"plan",type:"string",description:"stripe plan name of the subscription",optional:!1,defaultvalue:null},{name:"canceled_at",type:"string",description:"if he sub has been cancelled, add the date",optional:!1,defaultvalue:null}],name:"getSubcriptionState",longname:"Bucket.billing.getSubcriptionState",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/cards",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"token",type:"string",description:"card token generated by stripe",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully added the card",optional:!1}],response:[{name:"data",type:"object",description:"stripe credit card Object",optional:!1,defaultvalue:null}],name:"attachCreditCard",longname:"Bucket.billing.attachCreditCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/cards",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"404",description:"the user doesn't have any default card",optional:!1},{type:"200",description:"succesfully retieved the charges",optional:!1}],response:[{name:"data",type:"array",description:"list of stripe cards object",optional:!1,defaultvalue:null}],name:"fetchCreditCards",longname:"Bucket.billing.fetchCreditCards",scope:"route"},{route:{name:"/api/bucket/:id/payment/card/:card_id",type:"GET"},authentication:!0,params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":card_id",type:"string",description:"the stripe id of the card",optional:!1}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"404",description:"the user doesn't have any default card",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe card object",optional:!1,defaultvalue:null}],name:"fetchCreditCard",longname:"Bucket.billing.fetchCreditCard",scope:"route"},{route:{name:"/api/bucket/:id/payment/card",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"404",description:"the user doesn't have any default card",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe card object",optional:!1,defaultvalue:null}],name:"fetchDefaultCreditCard",longname:"Bucket.billing.fetchDefaultCreditCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/card",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"id",type:"string",description:"stripe card id",optional:!1,defaultvalue:null},{name:"address_line1",type:"string",description:"",optional:!0,defaultvalue:null},{name:"address_country",type:"string",description:"",optional:!0,defaultvalue:null},{name:"address_zip",type:"string",description:"",optional:!0,defaultvalue:null},{name:"address_city",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters, you need to specify a card",optional:!1},{type:"200",description:"succesfully updated the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe card object",optional:!1,defaultvalue:null}],name:"updateCreditCard",longname:"Bucket.billing.updateCreditCard",scope:"route"},{route:{name:"/api/bucket/:id/payment/card/:card_id",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":card_id",type:"string",description:"the stripe id of the card",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1},{type:"403",description:"the user must have one card active when having a subscription",optional:!1}],response:[{name:".",type:"object",description:"stripe card object",optional:!1,defaultvalue:null}],name:"deleteCreditCard",longname:"Bucket.billing.deleteCreditCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/card/:card_id/default",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":card_id",type:"string",description:"the stripe id of the card",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"200",description:"succesfully set the card as default",optional:!1}],response:[{name:"data",type:"object",description:"stripe card object",optional:!1,defaultvalue:null}],name:"setDefaultCard",longname:"Bucket.billing.setDefaultCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:".",type:"object",description:"stripe metadata object",optional:!1,defaultvalue:null}],name:"fetchMetadata",longname:"Bucket.billing.fetchMetadata",scope:"route"},{route:{name:"/api/bucket/:id/payment",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"metadata",type:"object",description:"the metadata you can update",optional:!1,defaultvalue:null},{name:"metadata.vat_number",type:"string",description:"",optional:!0,defaultvalue:null},{name:"metadata.company_name",type:"string",description:"",optional:!0,defaultvalue:null},{name:"metadata.receipt_email",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters, you need to specify a card",optional:!1},{type:"200",description:"succesfully updated the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe customer metadata object",optional:!1,defaultvalue:null}],name:"updateMetadata",longname:"Bucket.billing.updateMetadata",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/banks",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"iban",type:"string",description:"the iban used to recognize the account",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"the type of the bank account (currently only sepa is available)",optional:!1,defaultvalue:null},{name:"name",type:"string",description:"name of the bank account owner",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully added the account",optional:!1}],response:[{name:"data",type:"object",description:"stripe credit card Object",optional:!1,defaultvalue:null}],name:"attachBankAccount",longname:"Bucket.billing.attachBankAccount",scope:"route"},{route:{name:"/api/bucket/:id/payment/banks",type:"GET"},authentication:!0,params:[{name:":id",type:"string",description:"bucket id",optional:!1}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"404",description:"the user doesn't have any default account",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1}],response:[{name:"data",type:"object",description:"stripe source object",optional:!1,defaultvalue:null}],name:"fetchBankAccount",longname:"Bucket.billing.fetchBankAccount",scope:"route"},{route:{name:"/api/bucket/:id/payment/banks",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1},{type:"404",description:"the user doesn't have any default account",optional:!1}],response:[{name:".",type:"object",description:"stripe source object",optional:!1,defaultvalue:null}],name:"deleteBankAccount",longname:"Bucket.billing.deleteBankAccount",scope:"route"}],server:[{route:{name:"/api/bucket/:id/data/deleteServer",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of server",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"406",description:"require an action before delete",optional:!1},{type:"400",description:"missing or invalid parameters",optional:!1},{type:"200",description:"successfully deleted",optional:!1}],response:[{name:"success",type:"boolean",description:"can be true or false",optional:!1,defaultvalue:null},{name:"msg",type:"string",description:"response",optional:!1,defaultvalue:null}],name:"deleteServer",longname:"Bucket.server.deleteServer",scope:"route"}],webcheck:[{route:{name:"/api/bucket/:id/webchecks/metrics",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks metrics",optional:!1}],name:"listMetrics",longname:"Bucket.webcheck.listMetrics",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/regions",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks regions",optional:!1}],name:"listRegions",longname:"Bucket.webcheck.listRegions",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck/metrics",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],body:[{name:"start",type:"string",description:"",optional:!0,defaultvalue:null},{name:"metrics",type:"array",description:"",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks regions",optional:!1}],name:"getMetrics",longname:"Bucket.webcheck.getMetrics",scope:"route",async:!0},{route:{name:"/api/bucket/:id/webchecks",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"application",type:"string",description:"Application's id to filter",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks",optional:!1}],name:"list",longname:"Bucket.webcheck.list",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"webcheck not found",optional:!1},{type:"200",description:"successfuly retrieved webcheck",optional:!1}],name:"get",longname:"Bucket.webcheck.get",scope:"route"},{route:{name:"/api/bucket/:id/webchecks",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"Webcheck name",optional:!1,defaultvalue:null},{name:"enabled",type:"boolean",description:"Webcheck's state",optional:!0,defaultvalue:null},{name:"target",type:"object",description:"",optional:!1,defaultvalue:null},{name:"target.type",type:"string",description:"Should be `http`, `https` or `tcp`",optional:!0,defaultvalue:null},{name:"target.port",type:"number",description:"Target's port",optional:!0,defaultvalue:null},{name:"target.address",type:"string",description:"Target's IP/domain",optional:!0,defaultvalue:null},{name:"target.path",type:"string",description:"HTTP Path (only for http/https)",optional:!0,defaultvalue:null},{name:"target.is_frontend",type:"boolean",description:"Enable or disable frontend metrics (via puppeteer)",optional:!0,defaultvalue:null},{name:"body",type:"string",description:"Body need to match this regex to succeed webcheck (only for http/https)",optional:!0,defaultvalue:null},{name:"interval",type:"number",description:"Webcheck's interval check (ms)",optional:!1,defaultvalue:null},{name:"timeout",type:"number",description:"Webcheck's timeout (ms)",optional:!1,defaultvalue:null},{name:"source",type:"object",description:"",optional:!1,defaultvalue:null},{name:"source.region",type:"string",description:"Webcheck's worker region",optional:!0,defaultvalue:null},{name:"retry",type:"object",description:"",optional:!1,defaultvalue:null},{name:"retry.max",type:"number",description:"Max webcheck's retry before mark as failed",optional:!0,defaultvalue:null},{name:"retry.interval",type:"number",description:"Webcheck's retry interval (ms)",optional:!0,defaultvalue:null},{name:"alerts",type:"object",description:"List of alerts (cf. Alert type)",optional:!1,defaultvalue:null},{name:"application",type:"string",description:"Application's id",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly created webcheck",optional:!1}],name:"create",longname:"Bucket.webcheck.create",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],body:[{name:"name",type:"string",description:"Webcheck name",optional:!0,defaultvalue:null},{name:"enabled",type:"boolean",description:"Webcheck's state",optional:!0,defaultvalue:null},{name:"target",type:"object",description:"",optional:!0,defaultvalue:null},{name:"target.type",type:"string",description:"Should be `http`, `https` or `tcp`",optional:!0,defaultvalue:null},{name:"target.port",type:"number",description:"Target's port",optional:!0,defaultvalue:null},{name:"target.address",type:"string",description:"Target's IP/domain",optional:!0,defaultvalue:null},{name:"target.path",type:"string",description:"HTTP Path (only for http/https)",optional:!0,defaultvalue:null},{name:"target.is_frontend",type:"boolean",description:"Enable or disable frontend metrics (via puppeteer)",optional:!0,defaultvalue:null},{name:"body",type:"string",description:"Body need to match this regex to succeed webcheck (only for http/https)",optional:!0,defaultvalue:null},{name:"interval",type:"number",description:"Webcheck's interval check (ms)",optional:!0,defaultvalue:null},{name:"timeout",type:"number",description:"Webcheck's timeout (ms)",optional:!0,defaultvalue:null},{name:"source",type:"object",description:"",optional:!0,defaultvalue:null},{name:"source.region",type:"string",description:"Webcheck's worker region",optional:!0,defaultvalue:null},{name:"retry",type:"object",description:"",optional:!0,defaultvalue:null},{name:"retry.max",type:"number",description:"Max webcheck's retry before mark as failed",optional:!0,defaultvalue:null},{name:"retry.interval",type:"number",description:"Webcheck's retry interval (ms)",optional:!0,defaultvalue:null},{name:"alerts",type:"object",description:"List of alerts (cf. Alert type)",optional:!0,defaultvalue:null},{name:"application",type:"string",description:"Application's id",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly updated webcheck",optional:!1}],name:"update",longname:"Bucket.webcheck.update",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck",type:"DELETE"},params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"204",description:"successfuly deleted webcheck",optional:!1}],name:"delete",longname:"Bucket.webcheck.delete",scope:"route",authentication:!1}],default:[{route:{name:"/api/bucket/:id/feedback",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"feedback",type:"string",description:"the feedback text",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing feedback field",optional:!1},{type:"200",description:"succesfully registered the feedback",optional:!1}],response:[{name:"feedback",type:"string",description:"the feedback that hasn't been registered",optional:!1,defaultvalue:null}],name:"sendFeedback",longname:"Bucket.sendFeedback",scope:"route"},{name:"retrieveUsers",route:{name:"/api/bucket/:id/users_authorized",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved bucket's members",optional:!1}],response:[{name:".",type:"array",description:"a array of user containing their email, username and roles",optional:!1,defaultvalue:null}],longname:"Bucket.retrieveUsers",scope:"route"},{name:"currentRole",route:{name:"/api/bucket/:id/current_role",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"200",description:"succesfully retrieved the use role",optional:!1}],response:[{name:"role",type:"string",description:"the user role",optional:!1,defaultvalue:null}],longname:"Bucket.currentRole",scope:"route"},{route:{name:"/api/bucket/:id/manage_notif",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the user email",optional:!1,defaultvalue:null},{name:"state",type:"string",description:"the notification state you want to set for that user\n (either 'email' or 'nonde)",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"user not found",optional:!1}],response:[{name:".",type:"array",description:"array of state for each user",optional:!1,defaultvalue:null}],name:"setNotificationState",longname:"Bucket.setNotificationState",scope:"route"},{name:"inviteUser",route:{name:"/api/bucket/:id/add_user",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the email of the user",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"403",description:"you cant invit more users because you hit the bucket limit",optional:!1},{type:"200",description:"succesfully invited the user (either directly or by email)",optional:!1}],response:[{name:"invitations",type:"array",description:"the list of invitations actually active",optional:!1,defaultvalue:null}],longname:"Bucket.inviteUser",scope:"route"},{route:{name:"/api/bucket/:id/invitation",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"email",type:"string",description:"the email of the invitation you want to delete",optional:!0,defaultvalue:null}],code:[{type:"400",description:"invalid/missing parameters",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted the invitation",optional:!1}],response:[{name:"invitations",type:"array",description:"the list of invitations actually active",optional:!1,defaultvalue:null}],name:"removeInvitation",longname:"Bucket.removeInvitation",scope:"route"},{route:{name:"/api/bucket/:id/remove_user",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the email of the user you want to remove",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"404",description:"user not found",optional:!1},{type:"403",description:"impossible to remove the owner from the bucket",optional:!1},{type:"500",description:"database error",optional:!1}],response:[{name:".",type:"array",description:"a array of user containing their email, username and roles",optional:!1,defaultvalue:null}],name:"removeUser",longname:"Bucket.removeUser",scope:"route"},{route:{name:"/api/bucket/:id/promote_user",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the email of the user you want to change the role",optional:!1,defaultvalue:null},{name:"role",type:"string",description:"the role you want to set",optional:!1,defaultvalue:null}],code:[{type:"400",description:"invalid/missing parameters",optional:!1},{type:"404",description:"user not found",optional:!1},{type:"403",description:"impossible to set the role of the owner",optional:!1}],response:[{name:".",type:"array",description:"a array of user containing their email, username and roles",optional:!1,defaultvalue:null}],name:"setUserRole",longname:"Bucket.setUserRole",scope:"route"},{name:"retrieveAll",route:{name:"/api/bucket/",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully fetched bucket",optional:!1}],response:[{name:".",type:"array",description:"array of buckets",optional:!1,defaultvalue:null}],longname:"Bucket.retrieveAll",scope:"route"},{name:"create",route:{name:"/api/bucket/create_classic",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"name",type:"string",description:"the name of the bucket",optional:!1,defaultvalue:null},{name:"comment",type:"string",description:"any comments that will be written under the bucket name",optional:!0,defaultvalue:null},{name:"app_url",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"403",description:"you cant create any more bucket",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully created a bucket",optional:!1}],response:[{name:"bucket",type:"object",description:"the created bucket",optional:!1,defaultvalue:null}],longname:"Bucket.create",scope:"route"},{deprecated:!0,route:{name:"/api/bucket/:id/start_trial",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"400",description:"can't claim trial",optional:!1},{type:"200",description:"trial launched",optional:!1}],response:[{name:"duration",type:"string",description:"the duration of the trial",optional:!1,defaultvalue:null},{name:"plan",type:"string",description:"the plan of the trial",optional:!1,defaultvalue:null}],name:"claimTrial",longname:"Bucket.claimTrial",scope:"route"},{deprecated:!0,name:"upgrade",route:{name:"/api/bucket/:id/upgrade",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"plan",type:"string",description:"name of the plan to upgrade to",optional:!1,defaultvalue:null},{name:"stripe_token",type:"string",description:"a card token created by stripe",optional:!0,defaultvalue:null},{name:"coupon_id",type:"string",description:"the id of the stripe coupon",optional:!0,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"403",description:"need a credit card OR not allowed to subscribe to the plan",optional:!1},{type:"500",description:"stripe/database error",optional:!1},{type:"200",description:"succesfully upgraded",optional:!1}],response:[{name:"bucket",type:"object",description:"the bucket object",optional:!1,defaultvalue:null},{name:"subscription",type:"object",description:"the subscription object attached to the subscription",optional:!1,defaultvalue:null}],longname:"Bucket.upgrade",scope:"route"},{name:"retrieve",route:{name:"/api/bucket/:id",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"200",description:"succesfully retrieved the bucket",optional:!1}],response:[{name:".",type:"object",description:"bucket object",optional:!1,defaultvalue:null}],longname:"Bucket.retrieve",scope:"route"},{route:{name:"/api/bucket/:id",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"",optional:!0,defaultvalue:null},{name:"comment",type:"string",description:"",optional:!0,defaultvalue:null},{name:"app_url",type:"string",description:"",optional:!0,defaultvalue:null},{name:"configuration",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"object",description:"bucket object",optional:!1,defaultvalue:null}],name:"update",longname:"Bucket.update",scope:"route",async:!0},{name:"retrieveServers",route:{name:"/api/bucket/:id/meta_servers",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved the server's metadata",optional:!1}],response:[{name:".",type:"array",description:"servers metadata",optional:!1,defaultvalue:null}],longname:"Bucket.retrieveServers",scope:"route"},{route:{name:"/api/bucket/:id",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted the bucket",optional:!1}],response:[{name:".",type:"object",description:"the deleted bucket",optional:!1,defaultvalue:null}],name:"destroy",longname:"Bucket.destroy",scope:"route"},{route:{name:"/api/bucket/:id/transfer_ownership",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"new_owner",type:"string",description:"the wanted owner's email",optional:!1,defaultvalue:null}],code:[{type:"400",description:"Missing/invalid parameters",optional:!1},{type:"404",description:"user not found",optional:!1},{type:"403",description:"the new owner need to have a active credit card",optional:!1},{type:"200",description:"succesfully transfered the bucket, old owner is now admin",optional:!1}],response:[{name:".",type:"object",description:"bucket object",optional:!1,defaultvalue:null}],name:"transferOwnership",longname:"Bucket.transferOwnership",scope:"route"},{route:{name:"/api/bucket/:id/user_options",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"options",type:"object",description:"user options",optional:!1,defaultvalue:null}],code:[{type:"200",description:"succesfully update user options",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:"bucket",type:"object",description:"",optional:!1,defaultvalue:null}],name:"updateUserOptions",longname:"Bucket.updateUserOptions",scope:"route"}]},dashboard:[{route:{name:"/api/bucket/:id/dashboard/",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of servers status",optional:!1,defaultvalue:null}],name:"retrieveAll",longname:"Dashboard.retrieveAll",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/:dashid",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashid",type:"string",description:"dashboard id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"404",description:"dashboard not found",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of dashboards",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Dashboard.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/:dashid",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashid",type:"string",description:"dashboard id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted dashboard",optional:!1},{type:"400",description:"Invalid params",optional:!1},{type:"404",description:"dashboard not found",optional:!1}],response:[{name:".",type:"array",description:"array of dashboards",optional:!1,defaultvalue:null}],name:"remove",longname:"Dashboard.remove",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/:dashId",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashId",type:"string",description:"dashboard id",optional:!1}],body:[{name:"name",type:"string",description:"the name of the dashboard",optional:!1,defaultvalue:null},{name:"children",type:"object",description:"the list of component that compose the dashboard",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"404",description:"dashboard not found",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of servers status",optional:!1,defaultvalue:null}],name:"update",longname:"Dashboard.update",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"the name of the dashboard",optional:!1,defaultvalue:null},{name:"children",type:"object",description:"the list of component that compose the dashboard",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully created dashboard",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"dashboard",description:"complete dashboard object from database",optional:!1,defaultvalue:null}],name:"create",longname:"Dashboard.create",scope:"route"}],data:{dependencies:[{route:{name:"/api/bucket/:id/data/dependencies/",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"the application name",optional:!1,defaultvalue:null},{name:"server_name",type:"string",description:"filter by server name",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"array",description:"recorded dependencies",optional:!1,defaultvalue:null}],examples:["km.data.dependencies.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"],name:"retrieve",longname:"Data.dependencies.retrieve",scope:"route"}],events:[{route:{name:"/api/bucket/:id/data/events",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"event_name",type:"string",description:"the event name to retrieve",optional:!1,defaultvalue:null},{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null},{name:"limit",type:"number",description:"limit the number of events to retrieve",optional:!0,defaultvalue:100},{name:"offset",type:"number",description:"offset research by X",optional:!0,defaultvalue:0}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of events",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Data.events.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/eventsKeysByApp",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object representing events emitted for each application name",optional:!1,defaultvalue:null}],name:"retrieveMetadatas",longname:"Data.events.retrieveMetadatas",scope:"route"},{route:{name:"/api/bucket/:id/data/events/stats",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"event_name",type:"string",description:"the event name to retrieve",optional:!1,defaultvalue:null},{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null},{name:"days",type:"number",description:"limit the number of days of data",optional:!0,defaultvalue:2},{name:"interval",type:"string",description:"interval of time between two point",optional:!0,defaultvalue:"minute"}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of point (each point is one dimensional array, X are at 0 and Y at 1)",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.events.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/events/delete_all",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted data",optional:!1}],response:[{name:".",type:"array",description:"array of object representing events emitted for each application name",optional:!1,defaultvalue:null}],name:"deleteAll",longname:"Data.events.deleteAll",scope:"route"}],exceptions:[{route:{name:"/api/bucket/:id/data/exceptions",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"filter exceptions by server source",optional:!0,defaultvalue:null},{name:"app_name",type:"string",description:"filter exceptions by app source",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"filter out exceptions older than X (in minutes)",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of exceptions",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Data.exceptions.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/exceptions/summary",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"retrieveSummary",longname:"Data.exceptions.retrieveSummary",scope:"route"},{route:{name:"/api/bucket/:id/data/exceptions/delete_all",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"deleteAll",longname:"Data.exceptions.deleteAll",scope:"route"},{route:{name:"/api/bucket/:id/data/exceptions/delete",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"identifier",type:"string",description:"exception identifier",optional:!0,defaultvalue:null},{name:"app_name",type:"string",description:"the application on which exception happened",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"the server on which exception happened",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing/invalid parameters",optional:!1}],response:[{name:".",type:"array",description:"array of deleted exceptions",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.exceptions.delete",scope:"route"}],issues:[{route:{name:"/api/bucket/:id/data/issues/list",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"filter exceptions by server source",optional:!0,defaultvalue:null},{name:"app_name",type:"string",description:"filter exceptions by app source needed if initiator+source not provided",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"exclude exceptions older than 'before' minutes",optional:!0,defaultvalue:null},{name:"initiator",type:"string",description:"filter exceptions by initiator (node/golang/browser/webcheck...) needed with source",optional:!0,defaultvalue:null},{name:"source",type:"string",description:"filter exceptions by source (browser app id, webcheck id...)",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"array of string to filter tags",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of exceptions",optional:!1,defaultvalue:null}],name:"list",longname:"Data.issues.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/occurrences/:identifier",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":identifier",type:"string",description:"issue identifier",optional:!1}],query:[{name:"from",type:"number",description:"",optional:!0,defaultvalue:null},{name:"search_after",type:"number",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of occurrences id",optional:!1,defaultvalue:null}],name:"listOccurencesForIdentifier",longname:"Data.issues.listOccurencesForIdentifier",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/replay/:uuid",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":uuid",type:"string",description:"replay id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"replay",type:"string",description:"",optional:!1,defaultvalue:null}],name:"getReplay",longname:"Data.issues.getReplay",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"a specific app name",optional:!0,defaultvalue:null},{name:"start",type:"string",description:"ignore issue before this date",optional:!0,defaultvalue:null},{name:"identifier",type:"string",description:"a specific issue identifier",optional:!0,defaultvalue:null},{name:"interval",type:"string",description:"ignore issue before this date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"ignore issue after this date",optional:!0,defaultvalue:null},{name:"includeFixed",type:"boolean",description:"choose to ignore or not the fixed occurences",optional:!0,defaultvalue:!1},{name:"initiator",type:"string",description:"filter exceptions by initiator (node/golang/browser/webcheck...) needed with source",optional:!0,defaultvalue:null},{name:"source",type:"string",description:"filter exceptions by source (browser app id, webcheck id...)",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"array of string to filter tags",optional:!0,defaultvalue:null},{name:"includeEmptyDocs",type:"boolean",description:"add empty docs",optional:!0,defaultvalue:!1},{name:"invertedTags",type:"boolean",description:"filter issue without tags",optional:!0,defaultvalue:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.issues.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/issues/ocurrences",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"identifier",type:"object",description:"find occurence by using an issue identifier",optional:!0,defaultvalue:null},{name:"occurrence_id",type:"object",description:"find ocurrence by his id",optional:!0,defaultvalue:null},{name:"includeFixed",type:"boolean",description:"choose to ignore or not the fixed occurences",optional:!0,defaultvalue:!1},{name:"limit",type:"number",description:"limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing ocurrences",optional:!1,defaultvalue:null}],name:"findOccurences",longname:"Data.issues.findOccurences",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/search",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"message",type:"string",description:"find occurence that match this message",optional:!1,defaultvalue:null},{name:"includeFixed",type:"boolean",description:"choose to ignore or not the fixed occurences",optional:!0,defaultvalue:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"search",longname:"Data.issues.search",scope:"route"},{route:{name:"/api/bucket/:id/data/issues/summary/:aggregateBy",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":aggregateBy",type:"string",description:"servers, apps, initiators or sources",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"issues count aggregated",optional:!1,defaultvalue:null}],name:"summary",longname:"Data.issues.summary",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"app_name",type:"string",description:"an specific application to delete application",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"deleteAll",longname:"Data.issues.deleteAll",scope:"route"},{route:{name:"/api/bucket/:id/data/issues/:identifier",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":identifier",type:"string",description:"the identifier of issue that you want to delete",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing/invalid parameters",optional:!1}],response:[{name:".",type:"array",description:"array of deleted exceptions",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.issues.delete",scope:"route"}],logs:[{route:{name:"/api/bucket/:id/data/logs",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"the application name",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter by server name",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"only search log oldest than <before>",optional:!0,defaultvalue:null},{name:"after",type:"string",description:"only search log newer than <after>",optional:!0,defaultvalue:null},{name:"size",type:"integer",description:"line limit, default to 100",optional:!0,defaultvalue:null},{name:"from",type:"integer",description:"offset limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"array",description:"recorded dependencies",optional:!1,defaultvalue:null}],examples:["km.data.logs.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"],name:"retrieve",longname:"Data.logs.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/logs/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"object",description:"a specific app name",optional:!0,defaultvalue:null},{name:"start",type:"object",description:"ignore log before this date",optional:!0,defaultvalue:null},{name:"interval",type:"object",description:"ignore log before this date",optional:!0,defaultvalue:null},{name:"end",type:"object",description:"ignore log after this date",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.logs.retrieveHistogram",scope:"route"}],metrics:[{route:{name:"/api/bucket/:id/data/metrics/aggregations",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"aggregations",type:"object",description:"array of aggregations to compute",optional:!1,defaultvalue:null},{name:"aggregations[].start",type:"date",description:"oldest documents to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].end",type:"date",description:"newest documents to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].interval",type:"number",description:"interval between two points",optional:!0,defaultvalue:null},{name:"aggregations[].name",type:"string",description:"the name of metric to compute the graph",optional:!0,defaultvalue:null},{name:"aggregations[].servers",type:"array",description:"filter source server to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].apps",type:"array",description:"filter source applications to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].initiator",type:"string",description:"filter source initiator to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].webcheck",type:"string",description:"filter source webcheck to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].collector",type:"string",description:"filter source collector to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].tags",type:"array",description:"filter tags to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].types",type:"array",description:"type of aggregation",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"aggregations",optional:!1,defaultvalue:null}],examples:["km.data.metrics.retrieveAggregations(bucket._id, {\n  aggregations: [\n    {\n     'end': 'now-1d',\n     'apps': ['INTERACTION', 'WEB-API', 'WORKER'],\n     'types': ['histogram', 'apps', 'servers'],\n     'name': 'HTTP'\n    }\n  ]\n})"],name:"retrieveAggregations",longname:"Data.metrics.retrieveAggregations",scope:"route"},{route:{name:"/api/bucket/:id/data/metrics/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter probes by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter probes by server source",optional:!0,defaultvalue:null},{name:"interval",type:"string",description:"interval of time between two point",optional:!0,defaultvalue:"minute"},{name:"before",type:"string",description:"filter out probes that are after X minute",optional:!0,defaultvalue:60}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"server_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name.metrics",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name.metrics.agg_type",type:"string",description:"the type of aggregation for this probe",optional:!1,defaultvalue:null},{name:"server_name.app_name.metrics_name.timestamps_and_stats",type:"array",description:"array of point",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.metrics.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/metrics/list",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"servers",type:"object",description:"filter metrics by app name",optional:!0,defaultvalue:null},{name:"apps",type:"object",description:"filter metrics by server name",optional:!0,defaultvalue:null},{name:"initiator",type:"string",description:"filter metrics by a specific initiator",optional:!0,defaultvalue:null},{name:"source",type:"string",description:"filter metrics by a specific source",optional:!0,defaultvalue:null},{name:"collector",type:"string",description:"filter metrics by a specific collector",optional:!0,defaultvalue:null},{name:"webcheck",type:"string",description:"filter metrics by a specific webcheck",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"retrieveList",longname:"Data.metrics.retrieveList",scope:"route"},{route:{name:"/api/bucket/:id/data/metrics",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter metrics by app source",optional:!1,defaultvalue:null},{name:"server_name",type:"string",description:"filter metrics by server source",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"filter out metrics that are after X minute",optional:!0,defaultvalue:720}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"retrieveMetadatas",longname:"Data.metrics.retrieveMetadatas",scope:"route"}],notifications:[{route:{name:"/api/bucket/:id/data/notifications",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"type",type:"string",description:"Type of notification",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"we search logs before this date (lower than)",optional:!0,defaultvalue:null},{name:"after",type:"string",description:"we search logs after this date (greater than)",optional:!0,defaultvalue:null},{name:"size",type:"number",description:"",optional:!0,defaultvalue:null},{name:"from",type:"number",description:"",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"type of notification",optional:!0,defaultvalue:null},{name:"providers",type:"array",description:"find notifications with this providers",optional:!0,defaultvalue:null},{name:"contacts",type:"array",description:"find notifications with this contact",optional:!0,defaultvalue:null},{name:"size",type:"integer",description:"line limit, default to 20",optional:!0,defaultvalue:null},{name:"from",type:"integer",description:"offset limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of traces",value:"{Array} . array of traces",optional:!1,type:null}],name:"list",longname:"Data.notifications.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/notifications/:notification",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":notification",type:"string",description:"notification id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Object} . notification",value:"{Object} . notification",optional:!1,type:null}],name:"retrieve",longname:"Data.notifications.retrieve",scope:"route",async:!0}],outliers:[{route:{name:"/api/bucket/:id/data/outliers/",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"the application name",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter by server name",optional:!0,defaultvalue:null},{name:"start",type:"string",description:"only search outlier newer than <start>",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"only search outlier older than <end>",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"array",description:"recorded dependencies",optional:!1,defaultvalue:null}],examples:["km.data.outliers.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"],name:"retrieve",longname:"Data.outliers.retrieve",scope:"route"}],processes:[{route:{name:"/api/bucket/:id/data/processEvents",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"filter out events that are after X minute",optional:!0,defaultvalue:60}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of process events",optional:!1,defaultvalue:null}],name:"retrieveEvents",longname:"Data.processes.retrieveEvents",scope:"route"},{route:{name:"/api/bucket/:id/data/processEvents/deployments",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of deployments",optional:!1,defaultvalue:null}],name:"retrieveDeployments",longname:"Data.processes.retrieveDeployments",scope:"route"}],profiling:[{route:{name:"/api/bucket/:id/data/profilings/:filename",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":filename",type:"string",description:"filename",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1}],response:[{name:".",type:"object",description:"return profile data",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Data.profiling.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/profilings/:filename/download",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":filename",type:"string",description:"filename",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1}],response:[{name:".",type:"file",description:"return a file",optional:!1,defaultvalue:null}],name:"download",longname:"Data.profiling.download",scope:"route"},{route:{name:"/api/bucket/:id/data/profilings",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"apps",type:"object",description:"",optional:!0,defaultvalue:null},{name:"servers",type:"object",description:"",optional:!0,defaultvalue:null},{name:"from",type:"object",description:"",optional:!0,defaultvalue:null},{name:"size",type:"object",description:"",optional:!0,defaultvalue:null},{name:"type",type:"object",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing profilings",optional:!1,defaultvalue:null}],name:"list",longname:"Data.profiling.list",scope:"route"},{route:{name:"/api/bucket/:id/data/profilings/:filename",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":filename",type:"string",description:"filename",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1}],response:[{name:".",type:"file",description:"return a file",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.profiling.delete",scope:"route"}],status:[{route:{name:"/api/bucket/:id/data/status",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of servers status",value:"{Array} . array of servers status",optional:!1,type:null}],name:"retrieve",longname:"Data.status.retrieve",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/status/blacklisted",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of servers status",value:"{Array} . array of servers status",optional:!1,type:null}],name:"retrieveBlacklisted",longname:"Data.status.retrieveBlacklisted",scope:"route",async:!0}],traces:[{route:{name:"/api/bucket/:id/data/traces",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"includeSpans",type:"boolean",description:"",optional:!0,defaultvalue:!0},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"limit",type:"string",description:"default: 10, max: 100",optional:!0,defaultvalue:null},{name:"kind",type:"string",description:"",optional:!0,defaultvalue:null},{name:"minDuration",type:"number",description:"",optional:!0,defaultvalue:null},{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"Query string array like [error=500, error, ...]",optional:!0,defaultvalue:null},{name:"orderBy",type:"string",description:"Default: newest, enum: oldest, newest, shortest, longest",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of traces",value:"{Array} . array of traces",optional:!1,type:null}],name:"list",longname:"Data.traces.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/:trace",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":trace",type:"string",description:"trace id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Object} . trace",value:"{Object} . trace",optional:!1,type:null}],name:"retrieve",longname:"Data.traces.retrieve",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/services",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"services,",type:"object",description:"spans names",optional:!1,defaultvalue:null}],name:"getServices",longname:"Data.traces.getServices",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/tags",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"tags",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getTags",longname:"Data.traces.getTags",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/histogram/tag",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"tag",type:"string",description:"",optional:!1,defaultvalue:null},{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"aggregation",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getHistogramByTag",longname:"Data.traces.getHistogramByTag",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/aggregation/tag",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"tag",type:"string",description:"",optional:!1,defaultvalue:null},{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"aggregation",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getTagsValue",longname:"Data.traces.getTagsValue",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/aggregation/duration",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"aggregation",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getDurationAvg",longname:"Data.traces.getDurationAvg",scope:"route",async:!0}],transactions:[{route:{name:"/api/bucket/:id/data/transactions/v2/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter transactions by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter transactions by server source",optional:!0,defaultvalue:null},{name:"interval",type:"string",description:"interval of time between two point",optional:!0,defaultvalue:"minute"},{name:"before",type:"string",description:"filter out transactions that are after X minute",optional:!0,defaultvalue:60}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of times series containing points",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.transactions.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/transactions/v2/summary",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter transactions by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter transactions by server source",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"server_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name",type:"object",description:"transaction object",optional:!1,defaultvalue:null}],name:"retrieveSummary",longname:"Data.transactions.retrieveSummary",scope:"route"},{route:{name:"/api/bucket/:id/data/transactions/v2/delete",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"app_name",type:"string",description:"filter transactions by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter transactions by server source",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"server_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name",type:"object",description:"transaction object",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.transactions.delete",scope:"route"}]},misc:[{route:{name:"/api/misc/changelog",type:"GET"},code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:"changelog",type:"array",description:"articles",optional:!1,defaultvalue:null}],name:"listChangelogArticles",longname:"Misc.listChangelogArticles",scope:"route",params:[],authentication:!1},{route:{name:"/api/misc/release/pm2",type:"GET"},code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:"pm2_version",type:"string",description:"latest version",optional:!1,defaultvalue:null}],name:"retrievePM2Version",longname:"Misc.retrievePM2Version",scope:"route",params:[],authentication:!1},{route:{name:"/api/misc/release/nodejs/:version",type:"GET"},params:[{name:":version",type:"string",description:"semver version range",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of releases matching the range requested",optional:!1,defaultvalue:null}],name:"retrieveNodeRelease",longname:"Misc.retrieveNodeRelease",scope:"route",authentication:!1},{route:{name:"/api/misc/plans",type:"GET"},code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"object",description:"list of plans keyed by plan name",optional:!1,defaultvalue:null}],name:"retrievePlans",longname:"Misc.retrievePlans",scope:"route",params:[],authentication:!1},{route:{name:"/api/misc/stripe/retrieveCoupon",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"coupon",type:"string",description:"the coupon name",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:"coupon",type:"object",description:"the coupon object",optional:!1,defaultvalue:null}],name:"retrieveCoupon",longname:"Misc.retrieveCoupon",scope:"route",params:[]},{route:{name:"/api/misc/stripe/retrieveCompany",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"vat_id",type:"string",description:"the vat id of the company",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:".",type:"object",description:"metadata about company",optional:!1,defaultvalue:null}],name:"retrieveCompany",longname:"Misc.retrieveCompany",scope:"route",params:[]},{route:{name:"/api/misc/stripe/retrieveVat",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"country",type:"string",description:"country code of the user",optional:!0,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:"coupon",type:"object",description:"the coupon object",optional:!1,defaultvalue:null}],name:"retrieveVAT",longname:"Misc.retrieveVAT",scope:"route",params:[]}],node:[{route:{name:"/api/node/default",type:"GET"},response:[{name:"node",type:"object",description:"Return node object",optional:!1,defaultvalue:null}],name:"getDefaultNode",longname:"Node.getDefaultNode",scope:"route",params:[],authentication:!1}],orchestration:[{route:{name:"/api/bucket/:id/balance",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"balancing error",optional:!1},{type:"403",description:"already on new node or not premium",optional:!1},{type:"200",description:"succesfully balanced the bucket",optional:!1}],response:[{name:"migration",type:"object",description:"is equal true if succesfull",optional:!1,defaultvalue:null}],name:"selfSend",longname:"Orchestration.selfSend",scope:"route",params:[]}],tokens:[{route:{name:"/api/users/token/",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfully retrieved",optional:!1}],response:[{name:".",type:"object",description:"array of tokens",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Tokens.retrieve",scope:"route",params:[]},{route:{name:"/api/users/token/:id",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"token id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"token not found",optional:!1},{type:"200",description:"refresh token has been deleted and all access token that have been created with it",optional:!1}],response:[{name:".",type:"object",description:"array of tokens",optional:!1,defaultvalue:null}],name:"remove",longname:"Tokens.remove",scope:"route"},{route:{name:"/api/users/token/",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"scope",type:"object",description:"a valid oauth scope",optional:!1,defaultvalue:null}],code:[{type:"409",description:"the otp is already enabled for the user, you can only delete it",optional:!1},{type:"200",description:"the otp can be registered for the account, return the full response",optional:!1}],response:[{name:".",type:"object",description:"generated token",optional:!1,defaultvalue:null}],name:"create",longname:"Tokens.create",scope:"route",params:[]}],user:{otp:[{route:{name:"/api/users/otp",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"409",description:"the otp is already enabled for the user, you can only delete it",optional:!1},{type:"200",description:"the otp can be registered for the account, return the full response",optional:!1}],response:[{name:"user",type:"object",description:"user model",optional:!1,defaultvalue:null},{name:"key",type:"string",description:"otp secret key",optional:!1,defaultvalue:null},{name:"qrImage",type:"string",description:"url to the QrCode",optional:!1,defaultvalue:null}],name:"retrieve",longname:"User.otp.retrieve",scope:"route",params:[]},{route:{name:"/api/users/otp",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"otpKey",type:"string",description:"secret key used to generate OTP code",optional:!1,defaultvalue:null},{name:"otpToken",type:"string",description:"a currently valid OTP code generated with the otpKey",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"403",description:"the code asked to add the OTP from user account is invalid",optional:!1},{type:"500",description:"error from database",optional:!1},{type:"200",description:"the otp has been registered for the user",optional:!1}],name:"enable",longname:"User.otp.enable",scope:"route",params:[]},{route:{name:"/api/users/otp",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],query:[{name:"otpToken",type:"string",description:"a currently valid OTP code",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"403",description:"the code asked to remove the OTP from user account is invalid",optional:!1},{type:"500",description:"error from database",optional:!1},{type:"200",description:"the otp has been deleted for the user",optional:!1}],name:"disable",longname:"User.otp.disable",scope:"route",params:[]}],providers:[{route:{name:"/api/users/integrations",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"200",description:"succesfully retrieved providers",optional:!1}],response:[{name:".",type:"array",description:"array of providers for user account",optional:!1,defaultvalue:null}],name:"retrieve",longname:"User.providers.retrieve",scope:"route",params:[]},{route:{name:"/api/users/integrations",type:"POST"},authentication:!0,body:[{name:"name",type:"string",description:"the provider name",optional:!1,defaultvalue:null}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"400",description:"invalid parameters",optional:!1},{type:"403",description:"the user already have this provider",optional:!1},{type:"200",description:"succesfully added the provider",optional:!1}],name:"add",longname:"User.providers.add",scope:"route",params:[]},{route:{name:"/api/users/integrations/:name",type:"DELETE"},authentication:!0,params:[{name:":name",type:"string",description:"the provider name",optional:!1}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"400",description:"invalid parameters or provider isn't implemented",optional:!1},{type:"403",description:"the provider isn't enabled",optional:!1},{type:"200",description:"succesfully removed the provider",optional:!1}],name:"remove",longname:"User.providers.remove",scope:"route"}],default:[{name:"retrieve",route:{name:"/api/users/isLogged",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"200",description:"the user has been retrieved",optional:!1}],response:[{name:"user",type:"object",description:"user model",optional:!1,defaultvalue:null}],longname:"User.retrieve",scope:"route"},{route:{name:"/api/users/show/:id",type:"GET"},params:[{name:":id",type:"string",description:"user id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters (no id provided)",optional:!1},{type:"404",description:"no user account where found",optional:!1},{type:"200",description:"the mail has been sent to the provided email",optional:!1}],response:[{name:"String",type:"",description:"email user email",optional:!1,defaultvalue:null},{name:"String",type:"",description:"username user pseudo",optional:!1,defaultvalue:null}],name:"show",longname:"User.show",scope:"route",authentication:!1},{route:{name:"/api/users/update",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"username",type:"string",description:"",optional:!0,defaultvalue:null},{name:"email",type:"string",description:"",optional:!0,defaultvalue:null},{name:"old_password",type:"string",description:"",optional:!0,defaultvalue:null},{name:"new_password",type:"string",description:"",optional:!0,defaultvalue:null},{name:"info",type:"object",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters, no data to update",optional:!1},{type:"403",description:"when updating the password, it need a new one",optional:!1},{type:"406",description:"when updating the password, the old one is false",optional:!1},{type:"409",description:"when updating email or username\n another user already have one of those two",optional:!1},{type:"200",description:"succesfully updated the card",optional:!1}],response:[{name:".",type:"object",description:"user object",optional:!1,defaultvalue:null}],name:"update",longname:"User.update",scope:"route",params:[]},{route:{name:"/api/users/delete",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"403",description:"permission denied (hold buckets)",optional:!1},{type:"200",description:"succesfully deleted the user",optional:!1}],response:[{name:".",type:"object",description:"user object",optional:!1,defaultvalue:null}],name:"delete",longname:"User.delete",scope:"route",params:[]}]},auth:[{name:"retrieveToken",route:{name:"/api/oauth/token",type:"POST"},service:{name:"OAUTH"},body:[{name:"client_id",type:"string",description:"the public id of your oauth application",optional:!1,defaultvalue:null},{name:"refresh_token",type:"string",description:"refresh token you retrieved via authorize endpoint",optional:!1,defaultvalue:null},{name:"grant_type",type:"string",description:"",optional:!1,defaultvalue:"refresh_token"}],code:[{type:"400",description:"invalid parameters (missing or not correct)",optional:!1}],response:[{name:"access_token",type:"string",description:"a fresh access_token",optional:!1,defaultvalue:null},{name:"refresh_token",type:"string",description:"the refresh token you used",optional:!1,defaultvalue:null},{name:"expire_at",type:"string",description:"UTC date at which the token will be considered\n as invalid",optional:!1,defaultvalue:null},{name:"token_type",type:"string",description:"the type of token to use, for now its always Bearer",optional:!1,defaultvalue:null}],longname:"Auth.retrieveToken",scope:"route",authentication:!1},{name:"requestNewPassword",route:{name:"/api/oauth/reset_password",type:"POST"},service:{name:"OAUTH"},body:[{name:"email",type:"string",description:"email of the account that want a password reset",optional:!1,defaultvalue:null}],code:[{type:"500",description:"the database failed to register the token to reset the mail",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"404",description:"no user account where found with the provided email",optional:!1},{type:"200",description:"the mail has been sent to the provided email",optional:!1}],longname:"Auth.requestNewPassword",scope:"route",authentication:!1},{name:"sendEmailLink",route:{name:"/api/oauth/send_email_link",type:"POST"},service:{name:"OAUTH"},code:[{type:"500",description:"the database failed to register the token to reset the mail",optional:!1},{type:"401",description:"need to authenticated",optional:!1},{type:"200",description:"the mail has been sent to the provided email",optional:!1}],longname:"Auth.sendEmailLink",scope:"route",authentication:!1},{name:"validEmail",route:{name:"/api/oauth/valid_email/:token",type:"GET"},params:[{description:"the token to validate the account",name:":token",optional:!1,type:null}],service:{name:"OAUTH"},code:[{type:"500",description:"the database failed to valid email",optional:!1},{type:"404",description:"need to authenticated",optional:!1},{type:"301",description:"the email has been valided",optional:!1}],longname:"Auth.validEmail",scope:"route",authentication:!1},{route:{name:"/api/oauth/register",type:"GET"},service:{name:"OAUTH"},body:[{name:"username",type:"string",description:"",optional:!1,defaultvalue:null},{name:"email",type:"string",description:"",optional:!1,defaultvalue:null},{name:"password",type:"string",description:"",optional:!1,defaultvalue:null},{name:"role",type:"string",description:"job title in user company",optional:!0,defaultvalue:null},{name:"company",type:"string",description:"company name",optional:!0,defaultvalue:null},{name:"accept_terms",type:"integer",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"either the registeration of new user is disabled or\nthe database failed to register the user",optional:!1},{type:"409",description:"the user field are already used by another user",optional:!1},{type:"200",description:"the user has been created",optional:!1}],response:[{name:"user",type:"object",description:"user model",optional:!1,defaultvalue:null},{name:"access_token",type:"object",description:"access token issued for the user",optional:!1,defaultvalue:null},{name:"refreshToken",type:"object",description:"refresh token issued for the user",optional:!1,defaultvalue:null}],name:"register",longname:"Auth.register",scope:"route",authentication:!1},{route:{name:"/api/oauth/revoke",type:"POST"},service:{name:"OAUTH"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"404",description:"token not found",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"the token has been succesfully deleted,\n if there was access token generated with this token, they\n have been deleted too",optional:!1}],name:"revoke",longname:"Auth.revoke",scope:"route"}]}},{}],38:[function(e,t,n){"use strict";var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();var a=e("./strategy");t.exports=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,a),i(t,[{key:"removeUrlToken",value:function(e){var t="?access_token="+e+"&token_type=refresh_token",n=window.location.href.replace(t,"");window.history.pushState("","",n)}},{key:"retrieveTokens",value:function(t,n){var i=this,e=function(e){return t.auth.retrieveToken({client_id:i.client_id,refresh_token:e})},a=new URL(window.location);this.response_mode="query"===this.response_mode?"search":this.response_mode;var o=new URLSearchParams(a[this.response_mode]);null!==o.get("access_token")?e(o.get("access_token")).then(function(e){i.removeUrlToken(e.data.refresh_token),localStorage.setItem("km_refresh_token",o.get("access_token"));var t=e.data;return n(null,t)}).catch(n):"undefined"!=typeof localStorage&&null!==localStorage.getItem("km_refresh_token")?e(localStorage.getItem("km_refresh_token")).then(function(e){i.removeUrlToken(e.data.refresh_token);var t=e.data;return n(null,t)}).catch(n):window.location=""+this.oauth_endpoint+this.oauth_query+"&redirect_uri="+window.location}},{key:"deleteTokens",value:function(n){var i=this;return new Promise(function(e,t){return n.auth.revoke().then(function(e){return console.log("Token successfuly revoked!")}).catch(function(e){return console.error("Error when trying to revoke token: "+e.message)}),localStorage.removeItem("km_refresh_token"),setTimeout(function(e){window.location=""+i.oauth_endpoint+i.oauth_query},500),e()})}}]),t}()},{"./strategy":40}],39:[function(e,t,n){"use strict";var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();var a=e("./strategy");t.exports=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,a),i(t,[{key:"retrieveTokens",value:function(e,n){if(this._opts.refresh_token&&this._opts.access_token)return n(null,{access_token:this._opts.access_token,refresh_token:this._opts.refresh_token});if(!this._opts.refresh_token||!this._opts.client_id)throw new Error("If you want to use the standalone flow you need to provide either \n        a refresh and access token OR a refresh token and a client id");e.auth.retrieveToken({client_id:this._opts.client_id,refresh_token:this._opts.refresh_token}).then(function(e){var t=e.data;return n(null,t)}).catch(n)}},{key:"deleteTokens",value:function(e){return e.auth.revoke}}]),t}()},{"./strategy":40}],40:[function(n,e,t){"use strict";var a=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();var o=n("../../constants.js"),i=function(){function i(e){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this._opts=e,this.client_id=e.client_id||e.OAUTH_CLIENT_ID,!this.client_id)throw new Error("You must always provide a application id for any of the strategies");this.scope=e.scope||"all",this.response_mode=e.reponse_mode||"query";var t=null;e&&e.services&&(t=e.services.OAUTH||e.services.API);var n=o.services.OAUTH||o.services.API;this.oauth_endpoint=""+(t||n),"/"===this.oauth_endpoint[this.oauth_endpoint.length-1]&&"/"===o.OAUTH_AUTHORIZE_ENDPOINT[0]&&(this.oauth_endpoint=this.oauth_endpoint.substr(0,this.oauth_endpoint.length-1)),this.oauth_endpoint+=o.OAUTH_AUTHORIZE_ENDPOINT,this.oauth_query="?client_id="+e.client_id+"&response_mode="+this.response_mode+"&response_type=token&scope="+this.scope}return a(i,[{key:"retrieveTokens",value:function(){throw new Error("You need to implement a retrieveTokens function inside your strategy")}},{key:"deleteTokens",value:function(){throw new Error("You need to implement a deleteTokens function inside your strategy")}}],[{key:"implementations",value:function(e){var t={embed:{nodule:n("./embed_strategy"),condition:"node"},browser:{nodule:n("./browser_strategy"),condition:"browser"},standalone:{nodule:n("./standalone_strategy"),condition:null}};return e?t[e]:null}}]),i}();e.exports=i},{"../../constants.js":1,"./browser_strategy":38,"./embed_strategy":28,"./standalone_strategy":39}],41:[function(e,t,n){"use strict";var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();var r=e("./utils/validator"),s=e("debug")("kmjs:endpoint");t.exports=function(){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),Object.assign(this,e)}return i(t,[{key:"build",value:function(a){var o=this;return function(){var e=arguments,t=(new Error).stack.split("\n")[2];return t&&0<t.length&&s("Call to '"+o.route.name+"' from "+t.replace("    at ","")),new Promise(function(n,i){r.extract(o,Array.prototype.slice.call(e)).then(function(e){if(o.service&&o.service.baseURL){var t=o.service.baseURL;t="/"===t[t.length-1]?t.substr(0,t.length-1):t,e.url=t+e.url}a.request(e).then(n,i)}).catch(i)})}}}]),t}()},{"./utils/validator":45,debug:29}],42:[function(o,e,t){"use strict";var n=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();var r=o("./namespace"),s=o("../constants"),l=o("./network"),p=o("debug")("kmjs"),i=function(){function a(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),p("init keymetrics instance"),this.opts=Object.assign(s,e),p("init network client (http/ws)"),this._network=new l(this,this.opts);var t=e&&e.mappings?e.mappings:o("./api_mappings.json");p("Using mappings provided in "+(e&&e.mappings?"options":"package")),p("building namespaces");var n=new r(t,{name:"root",http:this._network,services:this.opts.services});for(var i in p("exposing namespaces"),n)"name"!==i&&"opts"!==i&&(this[i]=n[i]);p("attached namespaces : "+Object.keys(this)),this.realtime=this._network.realtime}return n(a,[{key:"use",value:function(e,t){var n=this;return p("using "+e+" authentication strategy"),this._network.useStrategy(e,t),this.auth.logout=function(){return n._network.oauth_flow.deleteTokens(n)},this}},{key:"apiDateLag",get:function(){return this._network.apiDateLag}}]),a}();e.exports=i},{"../constants":1,"./api_mappings.json":37,"./namespace":43,"./network":44,debug:29}],43:[function(e,t,n){"use strict";var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();var l=e("./endpoint"),p=e("debug")("kmjs:namespace");t.exports=function(){function r(e,t){for(var n in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),p("initialization namespace "+t.name),this.name=t.name,this.http=t.http,this.endpoints=[],this.namespaces=[],p("building namespace "+t.name),e){var i=e[n];if("object"!==(void 0===e?"undefined":s(e))||i.route)i.service&&t.services&&t.services[i.service.name]&&(i.service.baseURL=t.services[i.service.name]),this.addEndpoint(new l(i));else{if("default"===n){var a=new r(i,{name:n,http:this.http,services:t.services});for(var o in this.namespaces.push(a),a)"name"!==o&&"opts"!==o&&(this[o]=a[o]);continue}this.addNamespace(new r(i,{name:n,http:this.http,services:t.services}))}}0<this.namespaces.length&&p("namespace "+this.name+" contains namespaces : \n"+this.namespaces.map(function(e){return e.name}).join("\n")+"\n"),0<this.endpoints.length&&p("Namespace "+this.name+" contains endpoints : \n"+this.endpoints.map(function(e){return e.route.name}).join("\n")+"\n")}return i(r,[{key:"addNamespace",value:function(e){if(!e||e.name===this.name)throw new Error("A namespace must not have the same name as the parent namespace");if(!(e instanceof r))throw new Error("addNamespace only accept Namespace instance");this.namespaces.push(e),this[e.name]=e}},{key:"addEndpoint",value:function(e){if(!e||e.name===this.name)throw new Error("A endpoint must not have the same name as a namespace");if(!(e instanceof l))throw new Error("addNamespace only accept Namespace instance");this.endpoints.push(e),this[e.name]=e.build(this.http)}}]),r}()},{"./endpoint":41,debug:29}],44:[function(e,t,n){"use strict";var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();var r=e("axios"),s=e("./auth_strategies/strategy"),l=e("../constants"),c=e("debug")("kmjs:network"),p=e("debug")("kmjs:network:http"),d=e("debug")("kmjs:network:ws"),f=e("./utils/websocket"),u=e("eventemitter2"),m=e("async");t.exports=function(){function o(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),c("init network manager"),t.baseURL=t.services.API,this.opts=t,this.opts.maxRedirects=0,this.tokens={refresh_token:null,access_token:null},this.km=e,this._queue=[],this._axios=r.create(t),this._websockets=[],this._endpoints=new Map,this._bucketFilters=new Map,this.apiDateLag=0,this.realtime=new u({wildcard:!0,delimiter:":",newListener:!1,maxListeners:20});var n=this,i=this.realtime.on;this.realtime.on=function(){return n.editSocketFilters("push",arguments[0]),i.apply(n.realtime,arguments)};var a=this.realtime.off;this.realtime.off=function(){return n.editSocketFilters("remove",arguments[0]),a.apply(n.realtime,arguments)},this.realtime.subscribe=this.subscribe.bind(this),this.realtime.unsubscribe=this.unsubscribe.bind(this),this.authenticated=!1,this._setupDateLag()}return i(o,[{key:"_setupDateLag",value:function(){var i=this,t=function(e){if(e&&e.headers&&e.headers.date){var t=new Date(e.headers.date),n=new Date;t.setMilliseconds(0),n.setMilliseconds(0),i.apiDateLag=t-n}};this._axios.interceptors.response.use(function(e){return t(e),e},function(e){return t(e.response),Promise.reject(e)})}},{key:"_queueUpdater",value:function(){if(!1!==this.authenticated)for(0<this._queue.length&&c("Emptying requests queue (size: "+this._queue.length+")");0<this._queue.length;){var e=this._queue.shift();this.request(e.request).then(e.resolve,e.reject)}}},{key:"_resolveBucketEndpoint",value:function(t){var n=this;if(!t)return Promise.reject(new Error("Missing argument : bucketID"));if(!this._endpoints.has(t)){var e=this._axios.request({url:"/api/bucket/"+t,method:"GET",headers:{Authorization:"Bearer "+this.tokens.access_token}}).then(function(e){return e.data.node.endpoints.web}).catch(function(e){throw n._endpoints.delete(t),e});this._endpoints.set(t,e)}return this._endpoints.get(t)}},{key:"request",value:function(o){var r=this;return new Promise(function(n,i){m.series([function(e){return!0===r.authenticated||!1===o.authentication?e():(p("Queued request to "+o.url),r._queue.push({resolve:n,reject:i,request:o}),e(-1))},function(t){if(!o.url.match(/bucket\/[0-9a-fA-F]{24}/))return t();var e=o.url.split("/")[3];r._resolveBucketEndpoint(e).then(function(e){return o.baseURL=e,t()}).catch(t)},function(i){var a=function(e){return i(null,e)};p("Making request to "+o.url),o.headers||(o.headers={}),o.headers.Authorization="Bearer "+r.tokens.access_token,r._axios.request(o).then(a).catch(function(e){var n=e.response;if(n&&401!==n.status)return i(n);p("Got unautenticated response, buffering request from now ..."),r.authenticated=!1,p("Asking to the oauth flow to retrieve new tokens");var t=function(){r.oauth_flow.retrieveTokens(r.km,function(e,t){if(e)return p("Failed to retrieve new tokens : "+(e.message||e)),i(n);p("Succesfully retrieved new tokens"),r._updateTokens(null,t,function(e,t){return e?i(n):(p("Re-buffering call to "+o.url+" since authenticated now"),o.headers.Authorization="Bearer "+r.tokens.access_token,r._axios.request(o).then(a).catch(i))})})};if(o.url==r.opts.services.OAUTH+"/api/oauth/token")return setTimeout(t.bind(r),500);t()})}],function(e,t){if(-1!==e)return e?i(e):n(t[2])})})}},{key:"_updateTokens",value:function(e,t,n){var i=this;if(e)return console.error("Error while retrieving tokens:",e),this.oauth_flow.deleteTokens(this.km),console.error(e.response?e.response.data:e.stack);if(!t||!t.access_token||!t.refresh_token)throw new Error("Invalid tokens");this.tokens=t,p("Registered new access_token : "+t.access_token),this._websockets.forEach(function(e){return e.updateAuthorization(t.access_token)}),this._axios.defaults.headers.common.Authorization="Bearer "+t.access_token,this._axios.request({url:"/api/bucket",method:"GET",headers:{Authorization:"Bearer "+t.access_token}}).then(function(e){return p("Cached "+e.data.length+" buckets for current user"),i.authenticated=!0,i._queueUpdater(),"function"==typeof n?n(null,!0):null}).catch(function(e){return console.error("Error while retrieving buckets"),console.error(e.response?e.response.data:e),"function"==typeof n?n(e):null})}},{key:"useStrategy",value:function(e,t){if(t||(t={}),t.client_id||(t.client_id=this.opts.OAUTH_CLIENT_ID),"object"===(void 0===e?"undefined":a(e))){if(this.oauth_flow=e,!this.oauth_flow.retrieveTokens||!this.oauth_flow.deleteTokens)throw new Error("You must implement the Strategy interface to use it");return this.oauth_flow.retrieveTokens(this.km,this._updateTokens.bind(this))}if(void 0===s.implementations(e))throw new Error("The flow named "+e+" doesn't exist");var n=s.implementations(e);if(n.condition&&l.ENVIRONNEMENT!==n.condition)throw new Error("The flow "+e+" is reserved for "+n.condition+" environment");var i=n.nodule;return this.oauth_flow=new i(t),this.oauth_flow.retrieveTokens(this.km,this._updateTokens.bind(this))}},{key:"editSocketFilters",value:function(e,t){if(0===t.indexOf("**"))throw new Error("You need to provide a bucket public id.");var n=(t=t.split(":"))[0],i=t.slice(2).join(":"),a=this._websockets.find(function(e){return e.bucketPublic===n});this._bucketFilters.has(n)||this._bucketFilters.set(n,[]);var o=this._bucketFilters.get(n);"push"===e?o.push(i):o.splice(o.indexOf(i),1),a&&a.send(JSON.stringify({action:"sub",public_id:n,filters:Array.from(new Set(o))}))}},{key:"subscribe",value:function(p,e){var u=this;return new Promise(function(s,l){c("Request endpoints for "+p),u.km.bucket.retrieve(p).then(function(e){var i=e.data,t=!1,n=i.node.endpoints,a=n.realtime||n.web;a=a.replace("http","ws"),u.opts.IS_DEBUG&&(a=a.replace(":3000",":4020")),d("Found endpoint for "+p+" : "+a);var o=new f(a+"/primus",u.tokens.access_token);o.bucketPublic=i.public_id,o.connected=!1,o.bucket=p;var r=null;o.onmaxreconnect=function(e){if(!t)return t=!0,l(new Error("Connection timeout"))},o.onopen=function(){if(c("Connected to ws endpoint : "+a+" (bucket: "+p+")"),o.connected=!0,u.realtime.emit(i.public_id+":connected"),o.send(JSON.stringify({action:"sub",public_id:i.public_id,filters:Array.from(new Set(u._bucketFilters.get(i.public_id)))})),null!==r&&(clearInterval(r),r=null),r=setInterval(function(){o.ping()}.bind(u),5e3),!t)return t=!0,s(o)},o.onunexpectedresponse=function(e,t){return 401===t.statusCode?u.oauth_flow.retrieveTokens(u.km,function(e,t){if(e)return c("Failed to retrieve tokens for ws: "+e.message);c("Succesfully retrieved new tokens for ws"),u._updateTokens(null,t,function(e,t){return e?c("Failed to update tokens for ws: "+e.message):o._tryReconnect()})}):o._tryReconnect()},o.onerror=function(e){d("Error on "+a+" (bucket: "+p+")"),d(e),u.realtime.emit(i.public_id+":error",e)},o.onclose=function(){c("Closing ws connection "+a+" (bucket: "+p+")"),o.connected=!1,u.realtime.emit(i.public_id+":disconnected"),null!==r&&(clearInterval(r),r=null)},o.onmessage=function(e){d("Received message for bucket "+p+" ("+(e.data.length/1e3).toFixed(1)+" Kb)");var t=null;try{t=JSON.parse(e.data)}catch(e){return d("Receive not json message for bucket "+p)}var n=t.data[1];Object.keys(n).forEach(function(e){"server_name"!==e&&u.realtime.emit(i.public_id+":"+(n.server_name||"none")+":"+e,n[e])})},u._websockets.push(o)}).catch(l)})}},{key:"unsubscribe",value:function(i,e){var a=this;return new Promise(function(e,t){c("Unsubscribe from realtime for "+i);var n=a._websockets.find(function(e){return e.bucket===i});return n?(n.close(1e3,"Disconnecting"),c("Succesfully unsubscribed from realtime for "+i),e()):t(new Error("Realtime wasn't connected to "+i))})}}]),o}()},{"../constants":1,"./auth_strategies/strategy":40,"./utils/websocket":46,async:2,axios:3,debug:29,eventemitter2:31}],45:[function(e,t,n){"use strict";var te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();t.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return i(e,null,[{key:"extract",value:function(Y,Z){var ee=function(e){return null!=e};return new Promise(function(e,t){var n={params:{},data:{},url:Y.route.name+"",method:Y.route.type,authentication:Y.authentication||!1};switch(Y.route.type){case"GET":var i=!0,a=!1,o=void 0;try{for(var r,s=(Y.params||[])[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l=r.value,p=Z.shift();if("string"!=typeof p&&!1===l.optional)return t(new Error("Expected to receive string argument for "+l.name+" to match but got "+p));p?n.url=n.url.replace(l.name,p):!1===l.optional&&null!==l.defaultvalue&&(n.url=n.url.replace(l.name,l.defaultvalue))}}catch(e){a=!0,o=e}finally{try{!i&&s.return&&s.return()}finally{if(a)throw o}}var u=!0,c=!1,d=void 0;try{for(var f,m=(Y.query||[])[Symbol.iterator]();!(u=(f=m.next()).done);u=!0){var y=f.value,h=Z.shift();if("string"!=typeof h&&!1===y.optional)return t(new Error("Expected to receive string argument for "+y.name+" query but got "+h));h?n.params[y.name]=h:!1===y.optional&&null!==y.defaultvalue&&(n.params[y.name]=y.defaultvalue)}}catch(e){c=!0,d=e}finally{try{!u&&m.return&&m.return()}finally{if(c)throw d}}break;case"PUT":case"POST":case"PATCH":var v=!0,g=!1,b=void 0;try{for(var k,w=(Y.params||[])[Symbol.iterator]();!(v=(k=w.next()).done);v=!0){var _=k.value,T=Z.shift();if("string"!=typeof T&&!1===_.optional)return t(new Error("Expected to receive string argument for "+_.name+" to match but got "+T));T?n.url=n.url.replace(_.name,T):!1===_.optional&&null!==_.defaultvalue&&(n.url=n.url.replace(_.name,_.defaultvalue))}}catch(e){g=!0,b=e}finally{try{!v&&w.return&&w.return()}finally{if(g)throw b}}var A=!0,j=!1,E=void 0;try{for(var S,x=(Y.query||[])[Symbol.iterator]();!(A=(S=x.next()).done);A=!0){var O=S.value,P=Z.shift();if("string"!=typeof P&&!1===O.optional)return t(new Error("Expected to receive string argument for "+O.name+" query but got "+P));P?n.params[O.name]=P:!1===O.optional&&null!==O.defaultvalue&&(n.params[O.name]=O.defaultvalue)}}catch(e){j=!0,E=e}finally{try{!A&&x.return&&x.return()}finally{if(j)throw E}}if(0===Z.length)break;var z=Z[0];if("object"!==(void 0===z?"undefined":te(z))&&0<Y.body.length)return t(new Error("Expected to receive an object for post data but received "+(void 0===z?"undefined":te(z))));var L=!0,B=!1,C=void 0;try{for(var D,U=(Y.body||[])[Symbol.iterator]();!(L=(D=U.next()).done);L=!0){var R=D.value;if(!ee(z[R.name])&&!1===R.optional&&null===R.defaultvalue)return t(new Error("Missing mandatory field "+R.name+" to make a POST request on "+Y.route.name));if(te(z[R.name])!==R.type&&!1===R.optional&&null===R.defaultvalue)return t(new Error("Invalid type for field "+R.name+", expected "+R.type+" but got "+te(z[R.name])));ee(z[R.name])&&(n.data[R.name]=z[R.name]),!1===R.optional&&null!==R.defaultvalue&&(n.data[R.name]=R.defaultvalue)}}catch(e){B=!0,C=e}finally{try{!L&&U.return&&U.return()}finally{if(B)throw C}}break;case"DELETE":var I=!0,N=!1,q=void 0;try{for(var M,G=(Y.params||[])[Symbol.iterator]();!(I=(M=G.next()).done);I=!0){var F=M.value,H=Z.shift();if("string"!=typeof H&&!1===F.optional)return t(new Error("Expected to receive string argument for "+F.name+" to match but got "+H));H?n.url=n.url.replace(F.name,H):!1===F.optional&&null!==F.defaultvalue&&(n.url=n.url.replace(F.name,F.defaultvalue))}}catch(e){N=!0,q=e}finally{try{!I&&G.return&&G.return()}finally{if(N)throw q}}var W=!0,V=!1,Q=void 0;try{for(var X,J=(Y.query||[])[Symbol.iterator]();!(W=(X=J.next()).done);W=!0){var $=X.value,K=Z.shift();if("string"!=typeof K&&!1===$.optional)return t(new Error("Expected to receive string argument for "+$.name+" query but got "+K));K?n.params[$.name]=K:!1===$.optional&&null!==$.defaultvalue&&(n.params[$.name]=$.defaultvalue)}}catch(e){V=!0,Q=e}finally{try{!W&&J.return&&J.return()}finally{if(V)throw Q}}break;default:return t(new Error("Invalid endpoint declaration, invalid method "+Y.route.type+" found"))}return e(n)})}}]),e}()},{}],46:[function(e,t,n){"use strict";var i=e("ws"),a=e("debug")("kmjs:network:_ws"),o="function"!=typeof i?WebSocket:i,r={debug:!1,automaticOpen:!0,reconnectOnError:!0,reconnectInterval:1e3,maxReconnectInterval:1e4,reconnectDecay:1,timeoutInterval:2e3,maxReconnectAttempts:1/0,randomRatio:3,reconnectOnCleanClose:!1},s=function(e,t,n,i){n||(n=[]),i||(i=[]),this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this._url=e,this._token=t,this._protocols=n,this._options=Object.assign({},r,i),this._messageQueue=[],this._reconnectAttempts=0,this.readyState=this.CONNECTING,"function"==typeof this._options.debug?this._debug=this._options.debug:this._options.debug?this._debug=console.log.bind(console):this._debug=function(){},this._options.automaticOpen&&this.open()};s.prototype.updateAuthorization=function(e){this._token=e},s.prototype.open=function(){a("open");var e=this._socket=new o(this._url+"?token="+this._token,this._protocols);if(this._options.binaryType&&(e.binaryType=this._options.binaryType),this._options.maxReconnectAttempts&&this._options.maxReconnectAttempts<this._reconnectAttempts)return this.onmaxreconnect();this._syncState(),e.on&&e.on("unexpected-response",this._onunexpectedresponse),e.onmessage=this._onmessage.bind(this),e.onopen=this._onopen.bind(this),e.onclose=this._onclose.bind(this),e.onerror=this._onerror.bind(this)},s.prototype.send=function(e){a("send"),this._socket&&this._socket.readyState===o.OPEN&&0===this._messageQueue.length?this._socket.send(e):this._messageQueue.push(e)},s.prototype.ping=function(){a("ping"),this._socket.ping&&this._socket&&this._socket.readyState===o.OPEN&&0===this._messageQueue.length&&this._socket.ping()},s.prototype.close=function(e,t){a("close"),void 0===e&&(e=1e3),this._socket&&this._socket.close(e,t)},s.prototype._onunexpectedresponse=function(e,t){a("unexpected-response"),this.onunexpectedresponse&&this.onunexpectedresponse(e,t)},s.prototype._onmessage=function(e){a("onmessage"),this.onmessage&&this.onmessage(e)},s.prototype._onopen=function(e){a("onopen"),this._syncState(),this._flushQueue(),0!==this._reconnectAttempts&&this.onreconnect&&this.onreconnect(),this._reconnectAttempts=0,this.onopen&&this.onopen(e)},s.prototype._onclose=function(e){a("onclose",e),this._syncState(),this._debug("WebSocket: connection is broken",e),this.onclose&&this.onclose(e),this._tryReconnect(e)},s.prototype._onerror=function(e){a("onerror",e),this._socket.close(),this._syncState(),this._debug("WebSocket: error",e),this.onerror&&this.onerror(e),this._options.reconnectOnError&&this._tryReconnect(e)},s.prototype._tryReconnect=function(e){var t=this;a("Trying to reconnect"),e.wasClean&&!this._options.reconnectOnCleanClose||setTimeout(function(){t.readyState!==t.CLOSING&&t.readyState!==t.CLOSED||(t._reconnectAttempts++,t.open())},this._getTimeout())},s.prototype._flushQueue=function(){for(;0!==this._messageQueue.length;){var e=this._messageQueue.shift();this._socket.send(e)}},s.prototype._getTimeout=function(){var e,t,n=this._options.reconnectInterval*Math.pow(this._options.reconnectDecay,this._reconnectAttempts);return n=n>this._options.maxReconnectInterval?this._options.maxReconnectInterval:n,this._options.randomRatio?(e=n/this._options.randomRatio,t=n,Math.random()*(t-e)+e):n},s.prototype._syncState=function(){this.readyState=this._socket.readyState},t.exports=s},{debug:29,ws:28}],"/":[function(e,t,n){"use strict";t.exports=e("./src/keymetrics.js")},{"./src/keymetrics.js":42}]},{},[])("/")});
