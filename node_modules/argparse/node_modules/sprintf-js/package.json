{"name": "sprintf-js", "version": "1.0.3", "description": "JavaScript sprintf implementation", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://alexei.ro/)", "main": "src/sprintf.js", "scripts": {"test": "mocha test/test.js"}, "repository": {"type": "git", "url": "https://github.com/alexei/sprintf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "README.md", "devDependencies": {"mocha": "*", "grunt": "*", "grunt-contrib-watch": "*", "grunt-contrib-uglify": "*"}}