{"name": "cron", "description": "Cron jobs for your node", "version": "1.8.2", "author": "<PERSON> <<EMAIL>> (http://github.com/ncb000gt)", "bugs": {"url": "http://github.com/kelektiv/node-cron/issues"}, "repository": {"type": "git", "url": "http://github.com/kelektiv/node-cron.git"}, "main": "lib/cron", "scripts": {"test": "jest"}, "dependencies": {"moment-timezone": "^0.5.x"}, "devDependencies": {"chai": "~4.2.x", "eslint": "~6.3.x", "eslint-config-prettier": "~6.2.x", "eslint-config-standard": "~14.1.x", "eslint-plugin-import": "~2.18.x", "eslint-plugin-jest": "^22.17.0", "eslint-plugin-node": "~10.0.x", "eslint-plugin-prettier": "~3.1.x", "eslint-plugin-promise": "~4.2.x", "eslint-plugin-standard": "~4.0.x", "jest": "^24.9.0", "prettier": "~1.18.x", "sinon": "~7.4.x"}, "keywords": ["cron", "node cron", "node-cron", "schedule", "scheduler", "cronjob", "cron job"], "license": "MIT", "contributors": ["<PERSON><PERSON> <<EMAIL>> (https://github.com/toots)", "<PERSON> <> (https://github.com/james<PERSON><PERSON>ey)", "<PERSON> <<EMAIL>> (https://github.com/ErrorProne)", "<PERSON> <<EMAIL>> (https://github.com/cliftonc)", "<PERSON> <<EMAIL>> (https://github.com/neyric)", "humanchimp <<EMAIL>> (https://github.com/humanchimp)", "<PERSON> <<EMAIL>> (https://github.com/spiceapps)", "<PERSON> <<EMAIL>> (https://github.com/danhbear)", "<PERSON><PERSON><PERSON> <vadi<PERSON><EMAIL>> (https://github.com/baryshev)", "<PERSON>nd<PERSON> Ferrari <<EMAIL>> (https://github.com/lfthomaz)", "<PERSON> <<EMAIL>> (https://github.com/greggzigler)", "<PERSON> <<EMAIL>> (https://github.com/jordanabderrachid)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (matsukaz)", "<PERSON> <<EMAIL>> (https://github.com/kirisu)"]}