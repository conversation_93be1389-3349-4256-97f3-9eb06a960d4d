"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogicalOperators = exports.AssignmentOperators = exports.BinaryOperators = void 0;
exports.BinaryOperators = [
    "==", "!=", "===", "!==",
    "<", "<=", ">", ">=",
    "<<", ">>", ">>>",
    "+", "-", "*", "/", "%",
    "&",
    "|", "^", "in",
    "instanceof",
];
exports.AssignmentOperators = [
    "=", "+=", "-=", "*=", "/=", "%=",
    "<<=", ">>=", ">>>=",
    "|=", "^=", "&=",
];
exports.LogicalOperators = [
    "||", "&&",
];
