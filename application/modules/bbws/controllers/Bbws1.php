<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
// //require_once(APPPATH . "third_party/PFBC/Form.php");

// use \diversen\gps;

class Bbws extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        // $this->load->model('M_pagu_paket');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }
       

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        //echo $satker; die();
        //echo $this->session->users['id_user_group_real'];
        //die();
        // $childsatker = $this->child_satker($satker);
        // echo $childsatker; die();
        //$hir_satkerList = $this->hir_satkerList($satker);
        //$dtModal = array("child_satker" => $hir_satkerList);

        // if ($tap === 'PI') {
        //     $title = "Pagu Indikatif";
        // } else if ($tap === 'PA') {
        //     $title = "Pagu Anggaran";
        // } else if ($tap === 'PAA') {
        //     $title = "Pagu Alokasi Anggaran";
        // } else if ($tap === 'DA') {
        //     $title = "Dipa Awal";
        // } else if (substr ($tap, 0, 2) === 'RD') {
        //     $title = "Dipa Revisi";
        // } else {
        //     $title = "";
        // }
        $title = "Data BBWS/BWS P3-TGAI";

        // $js_file2 = $this->load->view('bbws/js_file2', '', true);

        $js_file = $this->load->view('bbws/js_file', '', true);
        $modal_tambah = $this->load->view('bbws/modal_tambah', '', true);
        $modal_download = $this->load->view('bbws/modal_download', '', true);
        // if (!empty($this->session->users['kd_bujt'])) {
        //     $kd_prov_irmsv3 = $this->get_prov_irmsv3($this->session->users['kd_bujt']);
        // }

        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_download" => $modal_download,
            "title" => $title,
            "jv_script" => $js_file
        );
        // print_r($data);
        $this->load->view('index', $data);
    }

    public function ssp_paket() { 
        // if (empty($this->input->post('thang', TRUE))){
        //     $yearnow = 0;
        // } else {
            $id_user = $this->session->users['id_user'];
         $satker = $this->session->users['kd_satker'];
         $role = $this->session->users['id_user_group_real'];
         $wh = '';
         if($role==4){
             $wh = "kd_satker='$satker'";
         }
         elseif($role==7){
             $wh = "created_by=$id_user";
         }
         $yearnow = $this->input->post('thang', TRUE);
        
         $table = 'v_tbbws';
         $primaryKey = 'id'; //test
 
         $columns = array(
             array('db' => 'id', 'dt' => 0),
             array('db' => 'nm_satker', 'dt' => 1),
             array('db' => 'tpm_pria', 'dt' => 2),
             array('db' => 'tpm_wanita', 'dt' => 3),
             array('db' => 'tpm_total', 'dt' => 4),
    
         );
 
         datatable_ssp($table, $primaryKey, $columns, "$wh");
 
         // if ($roledesc == 'bujt') {
         //    // echo 'masuk sini';die();
         //     datatable_ssp($table, $primaryKey, $columns, " kd_bujt=$satker and $wh tahun=$yearnow");
         // } else {
         //     datatable_ssp($table, $primaryKey, $columns, " $wh  tahun=$yearnow");
             
         // }
     }
 
 
     public function addform() {
         error_reporting(3);
         $param = $this->input->post('formData', TRUE);
         $id = $this->input->post("formData")["id"];
         $data = [
             "ruas_id_ruas" => $this->input->post("formData")["id_ruas"],
             "aset_r_bujt_id_bujt" => $this->input->post("formData")["id_bujt"], //xx
             "staa" => $this->input->post("formData")["staa"],
             "stae" => $this->input->post("formData")["stae"],
             "temuan" => $this->input->post("formData")["temuan"],
             "penanganan" => $this->input->post("formData")["penanganan"],
             "catatan" => $this->input->post("formData")["catatan"],
             "bbws_1" => $this->input->post("formData")["bbws_1"],
             "bbws_2" => $this->input->post("formData")["bbws_2"],
             
         ];
        //  $this->db->set('r_ppjt_id_rppjt', '0', FALSE);
        //  $this->db->set('dok_kons_id_dokkons', '0', FALSE);
        //  $this->db->set('uji_laik_id_ujilaik', '0', FALSE);
         $this->db->set('created_at', 'NOW()', FALSE);
         $this->db->set('updated_at', 'NOW()', FALSE);
         $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
         $this->db->set('created_by', $this->session->users['id_user'], FALSE);
     
         if($id != ''){
             $this->db->where("id_periksa",$id);
            $dt = $this->db->update('bbws', $data);
 
         }else{
             $dt = $this->db->insert('bbws', $data);
 
         }
         if($dt){
             echo "sukses";
         }else{
             echo "gagal";
         }
     }
     function tampildata($tab='',$colum='',$val_colum=''){
          $data=$this->db->get_where($tab,array($colum=>str_replace('_', ' ', $val_colum)))->result_array();
          echo json_encode($data);
        }

      function tampildatabbws($kds){
            $data=$this->db->query("select a.*,b.nama_prov  from r_risibbws a
            left join aset_r_provinsi b on (a.kd_prov=b.kd_prov) where id_isibbws=$kds")->result_array();
            echo json_encode($data);
      }
     public function ajax_delete($id) {
        $this->db->where("id",$id);
        $dt=$this->db->delete('t_isibbws');
        $this->db->delete('r_risibbws',array('id_isibbws'=>$id));
       if($dt){
        echo 0;
        }else{
            echo "gagal";
        }
        if($dt){
         echo 0;
         }else{
             echo "gagal";
         }
     }
     public function up() {
        if (is_array($_FILES)) {
            $x = str_replace('-', '', date('Y-m'));
            $nama_dir = FCPATH . 'uploads/' . $x . "/";

            if (is_dir($nama_dir)) {

            } else {
                mkdir(FCPATH . 'uploads/' . $x, 0777, true);
//            $m = FCPATH . 'uploads/' . str_replace('-', '', date('Y-m'));
            }
            $upload_path_url = str_replace('-', '', date('Y-m'));
            if (is_uploaded_file($_FILES['filess']['tmp_name'])) {
                $sourcePath = $_FILES['filess']['tmp_name'];
                $namf = $_FILES['filess']['name'];
                $rep = str_replace(" ", "_", $namf);
                $fil = date('Ymd') . date("his") . $rep;
                $targetPath = FCPATH . "uploads/" . $upload_path_url . "/" . $fil;
                move_uploaded_file($sourcePath, $targetPath);
               // $this->db->where('id_usulan', $id);


               $this->db->set('created_at', 'NOW()', FALSE);
               $this->db->set('updated_at', 'NOW()', FALSE);
               $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
               $this->db->set('created_by', $this->session->users['id_user'], FALSE);
                $res = $this->db->insert('dok_periksa',
                   array('path' => $fil,
                    'judul_dok' => $this->input->post('judul', TRUE),
                    // 'uptodate' => $this->session->users['id_user'],
                    'id_periksa' => $this->input->post('id_up', TRUE)));
                   
        
            }
        }
    }
    public function ssp_attachment() {
        $id = $this->input->post("id", TRUE);
        $role = $this->input->post("role", TRUE);
        // echo $role." ***".$id_usulan; die();
        $table = 'dok_periksa';
        $primaryKey = 'id_dokperiksa';
        $columns = array(
            array('db' => 'path', 'dt' => 0),
            array('db' => 'id_dokperiksa', 'dt' => 1),
            array('db' => 'id_periksa', 'dt' => 2),
            array('db' => 'judul_dok', 'dt' => 3),
        );
        datatable_ssp($table, $primaryKey, $columns, "id_periksa=$id");
    }

    
    public function hps_lampiran($id) {

        $this->db->delete('dok_periksa', array('id_dokperiksa' => $id));

        echo json_encode(array("status" => TRUE));
    }
 
function upload_dt($tmp,$name,$judul,$id_giat,$kat){
    $upload_path_url = str_replace('-', '', date('Y-m'));
    if (is_uploaded_file($tmp)) {
       
        $sourcePath = $tmp;
        $namf = $name;
        $rep = str_replace(" ", "_", $namf);
        $fil = date('Ymd') . date("his") . $rep;
        $targetPath = FCPATH . "uploads/" . $upload_path_url . "/" . $fil;
        move_uploaded_file($sourcePath, $targetPath);
       // $this->db->where('id_usulan', $id);


       $this->db->set('created_at', 'NOW()', FALSE);
       $this->db->set('updated_at', 'NOW()', FALSE);
       $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
       $this->db->set('created_by', $this->session->users['id_user'], FALSE);
       if($kat=='edit'){
                $this->db->where('judul_dok',$judul);
                $this->db->where('id_giat',$id_giat);
        $res = $this->db->update('dok_kegiatanp3a',
        array('path' => $fil,
             'judul_dok' => $judul));
        }else{
            $res = $this->db->insert('dok_kegiatanp3a',
            array('path' => $fil,
                 'judul_dok' => $judul,
                 'id_giat' => $id_giat));
        }
        
           

    }
}
    function insert_data(){

            
            $id=$this->input->post('id');
           
            // $bbws=$this->input->post('bbws');
            $pagu_dana=$this->input->post('pagu_dana');
            $realisasi=$this->input->post('realisasi');
            $no_sktb=$this->input->post('no_sktb');
            $tgl_sktb=$this->input->post('tgl_sktb');
            $jml_ta=$this->input->post('jml_ta');
            $jml_asisten=$this->input->post('jml_asisten');
            $nm_pt=$this->input->post('nm_pt');
            $tpm_pria=$this->input->post('jum_pese_l');
            $tpm_wanita=$this->input->post('jum_pese_p');
            $tpm_total=$this->input->post('jum_tot_pes');
            $tgl_latih1=$this->input->post('tgl_latih1');
            $tgl_latih2=$this->input->post('tgl_latih2');
            $tgl_latih3=$this->input->post('tgl_latih3');
            $tgl_latih4=$this->input->post('tgl_latih4');
            $tgl_latih5=$this->input->post('tgl_latih5');
            $tgl_latih6=$this->input->post('tgl_latih6');
            $tgl_latih7=$this->input->post('tgl_latih7');
            $tgl_sosial1=$this->input->post('tgl_sosial1');
            $tgl_sosial2=$this->input->post('tgl_sosial2');
            $tgl_sosial3=$this->input->post('tgl_sosial3');
            $tgl_sosial4=$this->input->post('tgl_sosial4');
            $tgl_sosial5=$this->input->post('tgl_sosial5');
            $tgl_sosial6=$this->input->post('tgl_sosial6');
            $tgl_sosial7=$this->input->post('tgl_sosial7');
            $bbws =   $this->session->users['kd_satker'];
            $bl =	$this->input->post('bl');
            $jml_lokasi=$this->input->post('jml_lokasi');
            $nilai_pdp  =	$this->input->post('nilai_pdp');
            $pagu_dana  =	$this->input->post('pagu_dana');
            $pagu_dana_fisik  =	$this->input->post('pagu_dana_fisik');
            $satker = $this->session->users['kd_satker'];
            $kat1 = 'tambah';
            $kat2 = 'tambah';
            $data_kegiatan=array(
                         'kd_satker'=>($bbws == '') ? NULL : $bbws,  
                        // 'jml_lokasi'=>($jml_lokasi == '') ? NULL : $jml_lokasi,  
                        // 'pagu_dana'=>($pagu_dana == '') ? NULL : $pagu_dana,  
                        // 'realisasi'=>($realisasi == '') ? NULL : $realisasi,
                        'no_sktpb'=>($no_sktb == '') ? NULL : $no_sktb,  
                        'tgl_sktpb'=>($bbws == '') ? NULL : $tgl_sktb,  
                        'jml_ta'=>($bbws == '') ? NULL : $jml_ta,  
                        'jml_asisten'=>($bbws == '') ? NULL : $jml_asisten,  
                        'nm_pt'=>($bbws == '') ? NULL : $nm_pt,  
                        'tpm_pria'=>($tpm_pria == '') ? NULL : $tpm_pria,  
                        'tpm_wanita'=>($tpm_wanita == '') ? NULL : $tpm_wanita,  
                        'tpm_total'=>($tpm_total == '') ? NULL : $tpm_total,  
                        'tgl_latih1'=>($tgl_latih1 == '') ? NULL : $tgl_latih1,  
                        'tgl_latih2'=>($tgl_latih2 == '') ? NULL : $tgl_latih2,  
                        'tgl_latih3'=>($tgl_latih3 == '') ? NULL : $tgl_latih3,  
                        'tgl_latih4'=>($tgl_latih4 == '') ? NULL : $tgl_latih4,  
                        'tgl_latih5'=>($tgl_latih5 == '') ? NULL : $tgl_latih5,  
                        'tgl_latih6'=>($tgl_latih6 == '') ? NULL : $tgl_latih6,  
                        'tgl_latih7'=>($tgl_latih7 == '') ? NULL : $tgl_latih7,  
                        'tgl_sosial1'=>($tgl_sosial1 == '') ? NULL : $tgl_sosial1,  
                        'tgl_sosial2'=>($tgl_sosial2 == '') ? NULL : $tgl_sosial2,  
                        'tgl_sosial3'=>($tgl_sosial3 == '') ? NULL : $tgl_sosial3,  
                        'tgl_sosial4'=>($tgl_sosial4 == '') ? NULL : $tgl_sosial4,  
                        'tgl_sosial5'=>($tgl_sosial5 == '') ? NULL : $tgl_sosial5,  
                        'tgl_sosial6'=>($tgl_sosial6 == '') ? NULL : $tgl_sosial6,  
                        'tgl_sosial7'=>($tgl_sosial7 == '') ? NULL : $tgl_sosial7,  
                  
                      );
            $this->db->trans_start();

            if($id==''){
            $this->db->set('created_at', 'NOW()', FALSE);
            $this->db->set('updated_at', 'NOW()', FALSE);
            $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
            $this->db->set('created_by', $this->session->users['id_user'], FALSE);
            $x=$this->db->insert('t_isibbws',$data_kegiatan);
      
           $id =  $this->db->insert_id();
            
            // $this->db->trans_complete();
              $z="insert";
            }else{
    
            //   $this->db->trans_start();
            $this->db->where('id', $id);
            $this->db->set('created_at', 'NOW()', FALSE);
            $this->db->set('updated_at', 'NOW()', FALSE);
            $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
            $this->db->set('created_by', $this->session->users['id_user'], FALSE);
            $x=$this->db->update('t_isibbws',$data_kegiatan);
    
              $z="update";
            }

            $this->db->delete('r_risibbws',array('id_isibbws'=>$id));
            for ($xx = 0; $xx <= count($bl)-1; $xx++) {
               if($bl[$xx] !==''){
               $dt_bl=array('id_isibbws'=>$id,
                           'nilai_pdp_prov'=>($nilai_pdp[$xx]== '') ? NULL :	$nilai_pdp[$xx],
                           'jml_lokasi'=>($jml_lokasi[$xx]== '') ? NULL :	$jml_lokasi[$xx],
                           'pagu_dana_pendamping'=>($pagu_dana[$xx]== '') ? NULL :	$pagu_dana[$xx],
                           'pagu_dana_fisik'=>($pagu_dana_fisik[$xx]== '') ? NULL :	$pagu_dana_fisik[$xx],
                           'kd_prov'=>($bl[$xx]== '') ? NULL :	$bl[$xx],
                           'kd_satker'=>$satker,
                        );
               $this->db->insert('r_risibbws',$dt_bl);
               }

            } 
            $this->db->trans_complete();

            if($x){
              echo "0_".$z;
            }else{
            $this->db->last_query();
                // $errMess = $this->db->_error_message();
              echo "1_".$z;
            }
          }
    

}
