<!-- <script type="text/javascript" charset="utf8" src="<?php //echo base_url(); ?>assets/js/wayjs/way.js"></script> -->
<script>
     <?php
        require_once(FCPATH."env.php");
        echo 'var WGI_APP_BASE_URL = "'.WGI_APP_BASE_URL.'"; ';
    ?>
    var thangs = "<?php echo $this->session->users['tahun_p3tgai']; ?>";
    var satker = "<?php echo $this->session->users['kd_satker']; ?>";
    var nama_user = "<?php echo $this->session->users['nama']; ?>";
    var kd_prov_ses = "<?php echo $this->session->users['kd_prov']; ?>";
    var iduser = "<?php echo $this->session->users['id_user'] ?>"
    var xhrdata = null;
    var table = null;
    var group = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var urlBase = WGI_APP_BASE_URL;
    var sessLocked = "<?php echo $this->session->userdata("satker" . $this->session->users['tahun_p3tgai'] . "_" . $this->session->users['kd_satker']); ?>";
    var v0 = '';
    var v1 = '';
    var v2 = '';
    var v3 = '';
    var v4 = '';
    var tahap = '';
    var progress = '';
    var but = '';
//  alert(sessLocked);

    // Loading functions for form data
    function showFormLoading() {
        var loadingHtml = `
            <div id="form-loading-overlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                z-index: 9999;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            ">
                <img src="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/0.16.1/images/loader-large.gif"
                     alt="Loading..." style="width: 64px; height: 64px;">
                <div style="color: white; margin-top: 15px; font-size: 16px; font-family: Arial, sans-serif;">
                    Loading form data...
                </div>
            </div>
        `;
        $('body').append(loadingHtml);
    }

    function hideFormLoading() {
        $('#form-loading-overlay').fadeOut(300, function() {
            $(this).remove();
        });
    }

    //console.log('------>'+sessLocked)


    function clear_input() {
        //  $("#formData :input").val("");
        $('#formData')[0].reset();
    }



    function lockEntri(mod, id) {
        if (sessLocked == 1) {
            cssAct = (mod == 'edit') ? but : 'display:none !important;';
            // console.log("The key "+ satker +" exists in local storage.");
        } else {
            cssAct = (mod == 'edit') ? "onclick=\"dtTambahRow('edit','" + id + "')\"" : 'display:block;';
            // console.log("The key "+ satker +" does not exist in local storage.");
        }
        return cssAct;
    }

    function tracking_data(v0, v1, v2, v3, v4, tahap, ver1, ver2, ver3, ver4, id) {

        // "<span class='badge badge-primary'><i class='fa fa-edit' onclick= dtTambahRow('edit','" + id + "')></i></span>",
        // "<span class='badge badge-danger'><i class='fa fa-trash' onclick= dtDeleteRow('" + id + "')></i></span>",
        // "<span class='badge badge-warning'><i class='fa fa-search' onclick= dtTambahRow('detail','" + id + "')></i></span>",
        if (group == 3 || group == 4 || group == 5) {

            but = "onclick=\"dtTambahRow('detail','" + id + "')\"";
        } else if (group == 7 || group == 1 || group == 2) {

            but = lockEntri('edit', id);

        }
        var tahap = parseInt(tahap);


        if (v0 === 'Data Belum Lengkap' || v1 === 'Data Belum Lengkap') {
            progress1 = "<span style='cursor:pointer;' " + but +
                "  title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><span class='blink_me'><i class='fa fa-exclamation-triangle'></i></span>&nbsp;Persiapan</span>";
        } else {
            if (ver1 == 1) {
                progress1 = "<span style='cursor:pointer;' " + but +
                    " title='lengkap & valid' class='badge badge-pill badge-success'><span class='blink_me'><i class='fa fa-check-circle'></i></span>&nbsp;Persiapan</span>";
            } else if (ver1 == 2) {
                progress1 = "<span style='cursor:pointer;' " + but +
                    " title='lengkap & tdk vaild' class='badge badge-pill badge-warning'><span class='blink_me'><i class='fa fa-times'></i></span>&nbsp;Persiapan</span>";
            } else {
                progress1 = "<span style='cursor:pointer;' " + but +
                    " title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class='blink_me'><i class='fa fa-question-circle'></i></span>&nbsp;Persiapan</span>";
            }
        }

        if (ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap') {
            if (v2 === 'Data Belum Lengkap') {
                progress2 =
                    "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Perencanaan</span>";

            } else {
                if (ver2 == 1) {
                    progress2 =
                        "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Perencanaan</span>";
                } else if (ver2 == 2) {
                    progress2 =
                        "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Perencanaan</span>";
                } else {
                    progress2 =
                        "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Perencanaan</span>";
                }
                // progress2 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span>";
            }
        } else {
            progress2 =
                "<span style='cursor:help;' title='Belum sampai ke tahapan Perencanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span>";
        }

        if (ver2 == 1 && ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap' && v2 ==
            'Data Sudah Lengkap') {
            if (v3 === 'Data Belum Lengkap') {
                progress3 =
                    "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Pelaksanaan</span>";
            } else {
                if (ver3 == 1) {
                    progress3 =
                        "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Pelaksanaan</span>";
                } else if (ver3 == 2) {
                    progress3 =
                        "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Pelaksanaan</span>";
                } else {
                    progress3 =
                        "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Pelaksanaan</span>";
                }
                // progress3 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span>";
            }
        } else {
            progress3 =
                "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>";

        }

        if (ver3 == 1 && ver2 == 1 && ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap' && v2 ==
            'Data Sudah Lengkap' && v3 == 'Data Sudah Lengkap') {
            if (v4 === 'Data Belum Lengkap') {
                progress4 =
                    "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Penyelesaian</span>";
            } else {
                if (ver4 == 1) {
                    progress4 =
                        "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Penyelesaian</span>";
                } else if (ver4 == 2) {
                    progress4 =
                        "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Penyelesaian</span>";
                } else {
                    progress4 =
                        "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Penyelesaian</span>";
                }
                // progress4 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Penyelesaian</span>";
            }
        } else {
            progress4 =
                "<span style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";

        }


        if (tahap == 4) {
            progress = progress1 + progress2 + progress3 + progress4;
        } else if (tahap == 3) {
            progress = progress1 + progress2 + progress3 +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian'  class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        } else if (tahap == 2) {
            progress = progress1 + progress2 +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>" +
                "<span  style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        } else if (tahap == 1) {
            progress = progress1 +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Perencanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span>" +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>" +
                "<span  style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        }







        //    if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //    } else if(v0=='Data Belum Lengkap' && v1=='Data Belum Lengkap' & v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //    }

        //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     } else  if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     }

        //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     } else if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     }


        //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Sudah Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Penyelesaian</span>"
        //     } else if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Penyelesaian</span>";
        //     }


        return progress;
    }

    function dtRefresh(stat, id) {
        // var mustKMB = "<?php //echo $this->session->users['id_user']; ?>";
        // var id = $("#id").val()
        var url = "<?php echo base_url(); ?>persiapan/getback_file/" + stat + "/" + id;
        var text = 'Anda akan memproses perbaikan dokumen gambar?'
        // if(stat == 1){
        //     text = "Anda akan memproses perbaikan dokumen gambar?"
        // }else{
        //     text = ""
        // }
        swal({
            title: text,
            //   text: tex,
            icon: "info",
            buttons: true,
            dangerMode: true,
        })
            .then((willDelete) => {
                if (willDelete) {
                    $.get(url, {}).done(function (data) {
                        if (data == 0) {
                            swal({
                                title: "",
                                text: "Berhasil Mengembalikan Dokumen Foto",
                                icon: "success",
                                showConfirmButton: false,
                                timer: 3000,
                                type: "success"
                            });
                            // table.ajax.reload();
                            $('#modal-tambah').modal('hide');
                        }
                    })
                } else {
                    swal("Anda tidak berhasil mengembalikan Dokumen Foto");
                }
            })
    }

    // function clean_str(dirtyString) {
    //     var cleanString = dirtyString.replace(/[|&;$%@"<>()+,]/g, "");

    //     return cleanString;
    // }



    function listing() {

table = $('#dt-server-processing').DataTable({
    "draw": 0,
    "order": [
        [0, "asc"]
    ],
    "processing": true,
    "deferRender": true,
    "serverSide": true,
    "ajax": {
        type: "post",
        url: "<?php echo base_url(); ?>persiapan/ssp_paket",
        "data": function (d) {
            d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
            d.fs = $(".statustahap").val();
        }
    },
    "columnDefs": [
        {
            "aTargets": [0],
            "mRender": function (data, type, full) {
                return full[1];
            }
        },
        {
            "aTargets": [1],
            "mRender": function (data, type, full) {
                return full[2];
            }
        },
        {
            "aTargets": [2],
            "mRender": function (data, type, full) {
                return full[3];
            }
        },
        {
            "aTargets": [3],
            "mRender": function (data, type, full) {
                return full[4];
            }
        },
        {
            "aTargets": [4],
            "mRender": function (data, type, full) {
                return full[5];
            }
        },
        {
            "aTargets": [5],
            "mRender": function (data, type, full) {
                return full[6];
            }
        },
        {
            "aTargets": [6],
            "mRender": function (data, type, full) {
                return full[7];
            }
        },
        {
            "aTargets": [7],
            "mRender": function (data, type, full) {
                return full[8];
            }
        },
        {
            "aTargets": [8],
            "mRender": function (data, type, full) {
                return tracking_data(full[9], full[10], full[11], full[12], full[13], full[14], full[15], full[16], full[17], full[18], full[0]);
            }
        },
        {
            "aTargets": [9],
            "mRender": function (data, type, row) {
                var id = row[0];
                var show = 'display:none !important;';
                var show2 = 'display:none !important;';
                if (group == 2) {
                    show = 'display:block !important;';
                } else if (group == 1 || group == 7) {
                    show2 = lockEntri('del', id);
                }
                var html_button = [
                    "<span class='badge badge-warning' style='" + show + "cursor:pointer;background:#4f9dbb;' onclick=\"dtTambahRow('detail','" + id + "')\" title='Validasi data'>Validasi</span>",
                    "<span class='badge badge-danger' style='" + show2 + "cursor:pointer;'><i class='fa fa-trash' onclick=\"dtDeleteRow('" + id + "')\"></i></span>"
                ].join("\n");
                return html_button;
            }
        }
    ],
    "language": {
        "decimal": "",
        "emptyTable": "Data tidak ditemukan",
        "info": "Data _START_ s/d _END_ dari _TOTAL_",
        "infoEmpty": "Tidak ada data",
        "infoFiltered": "(tersaring dari _MAX_)",
        "thousands": ",",
        "lengthMenu": "_MENU_  data per halaman",
        "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
        "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
        "search": "Cari:",
        "zeroRecords": "Tidak ada data ditemukan",
        "aria": {
            "sortAscending": ": aktifkan untuk mengurutkan naik",
            "sortDescending": ": aktifkan untuk mengurutkan turun"
        }
    },
    "initComplete": function () {
        var $searchInput = $('#dt-server-processing_filter input');
        $searchInput.attr('placeholder', 'Lalu Tekan Enter'); // Add placeholder text
        $searchInput.unbind();
        $searchInput.bind('keyup', function (e) {
            if (e.keyCode == 13) { // Enter key
                table.search(this.value).draw();
            }
        });

    // Add a reset button next to the search input with additional styling
    var $resetButton = $('<button type="button" style="display: none; margin-left: 10px; padding: 5px 10px; background-color: #f0f0f0; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;">Reset</button>').click(function () {
        $searchInput.val(''); // Clear the search input
        table.search('').draw(); // Reset the table search
        $resetButton.hide(); // Hide the reset button after resetting
    });

    // Append the reset button to the filter element
    $('#dt-server-processing_filter').append($resetButton);

    // Show the reset button after hitting enter in the search input
    $searchInput.on('keyup', function (e) {
        if (e.keyCode == 13 && $searchInput.val().length > 0) { // Check if Enter key is pressed
            $resetButton.show(); // Show the reset button
        } else {
            $resetButton.hide(); // Hide the reset button if input is empty
        }
    });
    }
});

table.on('xhr', function () {
    xhrdata = table.ajax.json();
});
        //});
    }
    // function simpanForm() {
    //     var mode = $('#modeform').val();
    //     var data = $('.form-tambah').serialize();
    //     console.log(new FormData(this))

    //     // if($( "#frm-tambah" ).valid() === true){
    //         // var na=parseFloat($("#nilai_asuransi").val().replace(/\D/g, ''));
    //         // var realisasi=parseFloat($("#realisasi").val().replace(/\D/g, ''));
    //         // var deviasi=parseFloat($("#deviasi").val().replace(/\D/g, ''));
    //         var p_satu = $('input[name="persiapan_1"]:checked').val();
    //         var p_dua = $('input[name="persiapan_2"]:checked').val();
    //         if(p_satu=== undefined){
    //             p_satu = 0;
    //         }
    //         if(p_satu=== undefined){
    //             p_dua = 0;
    //         }
    //         var id = $("#id").val();

    //         // var serializeData = {
    //         //     "id_bujt" :  $("#id_bujt").val(),
    //         //     "id_ruas": $("#id_ruas").val(),
    //         //     "staa": $("#staa").val(),
    //         //     "stae": $("#stae").val(),
    //         //     "temuan" : $("#temuan").val(),
    //         //     "penanganan": $("#penanganan").val(),
    //         //     "catatan": $("#catatan").val(),
    //         //     "persiapan_1": p_satu,
    //         //     "persiapan_2": p_dua,
    //         //     "id": id,
    //         // };

    //         // console.log(serializeData)
    //             url = "<?php //echo base_url(); ?>persiapan/addform";

    //         var text_up = 'menambah';
    //         var params = {"formData": data,"<?php //echo $this->security->get_csrf_token_name(); ?>":"<?php //echo $this->security->get_csrf_hash(); ?>"};
    //         $.post(url, params).done(function (data) {
    //             console.log(data)

    //             $(".alert").css("display","block")
    //             $('#modal-tambah').modal('hide');
    //             // alert(data[0].status)
    //             if(id != ''){
    //                 text_up = 'merubah';
    //             }
    //             $("#text_i").empty();
    //             if(data=='sukses'){
    //                 $("#text_i").append("<strong>Berhasil !</strong> "+text_up+" data")
    //             }else{
    //                 $("#text_i").append("<strong>Ggagal !</strong> "+text_up+" data")
    //             }
    //             table.ajax.reload();
    //             setTimeout(close_alert, 3000);
    //         });
    //     // } else {
    //     //     $("label.error").css("color","red");
    //     // }
    // }
    function getFormUser(urole, kat, idgiat) {

        // var urole = e.value;
        urlWilayah = (idgiat == '') ? urole + "/" + iduser + "/" + kat : urole + "/" + iduser + "/" + kat + "/" + idgiat

        $("#form-user").empty();
        // $("#form-di").empty();

        $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) {

              $('#kd_satker').val(satker).selectpicker('refresh');
            $('#prov').val('').selectpicker('refresh');
            $('#kab').val('').selectpicker('refresh');
            $('#kec').val('').selectpicker('refresh');
            $('#desa').val('').selectpicker('refresh');


              $("#prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                        // e.stopPropagation();

                        var selected_value = $("[id='prov']").toArray().map(x => $(x).val());
                        if (selected_value[0].length > 1) {

                            var params = selected_value[0].toString();
                            params = params.replace(/,/g, "::");

                            refreshMultibootvar('kab', 20, 'kd_prov', params);

                        } else {
                            refreshSelectbootvar('kab', 20, 'kd_prov', selected_value[0][0]);
                        }
                    });


            $("#kab").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                // e.stopPropagation();

                var selected_value = $("[id='kab']").toArray().map(x => $(x).val());
                if (selected_value[0].length > 1) {

                    var params = selected_value[0].toString();
                    params = params.replace(/,/g, "::");

                    refreshMultibootvar('kec', 21, 'kd_kabkot', params);

                } else {
                    refreshSelectbootvar('kec', 21, 'kd_kabkot', selected_value[0][0]);
                }



            });
            $("#kec").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                // e.stopPropagation();
                var selected_value = $("[id='kec']").toArray().map(x => $(x).val());
                if (selected_value[0].length > 1) {

                    var params = selected_value[0].toString();
                    params = params.replace(/,/g, "::");

                    refreshMultibootvar('desa', 22, 'kd_camat', params);

                } else {
                    //refreshSelectbootvar('kec', 21, 'kd_kabkot', selected_value[0][0]);
                    refreshSelectbootvar('desa', 22, 'kd_camat', selected_value[0][0]);
                }


            });
            //   $('#prov').val(prov).selectpicker('refresh');
            //    $('#kab').val('').selectpicker('refresh');
            //    $('#kec').val('').selectpicker('refresh');
            //    $('#desa').val('').selectpicker('refresh');


        });


        $.get("<?php echo base_url(); ?>" + "persiapan/bindWilayah/" + urlWilayah)
            .done(function (data) {

                // setTimeout( function () {
                    $("#form-user").html(data);
                // }, 500);

                $('.bootstrap-select').selectpicker('refresh');


            });



        // $("#id_user_groups").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('kd_satker', 37, 'id_user', iduser);
        // });

        // $("#prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('kab', 20, 'kd_prov', this.value);
        // });
        // $("#kab").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('kec', 21, 'kd_kabkot', this.value);
        // });
        // $("#kec").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('desa', 22, 'kd_camat', this.value);
        // });



    }




    function validasiEkstensi(id, ext = '') {
        var inputFile = document.getElementById(id);
        var pathFile = inputFile.value;
        var file_size = inputFile.files[0].size / 1024;
        var bts = ''
        var txt = ''
        // var ekstensiOk = /(\.jpg|\.jpeg|\.png|\.gif)$/i;

        if (ext != '') {
            ekstensiOk = /(\.pdf)$/i;
            bts = 200
            txt = 'Silakan upload file yang dengan ekstensi .pdf'

        } else {
            ekstensiOk = /(\.jpg|\.jpeg|\.png|\.gif)$/i;
            bts = 500
            txt = 'Silakan upload file yang dengan ekstensi .png , .jpg,'


        }

        if (file_size > bts) {
            swal({
                // title: "Anda yakin menghapus data ?",
                text: 'Ukuran file tidak boleh lebih dari ' + bts + ' Kb',
                icon: "warning",
                buttons: false,
                timer: 2000,
                dangerMode: true,
            })
            $(inputFile).val(''); //for clearing with Jquery
            return
        }
        if (!ekstensiOk.exec(pathFile)) {
            swal({
                // title: "Anda yakin menghapus data ?",
                text: txt,
                icon: "warning",
                buttons: false,
                timer: 2000,
                dangerMode: true,
            })
            // alert('Silakan upload file yang dengan ekstensi .pdf/.jpg/.png/.gif');
            inputFile.value = '';
            return false;
        } else {
            // Preview gambar
            if (inputFile.files && inputFile.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    // document.getElementById('upload_peta_di').innerHTML = '<iframe src="'+e.target.result+'" class="img-responsive" style="width:100%;height:200px;"/>';
                };
                reader.readAsDataURL(inputFile.files[0]);
            }
        }
    }

    function shows() {
        $(".error").text('')
        $(".error").css({
            'font-size': '14px',
            'color': '#353c4e'
        })
        $(".form-tambah .form-group input").prop('readonly', false)
        $(".form-tambah .form-group input").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group select").prop('disabled', false)
        $(".form-tambah .form-group select").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group textarea").prop('readonly', false)
        $(".form-tambah .form-group textarea").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group button").prop('disabled', false)
        $(".form-tambah .form-group button").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group").css({
            "border-bottom": "0px"
        })
        $(".form-tambah .form-group input").val('')
        $(".form-tambah .form-group select").val('')
        $("#gb_npwp").html('')
        $(".form-tambah .form-group textarea").val('')
        $("#file_npwp").show()
        $("#fot_musya").show()
        $("#fot_sosial").show()
    }

    function bs_input_file() {
        $(".input-file").before(
            function () {
                if (!$(this).prev().hasClass('input-ghost')) {
                    var element = $("<input type='file' class='input-ghost' style='visibility:hidden; height:0'>");
                    element.attr("name", $(this).attr("name"));
                    element.change(function () {
                        element.next(element).find('input').val((element.val()).split('\\').pop());
                    });
                    $(this).find("button.btn-choose").click(function () {
                        element.click();
                    });
                    $(this).find("button.btn-reset").click(function () {
                        element.val(null);
                        $(this).parents(".input-file").find('input').val('');
                    });
                    $(this).find('input').css("cursor", "pointer");
                    $(this).find('input').mousedown(function () {
                        $(this).parents('.input-file').prev().click();
                        return false;
                    });
                    return element;
                }
            }
        );
    }
    $(function () {
        bs_input_file();
    });


    // function formatNpwp(z) {
    //     // console.log(z);
    //     if (z === null) {
    //         $("#no_npwp").val('');
    //     } else {
    //         var dt = z.value
    //         if (typeof (dt) === 'undefined' || dt === null) {
    //             $("#no_npwp").val('')
    //             dt = z;
    //             $("#no_npwp").val(dt.replace(/(\d{2})(\d{3})(\d{3})(\d{1})(\d{3})(\d{3})/, '$1.$2.$3.$4-$5.$6'))
    //         }

    //         if (!dt.startsWith('0.')) {
    //             dt = '0.' + dt.replace(/^0\.?/, ''); // Remove existing 0 or 0. to prevent duplicates
    //         }

    //         // alert(value.length)
    //         if (dt.length === 4) {  // After 0.[first digit]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length === 8) {  // After 0.XX.[next 3 digits]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length === 12) {  // After 0.XX.XXX.[next 3 digits]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length === 14) {  // After 0.XX.XXX.XXX.[1 digit]
    //             $("#no_npwp").val(dt + '-');
    //         } else if (dt.length === 18) {  // After 0.XX.XXX.XXX.X-[3 digits]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length > 20) {
    //             $("#no_npwp").val(dt.replace(/^0\.(\d{2})\.?(\d{3})\.?(\d{3})\.?(\d{1})-?(\d{3})\.?(\d{3})$/, '0.$1.$2.$3.$4-$5.$6'));
    //         }
    //         //  return value.replace(/(\d{2})(\d{3})/, '$1.$2.')
    //         //  return value.replace(/(\d{2})(\d{3})(\d{3})(\d{1})(\d{3})(\d{3})/, '$1.$2.$3.$4-$5.$6');
    //     }
    // }

    function formatNpwp(z) {
    // Handle null input
    if (z === null) {
        $("#no_npwp").val('');
        return;
    }

    // Get the input value (whether z is an element or direct value)
    let value = typeof z === 'object' && z.value !== undefined ? z.value : z;

    // Remove all non-digit characters to get clean digits for processing
    const digits = value.replace(/\D/g, '');

    // If no digits, clear the field
    if (digits.length === 0) {
        $("#no_npwp").val('');
        return;
    }

    // Format the digits according to NPWP pattern: X.XX.XXX.XXX.X-XXX.XXX
    let formatted = '';

    // First digit
    formatted += digits.substring(0, Math.min(1, digits.length));

    // Add dot after first digit
    if (digits.length > 1) {
        formatted += '.' + digits.substring(1, Math.min(3, digits.length));
    }

    // Add dot after next 2 digits
    if (digits.length > 3) {
        formatted += '.' + digits.substring(3, Math.min(6, digits.length));
    }

    // Add dot after next 3 digits
    if (digits.length > 6) {
        formatted += '.' + digits.substring(6, Math.min(9, digits.length));
    }

    // Add dot after next 3 digits
    if (digits.length > 9) {
        formatted += '.' + digits.substring(9, Math.min(10, digits.length));
    }

    // Add hyphen after next 1 digit
    if (digits.length > 10) {
        formatted += '-' + digits.substring(10, Math.min(13, digits.length));
    }

    // Add dot after next 3 digits
    if (digits.length > 13) {
        formatted += '.' + digits.substring(13);
    }

    // Set the formatted value
    $("#no_npwp").val(formatted);
}

    function dtTambahRow(kat, id) {
        // Show loading overlay immediately
        showFormLoading();

        $('#modal-tambah').modal({
            backdrop: 'static',
            keyboard: false
        })
        //  $("#frm-tambah").validate().resetForm();
        //  $('#frm-tambah').trigger('reset');
        //  $('#frm-tambah')[0].reset();

        // $('.card').each(function() {
        //     if($(this).val() == '') {
        //        $(this).css('background-color' , '#FF0000');
        //     }
        // });

        shows()




        $(".form-tambah #valid_2 input").prop('readonly', true)
        $(".form-tambah #valid_2 select").prop('disabled', true)
        $(".form-tambah #valid_2 textarea").prop('readonly', true)
        $(".form-tambah #valid_2 button").prop('disabled', true)

        var prov = ''
        var kab = ''
        var kec = ''
        var kel = ''
        var jen_penerima = ''
        var j_legalitas = ''
        var t_legalitas = ''
        var j_kelamin = ''
        var j_irigasi = ''
        var k_irigasi = ''
        var lbp = ''
        var jen_penerima = ''
        var nm_dae_irigasi = ''
        var bbws = ''



        //  initCombobox('prov', 19);
        initCombobox('bbws', 23);
        initCombobox('jen_penerima', 30);
        initCombobox('j_legalitas', 24);
        initCombobox('t_legalitas', 42);
        initCombobox('j_irigasi', 27);
        initCombobox('k_irigasi', 28);
        initCombobox('j_kelamin', 26);
        initCombobox('lbp', 29);

        //  initCombobox('nm_dae_irigasi', 31);
        //alert(dt[0].jns_kelamin);
        if (group == 2) {
            $("#btn_tambah_edit").css('display', 'block')
            $("#btn_verifikasi").css('display', 'none')
        } else {
            $("#btn_tambah_edit").css('display', 'block')
        }

        if (group == 7) {
            $(".form-tambah .form-group #prov").prop('disabled', true)
            $(".form-tambah .form-group #bbws").prop('disabled', true)
        }

        $('#nm_tpm').prop('readonly', true);
        if (kat == 'tambah') {
            $("#profil_p3_tgai").empty();
            $("#kehadiran_sosialisasi_tingkat_penerima_p3_tgai").empty();
            $("#musyawarah_desa").empty();
            $("#id").val('')
            $('#nm_tpm').val(nama_user);
            bbws = satker
            prov = kd_prov_ses
            //  refreshSelectbootvar('prov', 36, 'kd_satker', bbws);
            //refreshSelectbootvar('kab', 20, 'kd_prov', prov);
            getFormUser(group, kat, '', thangs);

            // Hide loading for add mode after form is initialized
            setTimeout(function() {
                hideFormLoading();
            }, 800);

            //  $('.tbhItem').text('Tambah Paket Pagu');
        } else {

            $('#prov').selectpicker('refresh');
            $('#kab').selectpicker('refresh');
            $('#kec').selectpicker('refresh');
            $('#desa').selectpicker('refresh');
            $('#nm_dae_irigasi').selectpicker('refresh');
            $('#k_irigasi').selectpicker('refresh');

            // Try asynchronous approach first, fallback to synchronous if needed
            tampil_data('v_all_kegiatan', 'id_giat', id, function(dt) {
                if (!dt || !dt[0]) {
                    console.warn('Async call failed, falling back to synchronous approach');
                    // Fallback to synchronous approach
                    var dt_sync = tampil_data('v_all_kegiatan', 'id_giat', id);
                    if (dt_sync && dt_sync[0]) {
                        processFormData(dt_sync);
                    } else {
                        console.error('Both async and sync calls failed');
                        hideFormLoading(); // Hide loading on error
                        alert('Error loading data. Please try again.');
                        return;
                    }
                } else {
                    processFormData(dt);
                }
            });

            // Function to process the form data regardless of how it was loaded
            function processFormData(dt) {
                valid_1 = dt[0].valid_1_p1
                if (valid_1 == 'Data Sudah Lengkap') {
                    $(".form-tambah #valid_2 input").prop('readonly', false)
                    $(".form-tambah #valid_2 select").prop('disabled', false)
                    $(".form-tambah #valid_2 textarea").prop('readonly', false)
                    $(".form-tambah #valid_2 button").prop('disabled', false)
                }
                j_legalitas = dt[0].id_jnslgl
                jen_penerima = dt[0].id_jnsp3a
                t_legalitas = dt[0].thn_berdiri
                j_kelamin = dt[0].jns_kelamin
                j_irigasi = dt[0].id_jnsdi
                k_irigasi = dt[0].id_kwdi
                lbp = dt[0].pendidikan
                nm_dae_irigasi = dt[0].kd_di
                prov = dt[0].kd_prov
                kab = dt[0].kd_kabkot
                kec = dt[0].kd_kec
                kel = dt[0].kd_desa
                bbws = dt[0].kd_satker

                refreshCombobox5('nm_dae_irigasi', 31, 'kd_kec::tahun', kec +  '::' + thangs, nm_dae_irigasi);

                // Load file data with fallback
                tampil_data('dok_kegiatanp3a', 'id_giat', id, function(dt_file) {
                    if (!dt_file) {
                        // Fallback to sync for file data
                        dt_file = tampil_data('dok_kegiatanp3a', 'id_giat', id);
                    }

                    if (dt_file && dt_file.length > 0) {
                        for (let i = 0; i < dt_file.length; i++) {
                            var judul = dt_file[i].judul_dok;
                            var filepath = dt_file[i].filepath.replace("/sismonp3tgai/uploads/", "/uploads/");
                            var timestamp = new Date().getTime();
                            $("#" + judul).html("<a class='fancybox' target='_blank' href='" + filepath + "/" + dt_file[i].filename +
                                "'><img 'class='img-responsive' style='width:100%;height:200px;' src='" + filepath + "/" + dt_file[
                                    i].filename + "?t=" + timestamp + "'></a>")
                            $("#F_" + judul).val(judul)
                        }
                    }

                    // Continue with form population after both AJAX calls complete
                    populateFormData(dt[0]);
                });
            }

            // Move form population to separate function for better organization
            function populateFormData(data) {

            $('#nm_penerima').val(data.nm_pppa);
            $('#id').val(data.id_giat);
            $('#nm_ketua').val(data.nm_ketua);
            $('#ala_ketua').val(data.alamat_ketua);
            $('#no_ketua').val(data.kontak_ketua);
            formatNpwp(data.npwp_p3a)
            // $('#no_npwp').val(data.npwp_p3a);
            $('#nm_tpm').val(data.nm_tpm);
            $('#jum_dp').val(data.jml_desa);
            $('#no_p').val(data.no_pntp_p3a);
            if (data.tgl_pntp_p3a != null) {
                $('#tgl_pen').val(data.tgl_pntp_p3a.split(' ')[0]);
            }

            // Set dropdown values immediately after loading data
            $('#j_kelamin').val(j_kelamin).selectpicker('refresh');
            $('#lbp').val(lbp).selectpicker('refresh');
            $('#bbws').val(bbws).selectpicker('refresh');
            $('#j_legalitas').val(j_legalitas).selectpicker('refresh');
            $('#t_legalitas').val(t_legalitas).selectpicker('refresh');
            $('#j_irigasi').val(j_irigasi).selectpicker('refresh');
            $('#k_irigasi').val(k_irigasi).selectpicker('refresh');
            $('#jen_penerima').val(jen_penerima).selectpicker('refresh');

            $('#ala_tpm').val(data.alamat_tpm);
            if (data.tgl_sosialisasi != null) {
                $('#tgl_pelaksana').val(data.tgl_sosialisasi.split(' ')[0]);
            }
            $('#jum_pes_l').val(data.sos_pria);
            $('#jum_pes_p').val(data.sos_wanita);
            var has = parseInt(data.sos_pria) + parseInt(data.sos_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_t').val(has);
            $('#jum_lan').val(data.sos_lansia);
            // $('#nm_dae_irigasi').val();
            // $('#j_irigasi').val();
            // $('#k_irigasi').val();
            $('#jum_ket_l').val(data.jm_pengurus_pria);
            $('#jum_ket_p').val(data.jm_pengurus_wanita);
            var has = parseInt(data.jm_pengurus_pria) + parseInt(data.jm_pengurus_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_ket_pgrus').val(has);
            $('#jum_ben_p').val(data.jm_bendahara_wanita);
            $('#jum_ben_l').val(data.jm_bendahara_pria);
            $('#jum_sek_p').val(data.jm_sekretaris_wanita);
            $('#jum_sek_l').val(data.jm_sekretaris_pria);
            $('#jum_anggota').val(data.jml_anggota);

            $('#jum_tim_per_l').val(data.timsiap_pria);
            $('#jum_tim_per_p').val(data.timsiap_wanita);
            var has = parseInt(data.timsiap_pria) + parseInt(data.timsiap_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_total').val(has);
            $('#jum_tim_pelak_l').val(data.timlak_pria);
            $('#jum_tim_pelak_p').val(data.timlak_wanita);

            has = parseInt(data.timlak_pria) + parseInt(data.timlak_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_pelak').val(has);
            $('#jum_tim_pngws_l').val(data.timwas_pria);
            $('#jum_tim_pngws_p').val(data.timwas_wanita);

            has = parseInt(data.timwas_pria) + parseInt(data.timwas_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_pngws').val(has);
            if (data.musy_desa != null) {
                $('#tgl_pelak').val(data.musy_desa.split(' ')[0]);
            }

            $('#jum_pese_l').val(data.musdes_pria);
            $('#jum_pese_p').val(data.musdes_wanita);
            $('#jum_pese_lan').val(data.musdes_lansia);
            has = parseInt(data.musdes_pria) + parseInt(data.musdes_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tot_pes').val(has);

            // Load and set location-based dropdowns with proper sequencing
            setTimeout(function() {
                // Load province first
                refreshCombobox5('prov', 36, 'kd_satker::tahun', bbws + '::' + thangs, prov);

                // Then load kabupaten after province is set
                setTimeout(function() {
                    refreshCombobox5('kab', 20, 'kd_prov::tahun', prov + '::' + thangs, kab);

                    // Then load kecamatan after kabupaten is set
                    setTimeout(function() {
                        refreshCombobox5('kec', 21, 'kd_kabkot::tahun', kab + '::' + thangs, kec);

                        // Finally load desa after kecamatan is set
                        setTimeout(function() {
                            refreshCombobox5('desa', 22, 'kd_camat::tahun', kec + '::' + thangs, kel);

                            // Hide loading after all location dropdowns are loaded
                            setTimeout(function() {
                                hideFormLoading();
                            }, 500);
                        }, 300);
                    }, 300);
                }, 300);
            }, 500);

            getFormUser(group, kat, id);
            }




        }

        $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) {

            // Only set values if they haven't been set already (for edit mode)
            if (kat !== 'tambah') {
                // Re-apply values in case they were lost during modal initialization
                setTimeout(function() {
                    $('#bbws').val(bbws).selectpicker('refresh');
                    $('#j_legalitas').val(j_legalitas).selectpicker('refresh');
                    $('#t_legalitas').val(t_legalitas).selectpicker('refresh');
                    $('#j_kelamin').val(j_kelamin).selectpicker('refresh');
                    $('#j_irigasi').val(j_irigasi).selectpicker('refresh');
                    $('#k_irigasi').val(k_irigasi).selectpicker('refresh');
                    $('#jen_penerima').val(jen_penerima).selectpicker('refresh');
                    $('#nm_dae_irigasi').val(nm_dae_irigasi).selectpicker('refresh');
                    $('#lbp').val(lbp).selectpicker('refresh');

                    // Set location dropdowns if they exist
                    if (prov) $('#prov').val(prov).selectpicker('refresh');
                    if (kab) $('#kab').val(kab).selectpicker('refresh');
                    if (kec) $('#kec').val(kec).selectpicker('refresh');
                    if (kel) $('#desa').val(kel).selectpicker('refresh');
                }, 200);
            }





            $("#prov").on("changed.bs.select", function (e, clickedIndex, newValue, oldValue) {

                $('#kec').val(kec).selectpicker('refresh');
                $('#desa').val(kel).selectpicker('refresh');
                refreshCombobox5('kab', 20, 'kd_prov::tahun', this.value +  '::' + thangs, '');
                refreshCombobox5('nm_dae_irigasi', 31, 'kd_prov::tahun', this.value +  '::' + thangs, '');
                // refreshSelectbootvar('kab', 20, 'kd_prov', this.value);
                // refreshSelectbootvar('nm_dae_irigasi', 31, 'kd_prov', this.value);

            });
            $("#kab").on("changed.bs.select", function (e, clickedIndex, newValue, oldValue) {

                $('#desa').val(kel).selectpicker('refresh');
                refreshCombobox5('kec', 21, 'kd_kabkot::tahun', this.value +  '::' + thangs, '');
                refreshCombobox5('nm_dae_irigasi', 31, 'kd_kabkot::tahun', this.value +  '::' + thangs, '');
                // refreshSelectbootvar('kec', 21, 'kd_kabkot', this.value);
                // refreshSelectbootvar('nm_dae_irigasi', 31, 'kd_kabkot', this.value);

            });
            $("#kec").on("changed.bs.select", function (e, clickedIndex, newValue, oldValue) {

                refreshCombobox5('desa', 22, 'kd_camat::tahun', this.value +  '::' + thangs, '');
                refreshCombobox5('nm_dae_irigasi', 31, 'kd_kec::tahun', this.value +  '::' + thangs, '');
                // refreshSelectbootvar('desa', 22, 'kd_camat', this.value);
                // refreshSelectbootvar('nm_dae_irigasi', 31, 'kd_kec', this.value);
            });
        });

        //alert(group);



        // $("#bbws").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {

        //     refreshSelectbootvar('prov', 36, 'kd_satker', this.value);
        //  });



        if (kat == 'detail') {
            // alert('fsdfcsf');
            $(".form-tambah .form-group input").prop('readonly', true)
            $(".form-tambah .form-group input").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group select").prop('disabled', true)
            $(".form-tambah .form-group select").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group textarea").prop('readonly', true)
            $(".form-tambah .form-group textarea").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group button").prop('disabled', true)
            $(".form-tambah .form-group button").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group").css({
                "border-bottom": "1px solid #80808054"
            })
            $("#file_npwp").hide()
            $("#fot_musya").hide()
            $("#fot_sosial").hide()
            if (group == 2) {
                $("#btn_tambah_edit").css('display', 'none')
                $("#btn_verifikasi").css('display', 'block')
                $("#btn_verifikasi button").prop('disabled', true)
                $("#btn_verifikasi").attr('title', 'Data belum lengkap, tidak bisa mem-validasi')
                if (dt[0].valid_1_p1 == 'Data Sudah Lengkap' && dt[0].valid_2_p1 == 'Data Sudah Lengkap') {
                    $("#btn_verifikasi button").prop('disabled', false)
                    $("#btn_verifikasi").attr('title', '')

                }
            } else {
                $("#btn_tambah_edit").css('display', 'none')

            }


        }
        $(".form-tambah #jum_tim_total").prop('readonly', true)
        $(".form-tambah #jum_tim_pelak").prop('readonly', true)
        $(".form-tambah #jum_tim_pngws").prop('readonly', true)
        $(".form-tambah #jum_t").prop('readonly', true)
        $(".form-tambah #jum_tot_pes").prop('readonly', true)

        $(".decformat").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });


        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal-tambah').modal('show');
        $('.tbhItem').text('Form persiapan');
        // $("#urutans").empty();
        // $("#urutans").val("2");

        // Ensure all selectpickers are properly initialized after modal is shown
        setTimeout(function() {
            $('.selectpicker').selectpicker('refresh');
        }, 300);

        // Fallback: Hide loading after maximum 5 seconds to prevent stuck loading
        setTimeout(function() {
            if ($('#form-loading-overlay').length > 0) {
                console.warn('Loading timeout reached, hiding loading overlay');
                hideFormLoading();
            }
        }, 5000);

        //--- end dt tambah row ---

    }

    function handlePaste(e) {
        var clipboardData, pastedData;

        // Stop data actually being pasted into div
        e.stopPropagation();
        e.preventDefault();

        // Get pasted data via clipboard API
        clipboardData = e.clipboardData || window.clipboardData;
        pastedData = clipboardData.getData('Text');

        // Do whatever with pasteddata
        formatNpwp(pastedData);
    }

    document.getElementById('no_npwp').addEventListener('paste', handlePaste);

    function update_verifikasi(stat) {
        var mustKMB = "<?php echo $this->session->users['id_user']; ?>";
        var id = $("#id").val()
        // var url = "<?php //echo base_url(); ?>persiapan/update_data/" + id + "/" + stat + "/" + mustKMB;
        var text = ''
        if (stat == 1) {
            text = "Anda yakin data tersebut valid ?"
        } else {
            text = "Anda yakin data tersebut tidak valid ?"
        }


        data_post = {
            "id": id,
            "stat": stat,
            "mustKMB": mustKMB,
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>",
        }

        swal({
            title: text,
            //   text: tex,
            icon: "info",
            buttons: true,
            dangerMode: true,
        })
            .then((willDelete) => {
                if (willDelete) {

                        jQuery.ajax({
                            // contentType: 'application/json',
                            type: "post",
                            dataType: "json",
                            data: data_post,
                            // crossDomain: true,
                            url: "<?= base_url('persiapan/update_data'); ?>",
                            success: function(data) {
                                if (data == 0) {
                                    swal({
                                        title: "",
                                        text: "Berhasil Verifikasi",
                                        icon: "success",
                                        showConfirmButton: false,
                                        timer: 3000,
                                        type: "success"
                                    });
                                    table.ajax.reload();
                                    $('#modal-tambah').modal('hide');
                                }


                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                alert(xhr.status);
                                alert(thrownError);
                            }
                        });

                    // console.log('dmasdhak');
                    // $.get(url).done(function (data) {
                    //     if (data == 0) {
                    //         swal({
                    //             title: "",
                    //             text: "Berhasil Merubah Status",
                    //             icon: "success",
                    //             showConfirmButton: false,
                    //             timer: 3000,
                    //             type: "success"
                    //         });
                    //         table.ajax.reload();
                    //         $('#modal-tambah').modal('hide');
                    //     }
                    // })
                } else {
                    swal("Anda tidak Memverifiaksi data");
                }
            })
    }

    function refreshCombobox5(divname, refindex, refresh_field, refresh_value, selvalue, $rev = 1) {
        var url = null;
        if ($rev === 1) {
            url = urlBase + "lookup/refreshlook4/" + refindex + "/" + refresh_field + "/" + refresh_value;
        } else {
            url = urlBase + "lookup/refreshlook4/" + refindex + "/" + refresh_field + "/" + refresh_value + "/" + 0;
        }
        wgiAjaxCache(url, function (ajaxdata) {
            try {
                jdata = JSON.parse(ajaxdata);
                $('#' + divname).empty();
                $('#' + divname).append(new Option("--Pilih--", ''));
                $.each(jdata, function (i, el) {
                    $('#' + divname).append(new Option(el.val, el.id));
                });

                // Ensure selectpicker is properly refreshed
                $('#' + divname).selectpicker('refresh');

                // Set the selected value after refresh
                if (selvalue) {
                    setTimeout(function() {
                        $('#' + divname).val(selvalue);
                        $('#' + divname).selectpicker('refresh');

                        // Trigger change event to ensure proper display
                        $('#' + divname).trigger('change');
                    }, 100);
                }
            } catch (error) {
                console.error('Error parsing JSON in refreshCombobox5:', error);
                console.error('Response data:', ajaxdata);
            }
        });
    }

    function dtDeleteRow(id) {

        swal({
            title: "Data di semua tahapan akan terhapus, apakah anda yakin ingin menghapus  ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
            .then((willDelete) => {
                if (willDelete) {
                    var url = ''
                    url = "<?php echo base_url(); ?>" + "persiapan/ajax_delete/" + id;
                    $.get(url).done(function (data) {
                        if (data == 0) {
                            swal({
                                title: "",
                                text: "Berhasil Menghapus Data",
                                icon: "success",
                                showConfirmButton: false,
                                timer: 3000,
                                type: "success"
                            });
                            table.ajax.reload();
                        }
                    })
                } else {
                    swal("Anda Tidak Menjadi Menghapus Data");
                }
            });
    }

    function hitung(yuhu, hasil) {
        var total = 0;
        var yuhu = yuhu.split('+')
        var satu = $("#" + yuhu[0]).val()
        var dua = $("#" + yuhu[1]).val()
        if (isNaN(parseInt(satu))) {
            satu = 0
        }
        if (isNaN(parseInt(dua))) {
            dua = 0
        }
        var to = parseInt(satu) + parseInt(dua)
        $("#" + hasil).val(to)
        // for (let i = 0; i < satu.length; i++) {
        //     var satu =  $("#"+satu[i]).val()
        //     if (isNaN(parseInt(satu))) {
        //       satu = 0
        //   }
        //     parseInt(total) += satu;
        //     }
        //     alert(total)



    }

    function isNumber(evt, a) {

        evt = (evt) ? evt : window.event;
        var charCode = (evt.which) ? evt.which : evt.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    }






    // }


    function get_extentsion_file(file) {
        var extension = file.substr((file.lastIndexOf('.') + 1));
        switch (extension) {
            case 'jpg':
            case 'png':
            case 'PNG':
            case 'jpeg':
            case 'gif':
            case 'JPG':
                return 'feather icon-image'; // There's was a typo in the example where
                break; // the alert ended with pdf instead of gif.
            case 'zip':
            case 'rar':
                //alert('was zip rar');
                return 'feather icon-archive';
                break;
            case 'pdf':
                return 'feather icon-file-text';
            case 'xlsx':
                return 'feather icon-file-text';
                break;
            default:
                return 'feather icon-file-text';

        }
    }

    function hapus_lampiran(id) {
        if (confirm('Yakin untuk menghapus data ini?')) {

            var url = "<?php echo base_url(); ?>" + "persiapan/hps_lampiran/" + id;
            var params = {
                "formData": {},
                "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
            };
            $.post(url, params)
                .done(function (data) {
                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload()
                    var tab = $('#table_id2').DataTable();
                    tab.ajax.reload();;
                })
                .fail(function () {
                    alert("error");
                })
        }
    }

    function tampil_data(table, colum, id, callback) {
        var url = ''
        var tadata = ''
        urls = "<?php echo base_url(); ?>persiapan/tampildata/" + table + "/" + colum + "/" + id;

        // If callback is provided, use async approach
        if (typeof callback === 'function') {
            $.ajax({
                url: urls,
                type: "GET",
                dataType: "json",
                async: true,
                success: function (data) {
                    callback(data);
                },
                error: function (xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);
                    console.error('URL:', urls);
                    // Don't show alert for async calls, just log the error
                    console.warn('Failed to load data from:', urls);
                    callback(null);
                }
            });
        } else {
            // Fallback to synchronous for backward compatibility (deprecated)
            $.ajax({
                url: urls,
                type: "GET",
                dataType: "json",
                async: false,
                success: function (data) {
                    tadata = data;
                },
                error: function (xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);
                    console.error('URL:', urls);
                    // For sync calls, we still need to handle the error gracefully
                    tadata = null;
                }
            });
            return tadata;
        }
    }

    function cek_kolom() {
        $(".form-tambah #valid_2 input").prop('readonly', true)
        $(".form-tambah #valid_2 select").prop('disabled', true)
        $(".form-tambah #valid_2 textarea").prop('readonly', true)
        $(".form-tambah #valid_2 button").prop('disabled', true)
        if ($(".form-tambah").valid() == true) {
            $(".form-tambah #valid_2 input").prop('readonly', false)
            $(".form-tambah #valid_2 select").prop('disabled', false)
            $(".form-tambah #valid_2 textarea").prop('readonly', false)
            $(".form-tambah #valid_2 button").prop('disabled', false)
        }
        var lab = $("label").text().split("This field is required.").length - 1;
        if (lab == 0) {

        }
        $(".form-tambah label").remove()
        $(".form-tambah label").text()
    }



    $(document).ready(function () {
        // Suppress error alerts for better user experience in Chrome
        window.originalAlert = window.alert;
        window.alert = function(message) {
            if (message && message.indexOf('Error loading data:') !== -1) {
                console.warn('AJAX Error suppressed:', message);
                return;
            }
            window.originalAlert(message);
        };

        isBlocked = sessLocked;
        if (group == 7) {

            if (isBlocked == 1) {
                $("#btnTambah").hide()
            } else {
                $("#btnTambah").show()
            }

        }



        $(".form-tambah").validate({
            ignore: ':not(select:visible,button:visible, input:visible, textarea:visible, .selectpicker)',
            rules: {
                bbws: {
                    required: true
                }, //profil
                prov: {
                    required: true
                },
                kab: {
                    required: true
                },
                kec: {
                    required: true
                },
                desa: {
                    required: true
                },
                jen_penerima: {
                    required: true
                },
                nm_penerima: {
                    required: true
                },
                j_legalitas: {
                    required: true
                },
                t_legalitas: {
                    required: true
                },
                nm_ketua: {
                    required: true
                },
                ala_ketua: {
                    required: true
                },
                no_ketua: {
                    required: true
                },
                no_npwp: {
                    required: true
                },
                file_npwp: {
                    required: false
                },
                nm_dae_irigasi: {
                    required: true
                }, //irigasi
                j_irigasi: {
                    required: true
                },
                k_irigasi: {
                    required: true
                },
                jum_ket_l: {
                    required: true
                }, //kepengurusan
                jum_ket_p: {
                    required: true
                },
                jum_ben_p: {
                    required: true
                },
                jum_ben_l: {
                    required: true
                },
                jum_anggota: {
                    required: true
                },
                jum_tim_per_l: {
                    required: true
                }, //swakelola
                jum_tim_per_p: {
                    required: true
                },
                jum_tim_pelak_l: {
                    required: true
                },
                jum_tim_pelak_p: {
                    required: true
                },
                jum_tim_pngws_l: {
                    required: true
                },
                jum_tim_pngws_p: {
                    required: true
                },
                nm_tpm: {
                    required: true
                }, //tpm
                jum_dp: {
                    required: true
                },
                j_kelamin: {
                    required: true
                },
                lbp: {
                    required: true
                },
                ala_tpm: {
                    required: true
                },
                // file_npwp: { required: true  },
                // jum_tim_total: { required: true  },
                // jum_tim_pelak: {required: true },
                // jum_tim_pngws: {required: true },
                // tgl_pelaksana: { required: true  }, //
                // jum_pes_l: { required: true  },
                // jum_pes_p: { required: true  },
                // jum_t: { required: true  },
                // jum_lan: { required: true  },
                // tgl_pelak: { required: true  },
                // jum_pese_l: { required: true  },
                // jum_pese_p: { required: true  },
                // jum_tot_pes: { required: true  },
                // no_p: { required: true  },
                // tgl_pen: { required: true  },
            }
        });
        $(".form-tambah .form-control").keypress(function () {
            cek_kolom();
        });
        $(".form-tambah .form-control").change(function () {
            cek_kolom();
        });
        // bind_combo_thang(thangs);
        //  tes_1()
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");


        // document.querySelectorAll(".search th").forEach(function (element) {
        //     var title = element.innerText;
        //     element.innerHTML = '<input type="text" class="form-control" placeholder="' + title.trim() +
        //         '">';
        // });

        // table.columns().every(function () {
        //     var that = this;
        //     document.querySelectorAll('input[type=text]').forEach(function (element) {
        //         element.addEventListener("keyup", function (event) {
        //             event.preventDefault();
        //             // console.log('Search index', that.index())
        //             if (that.search() !== this.value) {
        //                 that.search(this.value).draw();
        //             }
        //         });
        //     });

        // });

        $('.fs a').on('click', function (e) {
            e.preventDefault();
            var fs = $(this).text();

            $(".statustahap:first-child").text(fs);
            $(".statustahap:first-child").val(fs);


            table.ajax.reload();

            // return;
            // e.stopPropagation();

        });



        // Add event delegation for better Chrome compatibility
        // Handle dtTambahRow clicks using event delegation
        $(document).on('click', '[onclick*="dtTambahRow"]', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var onclickAttr = $(this).attr('onclick');
            if (onclickAttr) {
                try {
                    // Extract function call from onclick attribute
                    var match = onclickAttr.match(/dtTambahRow\('([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        var kat = match[1];
                        var id = match[2];
                        console.log('Event delegation calling dtTambahRow:', kat, id);
                        dtTambahRow(kat, id);
                    }
                } catch (error) {
                    console.error('Error executing dtTambahRow:', error);
                }
            }
            return false;
        });

        // Handle dtDeleteRow clicks using event delegation
        $(document).on('click', '[onclick*="dtDeleteRow"]', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var onclickAttr = $(this).attr('onclick');
            if (onclickAttr) {
                try {
                    // Extract function call from onclick attribute
                    var match = onclickAttr.match(/dtDeleteRow\('([^']+)'\)/);
                    if (match) {
                        var id = match[1];
                        console.log('Event delegation calling dtDeleteRow:', id);
                        dtDeleteRow(id);
                    }
                } catch (error) {
                    console.error('Error executing dtDeleteRow:', error);
                }
            }
            return false;
        });

        // Fix async AJAX in form submission for Chrome compatibility
        $("#submit").off('submit').on('submit', function (e) {
            e.preventDefault();
            var formData = new FormData(this);

            $.ajax({
                type: 'POST',
                url: "<?php echo base_url('persiapan/insert_data'); ?>",
                data: formData,
                processData: false,
                contentType: false,
                cache: false,
                async: true, // Changed to async for Chrome compatibility
                success: function (data) {
                    var ops = '';
                    if (data.split('_')[1] == 'insert') {
                        ops = 'Menambah';
                    } else {
                        ops = 'Merubah';
                    }
                    if (data.split('_')[0] == 0) {
                        swal({
                            icon: "success",
                            text: "Berhasil " + ops + " Data",
                            showConfirmButton: false,
                            timer: 2000,
                            type: "success"
                        });
                        table.ajax.reload();
                        $('#modal-tambah').modal('hide');
                    } else {
                        swal({
                            title: "",
                            text: "Gagal " + ops + " Data",
                            showConfirmButton: false,
                            timer: 1000,
                            icon: "warning"
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Form submission error:', error);
                    swal({
                        title: "Error",
                        text: "Terjadi kesalahan saat menyimpan data",
                        icon: "error",
                        timer: 2000
                    });
                }
            });
        });



    });
</script>