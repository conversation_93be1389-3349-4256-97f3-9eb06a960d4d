<!-- Slide Right Modal -->
<style>
    #maxx {
        width: 100%;
        height: 100%;
        padding: 0;
        margin:0;
    }
    #maxxs {
        height: 100%;
        border-radius: 0;
        color:#333;
        overflow:auto;
    }

    #tlist_detail_wrapper {
        width: 800px;
        margin: 0 auto;
    }

    #loading-img {
        background: url(<?= base_url(); ?>assets/img/load.gif) center center no-repeat;
        height: 100%;
        z-index: 1000;
    }

    .overlay {
        background: #e9e9e9;
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        opacity: 0.5;
    }

    input[type='number'] {
        -moz-appearance:textfield;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }

    .bd-example-modal-lg .modal-dialog{
        background-color: transparent;
        display: table;
        position: relative;
        margin: 0 auto;
        top: calc(50% - 24px);
        z-index: 999999;
    }

    .bd-example-modal-lg .modal-dialog .modal-content{
        background-color: transparent;
        border: none;
        z-index: 999999;
    }

</style>

<div class="modal fade rotate" id="modal-edit-detail"  tabindex="-1" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg" id="maxx">
        <div class="modal-content" id="maxxs">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title tbhItem">Edit Detail Paket Pagu Indikatif</h3>
                </div>
                <div class="block-content">
                    <!--                    <h2 class="content-heading border-bottom mb-4 pb-2 rujuk">Rujukan</h2>
                                        <hr class="rujuk">
                                        <div class="row rujukz">


                                            <div class="col-sm-12">
                                                <div class="block">
                                                    <ul class="nav nav-tabs nav-tabs-right" data-toggle="tabs">
                                                        <li class="active" onclick="tabirms('4')">
                                                            <a href="#btabswo-static2-wirmsv3">IRMSv3</a>
                                                        </li>
                                                        <li onclick="tabprogram('4')">
                                                            <a href="#btabswo-static2-weprogram">e-PROGRAM</a>
                                                        </li>
                                                        <li  onclick="tabrenstra('4')">
                                                            <a href="#btabswo-static2-wrenstra">Renstra</a>
                                                        </li>
                                                        <li onclick="tabdpr('4')">
                                                            <a href="#btabswo-static2-wusulandpr">Usulan DPR</a>
                                                        </li>
                                                        <li onclick="tabpemda('4')">
                                                            <a href="#btabswo-static2-wusulanpemda">Usulan PEMDA</a>
                                                        </li>
                                                        <li class="pull-left">
                                                            <ul class="block-options push-10-t push-10-l">
                                                                li>
                                                                    <button type="button" data-toggle="block-option" data-action="close"><i class="si si-close"></i></button>
                                                                </li
                                                                <li>
                                                                    <button type="button" data-toggle="block-option" data-action="content_toggle"><i class="si si-arrow-up"></i></button>
                                                                </li>
                                                                li>
                                                                    <button type="button" data-toggle="block-option" data-action="refresh_toggle" data-action-mode="demo"><i class="si si-refresh"></i></button>
                                                                </li>
                                                                <li>
                                                                    <button type="button" data-toggle="block-option" data-action="fullscreen_toggle"><i class="si si-size-fullscreen"></i></button>
                                                                </li
                                                            </ul>
                                                        </li>
                                                    </ul>
                                                    <div class="block-content tab-content">

                                                        <div class="tab-pane active" id="btabswo-static2-wirmsv3" style="overflow:scroll">
                                                            <h4 class="font-w300 push-15">WP IRMSv3</h4>
                                                            <p>
                                                            <table class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer" id="tirmsv3_wp4">
                                                                <thead>
                                                                    <tr>
                                                                        <th>ID</th>
                                                                        <th>PMS TREATMENT</th>
                                                                        <th>TREATMENT COST</th>
                                                                        <th>PMS BUDGET CAT</th>
                                                                        <th>SCN YEAR NUM</th>
                                                                        <th>ROUTE NAME</th>
                                                                        <th>LENGTH</th>
                                                                        <th>LANE DIR NAME</th>
                                                                        <th>OFFSET FROM</th>
                                                                        <th>OFFSET TO</th>
                                                                        <th>BM REGION</th>
                                                                        <th>BM PROVINCE</th>
                                                                        <th>Penanda</th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                            </p>
                                                        </div>
                                                        <div class="tab-pane" id="btabswo-static2-weprogram">
                                                            <h4 class="font-w300 push-15">e-Program</h4>
                                                            <p>
                                                            <table style="width:2200px !important;" id="teprogram4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                                                                <thead>
                                                                    <tr>
                                                                      <th>Penanda</th>
                                                                        <th>Usulan</th>
                                                                        <th>Nama Skenario</th>
                                                                        <th>Prioritas</th>
                                                                        <th>Biaya Thn 1</th>
                                                                        <th>Biaya Thn 2</th>
                                                                        <th>Biaya Thn 3</th>
                                                                        <th>Biaya Thn 4</th>
                                                                        <th>Biaya Thn 5</th>
                                                                        <th>Biaya Thn 6</th>
                                                                        <th>Panjang Thn 1</th>
                                                                        <th>Panjang Thn 2</th>
                                                                        <th>Panjang Thn 3</th>
                                                                        <th>Panjang Thn 4</th>
                                                                        <th>Panjang Thn 5</th>
                                                                        <th>Panjang Thn 6</th>
                                                                        <th>Benefit</th>
                                                                        <th>ID Skenario</th>
                                                                        <th>GID</th>
                                                                         <th>Penanda</th>
                                                                  <th>Remark</th>
                                                                       <th>Panjang</th>
                                                                       <th>Bol</th>
                                                                       <th>Code</th>
                                                                       <th>SID</th>
                                                                       <th>No Provinsi</th>
                                                                       <th>Data Dasar</th>
                                                                       <th>Tahun Dasar</th>
                                                                       <th>Inflasi</th>
                                                                       <th>Status</th>
                                                                       <th>Cakupan User</th>
                                                                    </tr>
                                                                </thead>

                                                            </table>
                                                            </p>
                                                        </div>
                                                        <div class="tab-pane" id="btabswo-static2-wrenstra">
                                                            <h4 class="font-w300 push-15">RENSTRA</h4>
                                                            <p>
                                                            <table class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer" style="width: 100%;" id="trenstra4">
                                                                <thead>
                                                                    <tr>
                                                                      <th>Penanda</th>
                                                                    <th>ID Renstra</th>
                                                                     <th>ID Target</th>
                                                                    <th>Target</th>
                                                                    <th>Nilai</th>
                                                                    <th>Satuan</th>
                                                                    <th>Tahun</th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                            </p>
                                                        </div>
                                                        <div class="tab-pane" id="btabswo-static2-wusulandpr">
                                                            <h4 class="font-w300 push-15">USULAN DPR</h4>
                                                            <p>
                                                            <table style="width: 100%;" id="tusulandpr4"  class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                                                                <thead>
                                                                    <tr>
                                                                      <th class="hidden">ID Usulan</th>
                                                                      <th>Penanda</th>
                                                                      <th style="width:500px !important;">Uraian</th>
                                                                      <th>Sumber</th>
                                                                      <th>Tanggal</th>
                                                                      <th>Pengusul</th>
                                                                      <th>Provinsi</th>
                                                                      <th>RKAKL Vol.</th>
                                                                      <th>RKAKL Biaya</th>
                                                                      <th>S.Kewenangan</th>
                                                                      <th>Evaluasi</th>
                                                                      <th>FS</th>
                                                                      <th>DED</th>
                                                                      <th>Lahan</th>
                                                                      <th>Dok. Ling</th>
                                                                      <th>Keterangan</th>
                                                                      <th>Usulan Volume</th>
                                                                      <th>Usulan Biaya</th>
                                                                       <th>Penanda</th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                            </p>
                                                        </div>

                                                        <div class="tab-pane" id="btabswo-static2-wusulanpemda">
                                                            <h4 class="font-w300 push-15">USULAN PEMDA</h4>
                                                            <p>
                                                                <table  id="tusulanpemda4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                                                                  <thead>
                                                                    <tr>
                                                                      <th class="hidden">ID Usulan</th>
                                                                      <th>Penanda</th>
                                                                      <th style="width:500px !important;">Uraian</th>
                                                                      <th>Sumber</th>
                                                                      <th>Tanggal</th>
                                                                      <th>Pengusul</th>
                                                                      <th>Provinsi</th>
                                                                      <th>RKAKL Vol.</th>
                                                                      <th>RKAKL Biaya</th>
                                                                      <th>S.Kewenangan</th>
                                                                      <th>Evaluasi</th>
                                                                      <th>FS</th>
                                                                      <th>DED</th>
                                                                      <th>Lahan</th>
                                                                      <th>Dok. Ling</th>
                                                                      <th>Keterangan</th>
                                                                      <th>Usulan Volume</th>
                                                                      <th>Usulan Biaya</th>
                                                                       <th>Penanda</th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>-->

                    <div class="col-md-12" style="background:#a7a7a7;">
                        <div class="container" style="background:#fff;">
                            <div class="row">
                                <div class="col-md-12"><h2 class="content-heading border-bottom mb-4 pb-2">Form Paket</h2><hr></div>
                                <input type="hidden" id="modeform">
                                <div class="col-md-12" id="boxpagu">
                                    <div class="col-md-2" id="kosong"></div>
                                    <div class="col-md-4 tab-pagu-edit">
                                        <h5>Usulan</h5>
                                        <table id="boxs"  class="table table-bordered">
                                            <tr>
                                                <td>Propinsi Fisik</td>
                                                <td><div id='wupf'></div><div style="display:none;" id='wupf1'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Propinsi Non Fisik</td>
                                                <td><div id='wupnf'></div><div style="display:none;" id='wupnf1'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Preservasi Jalan</td>
                                                <td><div id='wuppj'></div><div style="display:none;" id='wuppj1'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Pembangunan Jalan</td>
                                                <td><div id='wuppem'></div><div style="display:none;" id='wuppem1'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Jembatan</td>
                                                <td><div id='wupj'></div><div style="display:none;" id='wupj1'></div></td>
                                            </tr>
                                            <tr>
                                                <td>JBH</td>
                                                <td><div id='wupjbh'></div><div style="display:none;" id='wupjbh1'></div></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-4 tab-pagu-edit">
                                        <h5>Pagu</h5>
                                        <table  id="boxs"  class="table table-bordered">
                                            <tr>
                                                <td>Propinsi Fisik</td>
                                                <td><div id='wppf'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Propinsi Non Fisik</td>
                                                <td><div id='wppnf'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Preservasi Jalan</td>
                                                <td><div id='wpppj'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Pembangunan Jalan</td>
                                                <td><div id='wpppem'></div></td>
                                            </tr>
                                            <tr>
                                                <td>Jembatan</td>
                                                <td><div id='wppj'></div></td>
                                            </tr>
                                            <tr>
                                                <td>JBH</td>
                                                <td><div id='wppjbh'></div></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <form role="form" method="POST" id="frm-edit-detail">
                                    <input type="hidden" id="wsource_rujukan">
<!--                                    <input type="hidden" id="id_paket" name="id_paket">
                                    <input type="hidden" id="id_usulan" name="id_usulan">-->
                                    <input type="hidden" id="wflow1" >
                                    <input type="hidden" id="wflow2" >
                                    <input type="hidden" id="wflow3" >
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label">Tahun Anggaran</label>
                                            <select id="wthang" class="form-control" style="background-color:#dadada;">
                                                <option value="">--Pilih--</option>
                                            </select>
                                            <input type="hidden" name="thang" />
                                        </div>
                                    </div>

                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label class="control-label">Program</label>
                                            <select style="background-color:#dadada;" id="wkd_program" class="form-control" onchange="handleProgram(this)">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">Kegiatan</label>
                                            <select style="background-color:#dadada;" id="wkd_kegiatan" class="form-control" onchange="handleKegiatan(this)">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <?php
                                            if ($this->session->konfig_tahun_ang > 2020) {
                                                echo '<label class="control-label">KRO </label>';
                                            } else {
                                                echo '<label class="control-label">Output </label>';
                                            }
                                            ?>
                                            <select style="background-color:#dadada;" id="wkd_output" name="wkd_output" class="form-control" onchange="javascript:handleOutput(this);">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <?php
                                            if ($this->session->konfig_tahun_ang > 2020) {
                                                echo '<label class="control-label">RO </label>';
                                            } else {
                                                echo '<label class="control-label">Sub Output </label>';
                                            }
                                            ?>
                                            <select style="background-color:#dadada;" id="wkd_sub_output" name="wkd_sub_output" class="form-control" onchange="javascript:handleSoutput(this);">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">Komponen</label>
                                            <select style="background-color:#dadada;" id="wkd_komponen" name="wkd_komponen" class="form-control">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="control-label">Kode Paket</label>
                                            <input style="background-color:#dadada;" id="wkd_sub_komponen" type="text" required="required" class="form-control" />
                                            <input type="hidden" name="kd_sub_komponen" />
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label class="control-label">Nama Paket</label>
                                            <input style="background-color:#dadada;" id="wnama_sub_komponen" name="wnama_sub_komponen" type="text" required="required" class="form-control" />
                                           <!-- <input type="hidden" name="nama_sub_komponen" />-->
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Jenis Kontrak</label>
                                            <select style="background-color:#dadada;" id="wjnskontrak" name="wjnskontrak" class="form-control">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!--dari pagu indikatif Awal-->
                                    <div id='wfsk' style="display:none;">
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC DED Status</label>
                                                <select style="background-color:#dadada;" id="wrc_ded_status" name="wrc_ded_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                    <!--                                                <option value="siap">Siap</option>
                                                                                                    <option value="tidak_siap">Tidak Siap</option>-->
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC FS Status </label>
                                                <select style="background-color:#dadada;" id="wrc_fs_status" name="wrc_fs_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                    <!--                                                <option value="siap">Siap</option>
                                                                                                    <option value="tidak_siap">Tidak Siap</option>-->
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC Lahan Status</label>
                                                <select style="background-color:#dadada;" id="wrc_lahan_status" name="wrc_lahan_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                    <!--                                                <option value="siap">Siap</option>
                                                                                                    <option value="tidak_siap">Tidak Siap</option>-->
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC Dockling Status</label>
                                                <select style="background-color:#dadada;" id="wrc_doklin_status" name="wrc_doklin_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                    <!--                                                <option value="siap">Siap</option>
                                                                                                    <option value="tidak_siap">Tidak Siap</option>-->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">KAK Status</label>
                                            <select style="background-color:#dadada;" id="wkak" name="wkak" class="form-control"2>
                                                <option value="">--Pilih--</option>
                                                <option value="SIAP">SIAP</option>
                                                <option value="TIDAK_SIAP">TIDAK SIAP</option>
                                                <option value="TDK_PERLU">TIDAK PERLU</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">RAB Status</label>
                                            <select style="background-color:#dadada;" id="wrab" name="wrab" class="form-control">
                                                <option value="">--Pilih--</option>
                                                <option value="SIAP">SIAP</option>
                                                <option value="TIDAK_SIAP">TIDAK SIAP</option>
                                                <option value="TDK_PERLU">TIDAK PERLU</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Provinsi</label>
                                            <select style="background-color:#dadada;" id="wprov" name="wprov" class="form-control">

                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-12"><h2 class="content-heading border-bottom mb-4 pb-2">Form Detail</h2><hr></div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Kab/Kota</label>
                                            <select id="wkabkot" name="wkabkot" class="form-control" required="required" onchange="javascript:handleRuaskabkot(this, 'w');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Jenis Belanja</label>
                                            <select style="" id="wkdgbkpk" name="wkdgbkpk" class="form-control" required="required" onchange="javascript:handleJnsBelanja(this, 'w');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Jenis Akun</label>
                                            <select id="wkdakun" name="wkdakun" class="form-control" onchange="javascript:handleAkun(this, 'w');" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!--new code-->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Beban/Jns Bantuan/Cara Penarikan </label>
                                            <select id="wsumber" name="wsumber" required="required"  class="form-control" onchange="javascript:handleSumber(this, 'w');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">KPPN</label>
<!--                                            <select style="background-color:#dadada;" id="wkdkppn" name="wkdkppn" class="form-control" onchange="javascript:ChangeSumberList(this, 'w');">-->
                                            <select id="wkdkppn" name="wkdkppn" class="form-control" onchange="javascript:ChangeSumberList(this, 'w');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Register </label>
                                            <select id="wregister" name="wregister" class="form-control wregister selectpicker with-ajax" data-live-search="true" disabled>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-6">
                                                <div class="panel panel-default" id="wpanelHitung" disabled>
                                                    <div class="panel-heading">
                                                        <table>
                                                            <tr>
                                                                <td><label>Cara Hitung</label></td>
                                                                <td>
                                                                    <div class="form-group" id="wradioHitung">
                                                                        <label class="radio-inline" style="display:none;"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="0"/><span style="padding-left:50px; display:none"/></label>
                                                                        <label class="radio-inline"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="1"/><span style="padding-left:50px"/>Non PPN</label>
                                                                        <label class="radio-inline"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="2"/><span style="padding-left:30px"/>Netto</label>
            <!--                                                            <label class="radio-inline"><input name="caraHitung" type="radio" disabled onchange="javascript:handleRadioHitung(this);"/ value="3">Bruto</label>-->
                                                                        <label class="radio-inline"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="4"/><span style="padding-left:70px"/>Non Sharing</label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="8"><p id="wtext1" style="color:red;"/></td></tr>
                                                            <tr>
                                                                <td style="text-align:right">PHLN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="winputPHLN" name="winputPHLN" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('w');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
<!--                                                                <td><input type="text" id="xinputPHLN2" name="xinputPHLN2" style="width:155px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('x');"></td>-->
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="winputKPPNph" name="winputKPPNph"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="winputRMPdp" name="winputRMPdp" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('w');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
<!--                                                                <td><input type="text" id="xinputRMPdp2" name="xinputRMPdp2" style="width:155px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('x');"></td>-->
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="winputKPPNrm" name="winputKPPNrm"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RPLN Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="winputRPLNPdp" name="winputRPLNPdp" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('w');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
<!--                                                                <td><input type="text" id="xinputRPLNPdp2" name="xinputRPLNPdp2" style="width:155px; display:none;" class="form-control number" placeholder="Rp"></td>-->
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="winputKPPNrp" name="winputKPPNrp"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right; width:85px">Register Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="6">
                                                                    <select id="wregisterpdp" name="wregisterpdp" class="form-control wregisterpdp selectpicker with-ajax" data-live-search="true" disabled>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>Catatan Akun (Optional)</label>
                                                    </div>
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="3">&nbsp</td></tr>
                                                            <tr>
                                                                <td style="text-align:right">Halaman 4</td>
                                                                <td style="width:20px">&nbsp;</td>
                                                                <td>
                                                                    <textarea id="winputHal4" name="winputHal4" style="width:400px;" class="form-control"></textarea>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">Dipa</td>
                                                                <td style="width:20px">&nbsp;</td>
                                                                <td>
                                                                    <textarea id="winputDipa" name="winputDipa" style="width:400px;" class="form-control"></textarea>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">PPK (Header 1)</label>
<!--                                        <select style="background-color:#dadada;" id="wid_ppk" name="wid_ppk" class="form-control">-->
<!--                                            <select id="wid_ppk" name="wid_ppk" class="form-control" onchange="javascript:handleCekPPK(this, 'w');">-->
                                            <select id="wid_ppk" name="wid_ppk" class="form-control">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="" onchnge="">Header 2 (Optional)</label>
                                            <input id="wheader2" name="wheader2" type="text" class="form-control" maxlength="87" disabled/>
                                        </div>
                                    </div>

                                    <!--new code-->
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <table>
                                                        <tr>
                                                            <td><label></label></td>
                                                            <td>
                                                                <div id="wradioVolumeKegiatan">
                                                                    <label class="radio-inline id_radioRuas"><input name="wradioVolume" type="radio" onchange="javascript:handleRadioVolume('w');" value="1"/><span style="padding-left:30px"/>Ruas</label>
                                                                    <label class="radio-inline id_radioJembatan"><input name="wradioVolume" type="radio" onchange="javascript:handleRadioVolume('w');" value="2"/><span style="padding-left:50px"/>Jembatan</label>
                                                                    <label class="radio-inline id_radioHonor"><input name="wradioVolume" type="radio" onchange="javascript:handleRadioVolume('w');" value="3"/><span style="padding-left:33px"/>Uraian</label>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="panel-body">
                                                    <div id="wradioHonor" style="display:none">
                                                        <div class="row">
                                                            <div class="col-md-8">
                                                                <table>
                                                                    <tr>
                                                                        <td><input type="number" id="winputVol1" name="winputVol1" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="winputSat1" name="winputSat1" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="winputVol2" name="winputVol2" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="winputSat2" name="winputSat2" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="winputVol3" name="winputVol3" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="winputSat3" name="winputSat3" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="winputVol4" name="winputVol4" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="winputSat4" name="winputSat4" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('w');"></td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <button class="btn btn-sm btn-success" type="button" onclick="javascript:modalSBM('w');">SBM</button>
                                                            </div>
                                                        </div>
                                                        <div class="row" style="display: none;">
                                                            <div class="col-md-12">
                                                                <input type="text" id="wkdsbu" name="wkdsbu" class="form-control">
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-12">
                                                                Uraian
                                                                <input type="text" id="wdetail" name="wdetail" style="width:100%; display:block;" class="form-control">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="wradioRuas" style="display:none">
                                                        <div class="row">
                                                            <div class="col-md-6 ruasjalan">
                                                                <div class="form-group">
                                                                    <label class="col-md-6">Ruas Jalan</label><div class="col-md-6" style="text-align:right;padding:0px;"><label id="wmaxRuas" style="color:blue;"/></div>
                                                                    <select id="wid_ruas" name="wid_ruas" class="form-control" onchange="javascript:handleRuas(this, 'w');" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
<!--                                                                    <input type="hidden" name="nm_ruas" id="nm_ruas">-->
                                                                </div>
                                                            </div>
                                                            <!--                                                            <div class="col-md-3 staw">
                                                                                                                            <div class="form-group">
                                                                                                                                <label for="exampleInput2" class="col-md-8">STA Awal</label><div   id="loadingsss" class="col-md-4"  style="display:none;"><i class="fa fa-refresh fa-spin"></i></div>
                                                                                                                                <select id="wsta_awal" name="wsta_awal" required="required" class="form-control" onchange="javascript:handleSTAEditdetail('awal', this);" required="required">
                                                                                                                                    <option value="0">--Pilih--</option>
                                                                                                                                </select>
                                                                                                                                <input type="hidden" name="sta_awal">
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                        <div class="col-md-3 staw">
                                                                                                                            <div class="form-group">
                                                                                                                                <label for="exampleInput2" class="col-md-8">STA Akhir</label><div   id="loadingssss" class="col-md-4"  style="display:none;"><i class="fa fa-refresh fa-spin"></i></div>
                                                                                                                                <select id="wsta_akhir" name="wsta_akhir" required="required" class="form-control" onchange="javascript:handleSTAEditdetail('akhir', this);" required="required">
                                                                                                                                    <option value="">--Pilih--</option>
                                                                                                                                </select>
                                                                                                                                <input type="hidden" name="sta_akhir">
                                                                                                                            </div>
                                                                                                                        </div>-->
                                                            <div class="col-md-3 staw" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput2" onchnge="">STA Awal (Meter)</label>
                                                                    <input type="number" id="wsta_awal" name="wsta_awal" class="form-control"  onkeyup="javascript:handleSTAEditdetail('awal', this);" required="required" placeholder="e.g. 100"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 staw" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput3" onchnge="">STA Akhir (Meter)</label>
                                                                    <input type="number" id="wsta_akhir" name="wsta_akhir" required="required" class="form-control" onkeyup="javascript:handleSTAEditdetail('akhir', this);" placeholder="e.g. 2100"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 jembatans">
                                                                <div class="form-group">
                                                                    <label for="exampleInput4" class="col-md-6">Jembatan</label><div class="col-md-6" style="text-align:right;padding:0px;"></div>
                                                                    <select id="wid_jembatan" name="wid_jembatan"  class="form-control" onchange="javascript:handleJembatan(this, 'w');" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
<!--                                                                    <input type="hidden" name="nm_jembatan" id="nm_jembatan">-->
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longs">
                                                                <div class="form-group">
                                                                    <label for="exampleInput5" onchnge="" id="19"></label>
                                                                    <input id="wlongitude" name="wlongitude" type="text" required="required" class="form-control" onkeyup="staAwal('w');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longs">
                                                                <div class="form-group">
                                                                    <label for="exampleInput6" onchnge="" id="20"></label>
                                                                    <input id="wlatitude" name="wlatitude" type="text" required="required" class="form-control" onkeyup="staAwal('w');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longsa" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput5" onchnge="" id="21"> </label>
                                                                    <input id="wlongitude2" name="wlongitude2" type="text" required="required" class="form-control" onkeyup="staAkhir('w');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longsa" style="display: none;" >
                                                                <div class="form-group">
                                                                    <label for="exampleInput6" id="22"> </label>
                                                                    <input id="wlatitude2" name="wlatitude2" type="text" required="required" class="form-control" onkeyup="staAkhir('w');"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longss" style="display: none;">
                                                            <div class="form-group">
                                                                <label>Longitude(X1)</label>
                                                                <input id="wlongitude3" name="wlongitude3" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longss" style="display: none;">
                                                            <div class="form-group">
                                                                <label>Latitude(Y1)</label>
                                                                <input id="wlatitude3" name="wlatitude3" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longsaa" style="display: none;">
                                                            <div class="form-group">
                                                                <label>Longitude(X2)</label>
                                                                <input id="wlongitude4" name="wlongitude4" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longsaa" style="display: none;" >
                                                            <div class="form-group">
                                                                <label>Latitude(Y2)</label>
                                                                <input id="wlatitude4" name="wlatitude4" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                        <!--                                                        <div class="col-md-12">
                                                                                                                    <p id="wtextMaxx" style="color:red;"/>
                                                                                                                </div>-->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-12" style="display:none;">
                                        <textarea id="wgeom" name="wgeom" class="form-control"></textarea>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Volume</label>
                                            <input id="wvolume" name="wvolume" type="text" required="required" class="form-control vol" />
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Satuan</label>
<!--                                            <input id="wsatuan" name="wsatuan" type="text" required="required" class="form-control" />-->
                                            <select id="wsatuan" name="wsatuan" class="form-control" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="form-inline"style="display:inline-block; width:50px;">
                                            <br>
                                            <label class="form-control" style="border:none">X</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="exampleInput8">Harga Satuan (Rupiah)</label>
<!--                                            <input onchange="count_jumlah(this)" type="text" id="whargasat" name="whargasat" required="required" class="form-control number" />-->
                                            <input type="text" id="whargasat" name="whargasat" required="required" class="form-control number" />
                                            <input type="hidden" id="wxhargasat" name="wxhargasat"/>
                                        </div>
                                    </div>
                                    <div class="col-md-1" style="display:inline-block; width:60px;">
                                        <div class="form-inline">
                                            <br>
                                            <label class="form-control" style="border:none;">=</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="exampleInput8" >Jumlah (Rupiah)</label>
                                            <input id="wjumlah" name="wjumlah" type="text" class="form-control number" required="required"/>
                                            <input id="wtotalpagu" name="wtotalpagu" type="hidden" />
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-6" id="wpanelNonSharing" style="display:none;">
                                                <div class="panel panel-default">
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="8"><p id="wtext2" style="color:red;"/></td></tr>
                                                            <tr>
                                                                <td style="text-align:right">PHLN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="winputNSPHLN" name="winputNSPHLN" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="winputNSPHLN2" name="winputNSPHLN2" style="width:155px;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('w');"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="winputNSRMPdp" name="winputNSRMPdp" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="winputNSRMPdp2" name="winputNSRMPdp2" style="width:155px;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('w');"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RPLN Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="winputNSRPLNPdp" name="winputNSRPLNPdp" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="winputNSRPLNPdp2" name="winputNSRPLNPdp2" style="width:155px;" class="form-control number" placeholder="Rp" disabled></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">Total </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="3"><input type="text" id="wTotal" name="wTotal" style="width:200px" class="form-control number" placeholder="Rp" disabled></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6" id="wpanelPaguBlokir" style="display:none;">
                                                <div class="panel panel-default">
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="5"><p id="wtextBlokir" style="color:red;"/></td></tr>
                                                            <tr id="wblokirs1" style="display:none;">
                                                                <td style="text-align:right;">Blokir PHLN </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="winputBlokirPHLN" name="winputBlokirPHLN" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('w');" disabled></td>
                                                            </tr>
                                                            <tr id="wblokirs2" style="display:none;">
                                                                <td style="text-align:right;">Blokir RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="text" id="winputBlokirRMPdp" name="winputBlokirRMPdp" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('w');" disabled></td>
                                                            </tr>
                                                            <tr id="wblokirs3" style="display:none;">
                                                                <td style="text-align:right;">Blokir RPLN Pdp </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="winputBlokirRPLNPdp" name="winputBlokirRPLNPdp" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('w');" disabled></td>
                                                            </tr>
                                                            <tr id="wrphblok" style="display:none;">
                                                                <td style="text-align:right;">Blokir </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="winputRphBlokir" name="winputRphBlokir" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('w');" disabled></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="panel panel-default">
                                            <div class="panel-heading">
                                                <label>Blokir</label>
                                            </div>
                                            <div class="panel-body" style="padding-bottom:0px;">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td style="text-align:right; width:110px; text-align:center; vertical-align:middle;">Kode Blokir</td>
                                                        <td style="text-align:right; width:auto;">
                                                            <select id="wkd_blokir" name="wkd_blokir" class="form-control" onchange="handleBlok(this, 'w');">
                                                                <option value="">--Pilih--</option>
                                                            </select>
                                                        </td>
                                                        <td style="text-align:right; text-align:center; vertical-align:middle; width:110px;">Blokir Detail<span style="padding-left:10px;"/></td>
                                                        <td style="text-align:left; text-align:center; vertical-align:middle; width:10px;"><label class="radio"><input name="witemblokir" type="radio" value="1" disabled onchange="handleBlokir(this, 'w');"/>Ya</label></td>
                                                        <td style="text-align:left; text-align:center; vertical-align:middle; width:30px;"><label class="radio"><input name="witemblokir" type="radio" value="0" disabled onchange="handleBlokir(this, 'w');"/><span style="padding-left:10px;"/>Tidak</label></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="text-align:right; text-align:center; vertical-align:middle;">Uraian Blokir</td>
                                                        <td colspan="4">
                                                            <input type="text" id="wurblokir" name="wurblokir" class="form-control"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <!--                                    <div class="col-md-6">
                                                                            <div class="form-group">
                                                                                <label>Beban/Jns Bantuan/Cara Penarikan </label>
                                                                                <select id="wsumber" name="wsumber" required="required"  class="form-control" onchange="javascript:handleSumber(this, 'w');">
                                                                                    <option value="">--Pilih--</option>
                                                                                </select>
                                                                            </div>
                                                                        </div>

                                                                        <div class="col-md-6">
                                                                            <div class="form-group">
                                                                                <label>Register </label>
                                                                                <select id="wregister" name="wregister" class="form-control">
                                                                                    <option value="">--Pilih--</option>
                                                                                </select>
                                                                            </div>
                                                                        </div>

                                                                        <div class="row">
                                                                            <div class="col-md-12">
                                                                                <div class="col-md-6">
                                                                                    <div class="panel panel-default" id="wpanelHitung">
                                                                                        <div class="panel-heading">
                                                                                            <table>
                                                                                                <tr>
                                                                                                    <td><label>Cara Hitung</label></td>
                                                                                                    <td>
                                                                                                        <div class="form-group" id="wradioHitung">
                                                                                                            <label class="radio-inline" style="display:none;"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="0"/><span style="padding-left:50px; display:none"/></label>
                                                                                                            <label class="radio-inline"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="1"/><span style="padding-left:50px"/>Non PPN</label>
                                                                                                            <label class="radio-inline"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="2"/><span style="padding-left:30px"/>Netto</label>
                                                                                                            <label class="radio-inline"><input name="wcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('w');" value="4"/><span style="padding-left:70px"/>Non Sharing</label>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </div>
                                                                                        <div class="panel-body">
                                                                                            <table>
                                                                                                <tr><td colspan="8"><p id="wtext1" style="color:red;"/></td></tr>
                                                                                                <tr>
                                                                                                    <td style="text-align:right">PHLN </td>
                                                                                                    <td style="width:10px">&nbsp;</td>
                                                                                                    <td><input type="number" id="winputPHLN" name="winputPHLN" style="width:70px; display:block;" class="form-control" onchange="hitung('w');" disabled></td>
                                                                                                    <td name="percent" style="padding-left:5px">%</td>
                                                                                                    <td><input type="text" id="winputPHLN2" name="winputPHLN2" style="width:200px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('w');"></td>
                                                                                                    <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                                                    <td style="width:5px">&nbsp;</td>
                                                                                                    <td>
                                                                                                        <select id="winputKPPNph" name="winputKPPNph"  class="form-control" disabled>
                                                                                                            <option value="">--Pilih--</option>
                                                                                                        </select>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="text-align:right">RM Pdp </td>
                                                                                                    <td style="width:10px">&nbsp;</td>
                                                                                                    <td><input type="number" id="winputRMPdp" name="winputRMPdp" style="width:70px; display:block;" class="form-control" onchange="hitung('w');" disabled></td>
                                                                                                    <td name="percent" style="padding-left:5px">%</td>
                                                                                                    <td><input type="text" id="winputRMPdp2" name="winputRMPdp2" style="width:200px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('w');"></td>
                                                                                                    <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                                                    <td style="width:10px">&nbsp;</td>
                                                                                                    <td>
                                                                                                        <select id="winputKPPNrm" name="winputKPPNrm"  class="form-control" disabled>
                                                                                                            <option value="">--Pilih--</option>
                                                                                                        </select>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="text-align:right">RPLN Pdp </td>
                                                                                                    <td style="width:10px">&nbsp;</td>
                                                                                                    <td><input type="number" id="winputRPLNPdp" name="winputRPLNPdp" style="width:70px; display:block;" class="form-control" onchange="hitung('w');" disabled></td>
                                                                                                    <td name="percent" style="padding-left:5px">%</td>
                                                                                                    <td><input type="text" id="winputRPLNPdp2" name="winputRPLNPdp2" style="width:200px; display:none;" class="form-control number" placeholder="Rp"></td>
                                                                                                    <td name="kppn" style="padding-left:10px">KPPN</td>
                                                                                                    <td style="width:10px">&nbsp;</td>
                                                                                                    <td>
                                                                                                        <select id="winputKPPNrp" name="winputKPPNrp"  class="form-control" disabled>
                                                                                                            <option value="">--Pilih--</option>
                                                                                                        </select>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr id="wtotalkppn" style="display:none">
                                                                                                    <td style="text-align:right">Total </td>
                                                                                                    <td style="width:10px">&nbsp;</td>
                                                                                                    <td colspan="5"><input type="text" id="wTotal" name="wTotal" style="width:200px" class="form-control number" placeholder="Rp"></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="text-align:right; width:85px">Register Pdp </td>
                                                                                                    <td style="width:10px">&nbsp;</td>
                                                                                                    <td colspan="6">
                                                                                                        <select id="wregisterpdp" name="wregisterpdp" class="form-control" disabled>
                                                                                                            <option value="">--Pilih--</option>
                                                                                                        </select>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="col-md-6">
                                                                                    <div class="panel panel-default">
                                                                                        <div class="panel-heading" style="height:43px">
                                                                                            <label>Catatan Akun (Optional)</label>
                                                                                        </div>
                                                                                        <div class="panel-body" style="height:170px">
                                                                                            <table>
                                                                                                <tr>
                                                                                                    <td style="text-align:right">Halaman 4</td>
                                                                                                    <td style="width:20px">&nbsp;</td>
                                                                                                    <td>
                                                                                                        <textarea id="winputHal4" name="winputHal4" style="width:400px;" class="form-control"></textarea>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="text-align:right">Dipa</td>
                                                                                                    <td style="width:20px">&nbsp;</td>
                                                                                                    <td>
                                                                                                        <textarea id="winputDipa" name="winputDipa" style="width:400px;" class="form-control"></textarea>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>-->



                                    <!--                                                                        <div class="col-md-6 ruasjalan">
                                                                                                                <div class="form-group">
                                                                                                                    <label for="exampleInput1" onchnge="" class="col-md-6">Ruas Jalan</label><div class="col-md-6" style="text-align:right;padding:0px;"></div>
                                                                                                                    <select id="wid_ruas" name="wid_ruas" class="form-control" onchange="javascript:handleRuas(this);" required="required">
                                                                                                                        <option value="">--Pilih--</option>
                                                                                                                    </select>
                                                                                                                    <input type="hidden" name="nm_ruas" id="nm_ruas">
                                                                                                                </div>
                                                                                                            </div>

                                                                                                            <div class="col-md-3 staw">
                                                                                                                <div class="form-group">
                                                                                                                    <label for="exampleInput2" class="col-md-8">STA Awal</label><div   id="loadingsss" class="col-md-4"  style="display:none;"><i class="fa fa-refresh fa-spin"></i></div>
                                                                                                                    <select id="wsta_awal" name="wsta_awal" required="required" class="form-control" >
                                                                                                                        <option value="0">--Pilih--</option>
                                                                                                                    </select>
                                                                                                                    <input type="hidden" name="sta_awal">
                                                                                                                </div>
                                                                                                            </div>
                                                                                                            <div class="col-md-3 staw">
                                                                                                                <div class="form-group">
                                                                                                                    <label for="exampleInput2" class="col-md-8">STA Akhir</label><div   id="loadingssss" class="col-md-4"  style="display:none;"><i class="fa fa-refresh fa-spin"></i></div>
                                                                                                                    <select id="wsta_akhir" name="wsta_akhir" required="required" class="form-control">
                                                                                                                        <option value="">--Pilih--</option>
                                                                                                                    </select>
                                                                                                                    <input type="hidden" name="sta_akhir">
                                                                                                                </div>
                                                                                                            </div>

                                                                                                            <div class="col-md-12" style="padding:0px;">
                                                                                                                <div class="col-md-6 jembatans">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput4" class="col-md-6">Jembatan</label><div class="col-md-6" style="text-align:right;padding:0px;"></div>
                                                                                                                        <select id="wid_jembatan" name="wid_jembatan"  class="form-control" onchange="javascript:handleJembatan(this);" required="required">
                                                                                                                            <option value="">--Pilih--</option>
                                                                                                                        </select>
                                                                                                                        <input type="hidden" name="nm_jembatan" id="nm_jembatan">
                                                                                                                    </div>
                                                                                                                </div>

                                                                                                                <div class="col-md-3 longs">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput5" onchnge="" id="19"></label>
                                                                                                                        <input id="wlongitude" name="wlongitude" type="text" required="required" class="form-control" />
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                                <div class="col-md-3 longs">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput6" onchnge="" id="20"></label>
                                                                                                                        <input id="wlatitude" name="wlatitude" type="text" required="required" class="form-control" />
                                                                                                                    </div>
                                                                                                                </div>

                                                                                                                <div class="col-md-3 longsa" style="display: none;">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput5" onchnge="" id="21"> </label>
                                                                                                                        <input id="wlongitude2" name="wlongitude2" type="text" required="required" class="form-control" />
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                                <div class="col-md-3 longsa" style="display: none;" >
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput6" id="22"> </label>
                                                                                                                        <input id="wlatitude2" name="wlatitude2" type="text" required="required" class="form-control" />
                                                                                                                    </div>
                                                                                                                </div>

                                                                                                                <div class="col-md-12 uraians" style="display:none;">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput1" onchnge="">Uraian</label>
                                                                                                                        <input id="wdetail" name="wdetail" type="text" required="required" class="form-control" />
                                                                                                                        input id="id_usulan" name="id_usulan" type="text" required="required" class="form-control" /
                                                                                                                    </div>
                                                                                                                </div>


                                                                                                                <div class="col-md-12">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput7" onchnge="">Volume</label>
                                                                                                                        <input id="wvolume" name="wvolume" type="text" required="required" class="form-control vol" />
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                                <div class="col-md-12">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput8" onchnge="">Satuan</label>
                                                                                                                        <input id="wsatuan" name="wsatuan" type="text" required="required" class="form-control" />
                                                                                                                    </div>
                                                                                                                </div>

                                                                                                                <div class="col-md-12">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput8">Harga Satuan (Rupiah)</label>
                                                                                                                        <input onchange="count_jumlah(this)" type="text" id="whargasat" name="whargasat" required="required" class="form-control number" />
                                                                                                                        <input type="hidden" id="wxhargasat" name="wxhargasat"/>
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                                <div class="col-md-12">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput8" >Jumlah (Rupiah)</label>
                                                                                                                        <input id="wjumlah" name="wjumlah" type="text" class="form-control number" required="required"/>
                                                                                                                        <input id="wtotalpagu" name="wtotalpagu" type="hidden" />
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                                <div class="col-md-6">
                                                                                                                    <div class="form-group">
                                                                                                                        <label for="exampleInput4" class="col-md-6" required="required">Sumber Dana </label>
                                                                                                                        <select id="wsumber" name="wsumber"  class="form-control">
                                                                                                                            <option value="">--Pilih--</option>
                                                                                                                        </select>

                                                                                                                    </div>
                                                                                                                </div>-->



                                    <div class="col-md-12" style="display:none;">
                                        <div class="form-group">
                                            <label for="exampleInput7" >ID Paket</label>
                                            <input type="text" id="wid_paket"/>
                                        </div>
                                    </div>

                                    <div class="col-md-12" style="display:none;">
                                        <div class="form-group">
                                            <label for="exampleInput7" >ID Usulan</label>
                                            <input type="text" id="wid_usulan"/>
                                        </div>
                                    </div>

                                    <div class="col-md-12 tags-edit-detail" style="display:'';">
                                        <div class="form-group" id="md-edit-penandaan">
                                            <label for="exampleInput7" onchnge="">Rujukan</label>
                                            <div style="float:right;">
                                                <button style='background:#5ab4ac' class="btn btn-sm" type="button" onclick="javascript:$('#modalSipro4').modal('show');tabsipro('4');">Sipro</button>
                                                <button style='background:#EDBB99' class="btn btn-sm btnDprd" type="button" onclick="javascript:$('#modalDprd4').modal('show');tabdprd('4');">DPRD</button>
                                                <button style='background:#ffeda0' class="btn btn-sm btnDiskresi" type="button" onclick="javascript:$('#modalDiskresi4').modal('show');tabdiskresi('4');">Diskresi</button>
                                                <button style='background:#FFA07A' class="btn btn-sm btnKL" type="button" onclick="javascript:$('#modalKL4').modal('show');tabkl('4');">K/L</button>
                                                <button style='background:#52BE80' class="btn btn-sm btnAkademisi" type="button" onclick="javascript:$('#modalAkademisi4').modal('show');tabakademisi('4');">Akademisi</button>
                                                <button style='background:#a6cee3' class="btn btn-sm btnPemda" type="button" onclick="javascript:$('#modalPemda4').modal('show');tabpemda('4');">Pemda</button>
                                                <button style='background:#b2df8a' class="btn btn-sm btnDpr" type="button" onclick="javascript:$('#modalDpr4').modal('show');tabdpr('4');">DPR</button>
                                                <button style='background:#cab2d6' class="btn btn-sm btnRenstra" type="button" onclick="javascript:$('#modalRenstra4').modal('show');tabrenstra('4');">Renstra</button>
                                                <button style='background:#fb9a99' class="btn btn-sm btnEprog" type="button" onclick="javascript:$('#modalEprogram4').modal('show');tabprogram('4');">e-PROGRAM</button>
                                                <!--                                                <button style='background:#fdbf6f' class="btn btn-sm btnIrms" type="button" onclick="javascript:$('#modalIrms4').modal('show');tabirms('4');">IRMSv3</button>-->
                                                <button style='background:#fdbf6f' class="btn btn-sm btnIrms" type="button" onclick="javascript:$('#modalIrms4').modal('show');tabirms('4');">IRMSv3 (jalan)</button>
                                                <button style='background:#fcc956' class="btn btn-sm btnIrmsJembatan" type="button" onclick="javascript:$('#modalIrmsJembatan4').modal('show');tabirmsjembatan('4');">IRMSv3 (jembatan)</button>
                                            </div>
                                            <br/>
                                            <div class="div-tags3">
                                                <!--input id="tags" name="tags" data-role="tagsinput" type="text" class="form-control"/-->
                                            </div>
                                            <input id="wtags" name="wtags" type="hidden"  class="form-control">
                                            <br/>
                                        </div>
                                    </div>
                                    <br/>
                                    <input type="hidden" id="rj_rams"><br>
                                    <input type="hidden" id="rj_irms"><br>
                                    <input type="hidden" id="rj_eprog"><br>
                                    <input type="hidden" id="rj_renstra"><br>
                                    <input type="hidden" id="rj_dpr"><br>
                                    <input type="hidden" id="rj_pemda"><br>
                                    <input type="hidden" id="rj_sipro"><br>
                                    <textarea id="objsumberdana" cols="50" style="display:none;"></textarea></br>
                                    <textarea id="htmldropdownsdana" style="display:none;" cols="50"></textarea></br>
                                </form>
                            </div>
                            <div class="overlay">
                                <div id="loading-img"></div>
                            </div>
                            <!-- /.box-body -->

                            <div class="modal-footer">
                                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Tutup</button>
                                <button id="btn_update_detail" class="btn btn-sm btn-primary" type="button" onclick="javascript:update_form_pagu_detail();"><i class="fa fa-check"></i>Simpan</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- END Slide Right Modal -->
<!--modal rujuk-->
<!--modal DPRD-->
<div class="modal" id="modalDprd4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan DPRD
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="dprd4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Diskresi-->
<div class="modal" id="modalDiskresi4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Diskresi
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="diskresi4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal KL-->
<div class="modal" id="modalKL4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan K/L
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="kl4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal akademik-->
<div class="modal" id="modalAkademisi4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Akademisi
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="akademisi4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal pemda-->
<div class="modal" id="modalPemda4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Pemda
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="tusulanpemda4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal DPR-->
<div class="modal" id="modalDpr4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan DPR
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 100%;" id="tusulandpr4"  class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Renstra-->
<div class="modal" id="modalRenstra4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Renstra
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 100%;" id="trenstra4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>ID Renstra</th>
                                <th>Target</th>
                                <th>Nilai</th>
                                <th>Satuan</th>
                                <th>Tahun</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Eprogram-->
<div class="modal" id="modalEprogram4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                e-PROGRAM
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width:2200px !important;" id="teprogram4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>Usulan</th>
                                <th>Nama Skenario</th>
                                <th>Prioritas</th>
                                <th>Biaya Thn 1</th>
                                <th>Biaya Thn 2</th>
                                <th>Biaya Thn 3</th>
                                <th>Biaya Thn 4</th>
                                <th>Biaya Thn 5</th>
                                <th>Biaya Thn 6</th>
                                <th>Panjang Thn 1</th>
                                <th>Panjang Thn 2</th>
                                <th>Panjang Thn 3</th>
                                <th>Panjang Thn 4</th>
                                <th>Panjang Thn 5</th>
                                <th>Panjang Thn 6</th>
                                <th>Benefit</th>
                                <th>ID Skenario</th>
                                <th>GID</th>
                            </tr>
                        </thead>

                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Irms-->
<!--<div class="modal" id="modalIrms4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
             Modal Header
            <div class="modal-header">
                IRMSv3
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
             Modal body
            <div class="modal-body">
                <div>
                    <table id="tirmsv3_wp4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>PMS TREATMENT</th>
                                <th>TREATMENT COST</th>
                                <th>PMS BUDGET CAT</th>
                                <th>SCN YEAR NUM</th>
                                <th>ROUTE NAME</th>
                                <th>LENGTH</th>
                                <th>LANE DIR NAME</th>
                                <th>OFFSET FROM</th>
                                <th>OFFSET TO</th>
                                <th>BM REGION</th>
                                <th>BM PROVINCE</th>
                                <th>Penanda</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
             Modal footer
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>-->

<!--modal Irms-->
<div class="modal" id="modalIrms4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                IRMSv3(jalan)
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tirmsv3_wp4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>OPTION</th>
                                <th>LINKNAME</th>
<!--                                <th>lane</th>-->
<!--                                <th>PMS section</th>-->
                                <th>START KM</th>
                                <th>END KM</th>
<!--                                <th>LENGTH</th>
                                <th>SCENARIO YEAR</th>
                                <th>IRI</th>
                                <th>KPI</th>
                                <th>PCI</th>-->
                                <th>TREATMENT</th>
                                <th>Treatment CostE</th>
                                <th>Kegiatan</th>
                                <th>output</th>
                                <th>Sub Output</th>
                                <th>Komponen</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<div class="modal" id="modalIrmsJembatan4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                IRMSv3(jembatan)
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tirmsv3jembatan_wp4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>OPTION</th>
                                <th>NAMA RUAS</th>
                                <th>NAMA JEMBATAN</th>
                                <th>PANJANG</th>
                                <th>PENANGANAN</th>
                                <th>ESTIMASI BIAYA</th>
                                <th>LATITUDE</th>
                                <th>LONGITUDE</th>
                                <th>KEGIATAN</th>
                                <th>OUTPUT</th>
                                <th>SUB OUTPUT</th>
                                <th>KOMPONEN</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Sipro-->
<div class="modal" id="modalSipro4">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Sipro
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tsipro_wp4" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>Sa1thn_id</th>
                                <th>Jenis Arahan</th>
                                <th>Kegiatan</th>
                                <th>Output</th>
                                <th>Sub output</th>
                                <th>Komponen</th>
                                <th>Nama Sub Komponen</th>
                                <th>Rc FS</th>
                                <th>Rc DED</th>
                                <th>Rc Lahan</th>
                                <th>Rc Docklin</th>
                                <th>Isu Strategis</th>
                                <th >Sub Kawasan</th>
                                <th>Volume</th>
                                <th >Satuan</th>
                                <th>RPM</th>
                                <th >PHLN</th>
                                <th>SBSN</th>
                                <th >RMP</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<div class="modal fade bd-example-modal-lg loading" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content" style="text-align:center;">
            <span class="fa fa-spinner fa-pulse fa-3x fa-fw"></span>
        </div>
    </div>
</div>
