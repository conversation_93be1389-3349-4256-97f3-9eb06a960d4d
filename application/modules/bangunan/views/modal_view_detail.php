<style>
    #maxx {
        width: 100%;
        height: 100%;
        padding: 0;
        margin:0;
    }
    #maxxs {
        height: 100%;
        border-radius: 0;
        color:#333;
        overflow:auto;
    }

    input[type='number'] {
        -moz-appearance:textfield;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }

    .bd-example-modal-lg .modal-dialog{
        background-color: transparent;
        display: table;
        position: relative;
        margin: 0 auto;
        top: calc(50% - 24px);
        z-index: 999999;
    }

    .bd-example-modal-lg .modal-dialog .modal-content{
        background-color: transparent;
        border: none;
        z-index: 999999;
    }
</style>

<div class="modal fade rotate" id="modal-view-detail"  tabindex="-1" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg" id="maxx">
        <div class="modal-content" id="maxxs">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title tbhItem">Lihat Detail Paket Pagu</h3>
                </div>
                <div class="block-content">
                    <div class="col-md-12" style="background:#a7a7a7;">
                        <div class="container" style="background:#fff;">
                            <div class="row">
                                <div class="col-md-12"><h2 class="content-heading border-bottom mb-4 pb-2">Form Paket</h2><hr></div>
                                <form role="form" method="POST" id="frm-view-detail">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label">Tahun Anggaran</label>
                                            <select id="dthang" class="form-control" style="background-color:#dadada;">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label class="control-label">Program</label>
                                            <select style="background-color:#dadada;" id="dkd_program" class="form-control" onchange="handleProgram(this)">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">Kegiatan</label>
                                            <select style="background-color:#dadada;" id="dkd_kegiatan" class="form-control" onchange="handleKegiatan(this)">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label id="oalabel" class="control-label"></label>
                                            <select style="background-color:#dadada;" id="dkd_output" name="dkd_output" class="form-control" onchange="javascript:handleOutput(this);">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label id="salabel" class="control-label"></label>
                                            <select style="background-color:#dadada;" id="dkd_sub_output" name="dkd_sub_output" class="form-control" onchange="javascript:handleSoutput(this);">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">Komponen</label>
                                            <select style="background-color:#dadada;" id="dkd_komponen" name="dkd_komponen" class="form-control">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="control-label">Kode Paket</label>
                                            <input style="background-color:#dadada;" id="dkd_sub_komponen" type="text" required="required" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label class="control-label">Nama Paket</label>
                                            <input style="background-color:#dadada;" id="dnama_sub_komponen" name="dnama_sub_komponen" type="text" required="required" class="form-control" />
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Jenis Kontrak</label>
                                            <select style="background-color:#dadada;" id="djnskontrak" name="djnskontrak" class="form-control">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!--dari pagu indikatif Awal-->
                                    <div id='dfsk' style="display:none;">
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC DED Status</label>
                                                <select style="background-color:#dadada;" id="drc_ded_status" name="drc_ded_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC FS Status </label>
                                                <select style="background-color:#dadada;" id="drc_fs_status" name="drc_fs_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC Lahan Status</label>
                                                <select style="background-color:#dadada;" id="drc_lahan_status" name="drc_lahan_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC Dockling Status</label>
                                                <select style="background-color:#dadada;" id="drc_doklin_status" name="drc_doklin_status" class="form-control">
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">KAK Status</label>
                                            <select style="background-color:#dadada;" id="dkak" name="dkak" class="form-control"2>
                                                <option value="">--Pilih--</option>
                                                <option value="SIAP">SIAP</option>
                                                <option value="TIDAK_SIAP">TIDAK SIAP</option>
                                                <option value="TDK_PERLU">TIDAK PERLU</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">RAB Status</label>
                                            <select style="background-color:#dadada;" id="drab" name="drab" class="form-control">
                                                <option value="">--Pilih--</option>
                                                <option value="SIAP">SIAP</option>
                                                <option value="TIDAK_SIAP">TIDAK SIAP</option>
                                                <option value="TDK_PERLU">TIDAK PERLU</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Provinsi</label>
                                            <select style="background-color:#dadada;" id="dprov" name="dprov" class="form-control">

                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-12"><h2 class="content-heading border-bottom mb-4 pb-2">Form Detail</h2><hr></div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Kab/Kota</label>
                                            <select id="dkabkot" name="dkabkot" class="form-control" required="required" onchange="javascript:handleRuaskabkot(this, 'd');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Jenis Belanja</label>
                                            <select style="" id="dkdgbkpk" name="dkdgbkpk" class="form-control" required="required" onchange="javascript:handleJnsBelanja(this, 'd');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Jenis Akun</label>
                                            <select id="dkdakun" name="dkdakun" class="form-control" onchange="javascript:handleAkun(this, 'd');" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!--new code-->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Beban/Jns Bantuan/Cara Penarikan </label>
                                            <select id="dsumber" name="dsumber" required="required"  class="form-control" onchange="javascript:handleSumber(this, 'd');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">KPPN</label>
                                            <select id="dkdkppn" name="dkdkppn" class="form-control" onchange="javascript:ChangeSumberList(this, 'd');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Register </label>
                                            <select id="dregister" name="dregister" class="form-control wregister selectpicker with-ajax" data-live-search="true" disabled>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-6">
                                                <div class="panel panel-default" id="dpanelHitung" disabled>
                                                    <div class="panel-heading">
                                                        <table>
                                                            <tr>
                                                                <td><label>Cara Hitung</label></td>
                                                                <td>
                                                                    <div class="form-group" id="dradioHitung">
                                                                        <label class="radio-inline" style="display:none;"><input name="dcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('d');" value="0"/><span style="padding-left:50px; display:none"/></label>
                                                                        <label class="radio-inline"><input name="dcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('d');" value="1"/><span style="padding-left:50px"/>Non PPN</label>
                                                                        <label class="radio-inline"><input name="dcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('d');" value="2"/><span style="padding-left:30px"/>Netto</label>
                                                                        <label class="radio-inline"><input name="dcaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('d');" value="4"/><span style="padding-left:70px"/>Non Sharing</label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="8"><p id="dtext1" style="color:red;"/></td></tr>
                                                            <tr>
                                                                <td style="text-align:right">PHLN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="dinputPHLN" name="dinputPHLN" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('d');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="dinputKPPNph" name="dinputKPPNph"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="dinputRMPdp" name="dinputRMPdp" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('d');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="dinputKPPNrm" name="dinputKPPNrm"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RPLN Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="dinputRPLNPdp" name="dinputRPLNPdp" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('d');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="dinputKPPNrp" name="dinputKPPNrp"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right; width:85px">Register Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="6">
                                                                    <select id="dregisterpdp" name="dregisterpdp" class="form-control wregisterpdp selectpicker with-ajax" data-live-search="true" disabled>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>Catatan Akun (Optional)</label>
                                                    </div>
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="3">&nbsp</td></tr>
                                                            <tr>
                                                                <td style="text-align:right">Halaman 4</td>
                                                                <td style="width:20px">&nbsp;</td>
                                                                <td>
                                                                    <textarea id="dinputHal4" name="dinputHal4" style="width:400px;" class="form-control"></textarea>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">Dipa</td>
                                                                <td style="width:20px">&nbsp;</td>
                                                                <td>
                                                                    <textarea id="dinputDipa" name="dinputDipa" style="width:400px;" class="form-control"></textarea>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">PPK (Header 1)</label>
                                            <select id="did_ppk" name="did_ppk" class="form-control">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="" onchnge="">Header 2 (Optional)</label>
                                            <input id="dheader2" name="dheader2" type="text" class="form-control" maxlength="87" disabled/>
                                        </div>
                                    </div>

                                    <!--new code-->
                                    <div class="col-md-12" id="pann">
                                        <div class="form-group">
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <table>
                                                        <tr>
                                                            <td><label></label></td>
                                                            <td>
                                                                <div id="dradioVolumeKegiatan">
                                                                    <label class="radio-inline id_radioRuas"><input name="dradioVolume" type="radio" onchange="javascript:handleRadioVolume('d');" value="1"/><span style="padding-left:30px"/>Ruas</label>
                                                                    <label class="radio-inline id_radioJembatan"><input name="dradioVolume" type="radio" onchange="javascript:handleRadioVolume('d');" value="2"/><span style="padding-left:50px"/>Jembatan</label>
                                                                    <label class="radio-inline id_radioHonor"><input name="dradioVolume" type="radio" onchange="javascript:handleRadioVolume('d');" value="3"/><span style="padding-left:33px"/>Uraian</label>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="panel-body">
                                                    <div id="dradioHonor" style="display:none">
                                                        <div class="row">
                                                            <div class="col-md-8">
                                                                <table>
                                                                    <tr>
                                                                        <td><input type="number" id="dinputVol1" name="dinputVol1" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="dinputSat1" name="dinputSat1" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="dinputVol2" name="dinputVol2" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="dinputSat2" name="dinputSat2" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="dinputVol3" name="dinputVol3" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="dinputSat3" name="dinputSat3" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="dinputVol4" name="dinputVol4" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="dinputSat4" name="dinputSat4" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('d');"></td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-12">
                                                                Uraian
                                                                <input type="text" id="ddetail" name="ddetail" style="width:100%; display:block;" class="form-control">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="dradioRuas" style="display:none">
                                                        <div class="row">
                                                            <div class="col-md-6 ruasjalan">
                                                                <div class="form-group">
                                                                    <label class="col-md-6">Ruas Jalan</label><div class="col-md-6" style="text-align:right;padding:0px;"><label id="dmaxRuas" style="color:blue;"/></div>
                                                                    <select id="did_ruas" name="did_ruas" class="form-control" onchange="javascript:handleRuas(this, 'd');" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 staw" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput2" onchnge="">STA Awal (Meter)</label>
                                                                    <input type="number" id="dsta_awal" name="dsta_awal" class="form-control"  onkeyup="javascript:handleSTAEditdetail('awal', this);" required="required" placeholder="e.g. 100"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 staw" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput3" onchnge="">STA Akhir (Meter)</label>
                                                                    <input type="number" id="dsta_akhir" name="dsta_akhir" required="required" class="form-control" onkeyup="javascript:handleSTAEditdetail('akhir', this);" placeholder="e.g. 2100"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 jembatans">
                                                                <div class="form-group">
                                                                    <label for="exampleInput4" class="col-md-6">Jembatan</label><div class="col-md-6" style="text-align:right;padding:0px;"></div>
                                                                    <select id="did_jembatan" name="did_jembatan"  class="form-control" onchange="javascript:handleJembatan(this, 'd');" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longs">
                                                                <div class="form-group">
                                                                    <label for="exampleInput5" onchnge="" id="19"></label>
                                                                    <input id="dlongitude" name="dlongitude" type="text" required="required" class="form-control" onkeyup="staAwal('d');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longs">
                                                                <div class="form-group">
                                                                    <label for="exampleInput6" onchnge="" id="20"></label>
                                                                    <input id="dlatitude" name="dlatitude" type="text" required="required" class="form-control" onkeyup="staAwal('d');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longsa" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput5" onchnge="" id="21"> </label>
                                                                    <input id="dlongitude2" name="dlongitude2" type="text" required="required" class="form-control" onkeyup="staAkhir('d');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longsa" style="display: none;" >
                                                                <div class="form-group">
                                                                    <label for="exampleInput6" id="22"> </label>
                                                                    <input id="dlatitude2" name="dlatitude2" type="text" required="required" class="form-control" onkeyup="staAkhir('d');"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longss" style="display: none;">
                                                            <div class="form-group">
                                                                <label>Longitude(X1)</label>
                                                                <input id="dlongitude3" name="dlongitude3" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longss" style="display: none;">
                                                            <div class="form-group">
                                                                <label>Latitude(Y1)</label>
                                                                <input id="dlatitude3" name="dlatitude3" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longsaa" style="display: none;">
                                                            <div class="form-group">
                                                                <label>Longitude(X2)</label>
                                                                <input id="dlongitude4" name="dlongitude4" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3 longsaa" style="display: none;" >
                                                            <div class="form-group">
                                                                <label>Latitude(Y2)</label>
                                                                <input id="dlatitude4" name="dlatitude4" type="text" class="form-control" onkeyup="geoms();"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Volume</label>
                                            <input id="dvolume" name="dvolume" type="text" required="required" class="form-control vol" />
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Satuan</label>
                                            <select id="dsatuan" name="dsatuan" class="form-control" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="form-inline"style="display:inline-block; width:50px;">
                                            <br>
                                            <label class="form-control" style="border:none">X</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="exampleInput8">Harga Satuan (Rupiah)</label>
                                            <input type="text" id="dhargasat" name="dhargasat" required="required" class="form-control number" />
                                        </div>
                                    </div>
                                    <div class="col-md-1" style="display:inline-block; width:60px;">
                                        <div class="form-inline">
                                            <br>
                                            <label class="form-control" style="border:none;">=</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="exampleInput8" >Jumlah (Rupiah)</label>
                                            <input id="djumlah" name="djumlah" type="text" class="form-control number" required="required"/>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-6" id="dpanelNonSharing" style="display:none;">
                                                <div class="panel panel-default">
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="8"><p id="dtext2" style="color:red;"/></td></tr>
                                                            <tr>
                                                                <td style="text-align:right">PHLN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="dinputNSPHLN" name="dinputNSPHLN" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="dinputNSPHLN2" name="dinputNSPHLN2" style="width:155px;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('d');"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="dinputNSRMPdp" name="dinputNSRMPdp" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="dinputNSRMPdp2" name="dinputNSRMPdp2" style="width:155px;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('d');"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RPLN Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="dinputNSRPLNPdp" name="dinputNSRPLNPdp" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="dinputNSRPLNPdp2" name="dinputNSRPLNPdp2" style="width:155px;" class="form-control number" placeholder="Rp" disabled></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">Total </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="3"><input type="text" id="dTotal" name="dTotal" style="width:200px" class="form-control number" placeholder="Rp" disabled></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6" id="dpanelPaguBlokir" style="display:none;">
                                                <div class="panel panel-default">
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="5"><p id="dtextBlokir" style="color:red;"/></td></tr>
                                                            <tr id="dblokirs1" style="display:none;">
                                                                <td style="text-align:right;">Blokir PHLN </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="dinputBlokirPHLN" name="dinputBlokirPHLN" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('d');" disabled></td>
                                                            </tr>
                                                            <tr id="dblokirs2" style="display:none;">
                                                                <td style="text-align:right;">Blokir RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="text" id="dinputBlokirRMPdp" name="dinputBlokirRMPdp" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('d');" disabled></td>
                                                            </tr>
                                                            <tr id="dblokirs3" style="display:none;">
                                                                <td style="text-align:right;">Blokir RPLN Pdp </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="dinputBlokirRPLNPdp" name="dinputBlokirRPLNPdp" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('d');" disabled></td>
                                                            </tr>
                                                            <tr id="drphblok" style="display:none;">
                                                                <td style="text-align:right;">Blokir </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="dinputRphBlokir" name="dinputRphBlokir" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('d');" disabled></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="panel panel-default">
                                            <div class="panel-heading">
                                                <label>Blokir</label>
                                            </div>
                                            <div class="panel-body" style="padding-bottom:0px;">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td style="text-align:right; width:110px; text-align:center; vertical-align:middle;">Kode Blokir</td>
                                                        <td style="text-align:right; width:auto;">
                                                            <select id="dkd_blokir" name="dkd_blokir" class="form-control" onchange="handleBlok(this, 'd');">
                                                                <option value="">--Pilih--</option>
                                                            </select>
                                                        </td>
                                                        <td style="text-align:right; text-align:center; vertical-align:middle; width:110px;">Blokir Detail<span style="padding-left:10px;"/></td>
                                                        <td style="text-align:left; text-align:center; vertical-align:middle; width:10px;"><label class="radio"><input name="ditemblokir" type="radio" value="1" disabled onchange="handleBlokir(this, 'd');"/>Ya</label></td>
                                                        <td style="text-align:left; text-align:center; vertical-align:middle; width:30px;"><label class="radio"><input name="ditemblokir" type="radio" value="0" disabled onchange="handleBlokir(this, 'd');"/><span style="padding-left:10px;"/>Tidak</label></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="text-align:right; text-align:center; vertical-align:middle;">Uraian Blokir</td>
                                                        <td colspan="4">
                                                            <input type="text" id="durblokir" name="durblokir" class="form-control"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-12 tags-edit-detail" style="display:'';">
                                        <div class="form-group" id="md-edit-penandaan">
                                            <label for="exampleInput7" onchnge="">Rujukan</label>
                                            <div style="float:right;">
                                                <button style='background:#5ab4ac' class="btn btn-sm" type="button" onclick="javascript:$('#modalSipro4').modal('show');tabsipro('4');">Sipro</button>
                                                <button style='background:#EDBB99' class="btn btn-sm btnDprd" type="button" onclick="javascript:$('#modalDprd4').modal('show');tabdprd('4');">DPRD</button>
                                                <button style='background:#ffeda0' class="btn btn-sm btnDiskresi" type="button" onclick="javascript:$('#modalDiskresi4').modal('show');tabdiskresi('4');">Diskresi</button>
                                                <button style='background:#FFA07A' class="btn btn-sm btnKL" type="button" onclick="javascript:$('#modalKL4').modal('show');tabkl('4');">K/L</button>
                                                <button style='background:#52BE80' class="btn btn-sm btnAkademisi" type="button" onclick="javascript:$('#modalAkademisi4').modal('show');tabakademisi('4');">Akademisi</button>
                                                <button style='background:#a6cee3' class="btn btn-sm btnPemda" type="button" onclick="javascript:$('#modalPemda4').modal('show');tabpemda('4');">Pemda</button>
                                                <button style='background:#b2df8a' class="btn btn-sm btnDpr" type="button" onclick="javascript:$('#modalDpr4').modal('show');tabdpr('4');">DPR</button>
                                                <button style='background:#cab2d6' class="btn btn-sm btnRenstra" type="button" onclick="javascript:$('#modalRenstra4').modal('show');tabrenstra('4');">Renstra</button>
                                                <button style='background:#fb9a99' class="btn btn-sm btnEprog" type="button" onclick="javascript:$('#modalEprogram4').modal('show');tabprogram('4');">e-PROGRAM</button>
                                                <!--<button style='background:#fdbf6f' class="btn btn-sm btnIrms" type="button" onclick="javascript:$('#modalIrms4').modal('show');tabirms('4');">IRMSv3</button>-->
                                                <button style='background:#fdbf6f' class="btn btn-sm btnIrms" type="button" onclick="javascript:$('#modalIrms4').modal('show');tabirms('4');">IRMSv3 (jalan)</button>
                                                <button style='background:#fcc956' class="btn btn-sm btnIrmsJembatan" type="button" onclick="javascript:$('#modalIrmsJembatan4').modal('show');tabirmsjembatan('4');">IRMSv3 (jembatan)</button>
                                            </div>
                                            <br/>
                                            <div class="div-tags3">
                                            </div>
                                            <input id="dtags" name="dtags" type="hidden"  class="form-control">
                                            <br/>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <!-- /.box-body -->

                            <div class="modal-footer">
                                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Tutup</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bd-example-modal-lg loading" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content" style="text-align:center;">
            <span class="fa fa-spinner fa-pulse fa-3x fa-fw"></span>
        </div>
    </div>
</div>
