<!-- Slide Right Modal -->
<!-- farwah -->
<style>
    #maxx {
        width: 100%;
        height: 100%;
        padding: 0;
        margin:0;
    }
    #maxxs {
        height: 100%;
        border-radius: 0;
        color:#333;
        overflow:auto;
    }

    #tlist_detail_wrapper {
        width: 800px;
        margin: 0 auto;
    }

    #loading-img {
        background: url(<?= base_url(); ?>assets/img/load.gif) center center no-repeat;
        height: 100%;
        z-index: 1000;
    }

    .overlay {
        background: #e9e9e9;
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        opacity: 0.5;
    }
    .ui-autocomplete {
        z-index: 2150000000;
        position: absolute;
    }
    input{
        color:#222 !important;

    }
    select{
        color:#222 !important;
    }

    input[type='number'] {
        -moz-appearance:textfield;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    label{
        color: gray;
    }
    th{
        font-size: 11px !important;
        /* width: auto !important; */
    }
    table tr td{
        font-size: 9px !important;
    }
    #tusulandpr2{
        width: 1800px !important;
    }
    thead{
        background: #cadada6e ;
    }
    th{
        background: #cadada6e ;
    }
    .DTFC_LeftBodyWrapper{
        top:-12px !important;

    }

    .checkboxlabel {
        padding-top: 5px;
        display: block;
        padding-left: 15px;
        text-indent: -15px;
    }

    .checkboxinput {
        width: 13px;
        height: 13px;
        padding: 0;
        margin:0;
        vertical-align: bottom;
        position: relative;
        top: -1px;
        *overflow: hidden;
    }
    #boxpagu h5{
        text-align: center;
    }
    #boxs td {
        padding: 2px !important;
    }
</style>

<div class="modal fade rotate" id="modal-edit-paket"  tabindex="-1" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg" id="maxx">
        <div class="modal-content" id="maxxs">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title tbhItem"></h3>

                </div>
                <div class="block-content">
                    
                    <div class="col-md-12" style="background:#a7a7a7;">
                        <div class="container" style="background:#fff;">
                            <div class="row">
                                <div class="col-md-12">
                                    <!--h2 class="content-heading border-bottom mb-4 pb-2">Form Tambah Investasi Aset Peralatan & Mesin
                                        <!--                                    <div style="float:right;">
                                                                                <button style='background:#8dd3c7; color:black;' class="btn btn-sm" type="button" onclick="javascript:$('#modalSipro').modal('show');tabsipro('2');">Usulan Sipro</button>
                                                                            </div>
                                    </h2-->
                                    <!--hr-->
                                </div>
                                <input type="hidden" id="modeform">
                               
                                <form role="form" method="POST" id="frm-edit-paket" >
                                    <input type="hidden" id="zid_paket" name="zid_paket">
                                    <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>UMUM</label>
                                                    </div>
                                                    <div class="panel-body">                                   
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label class="control-label">Tahun</label>
                                                                    <select id="ztahun" name="ztahun" class="form-control" required="required"></select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Ruas</label>
                                                                    <select id="zid_ruas" name="zid_ruas" class="form-control" required="required" onchange="prov_ruas(this,'update')"></select>
                                                                </div>  
                                                                <div class="form-group">
                                                                    <input type="hidden">
                                                                    <label class="control-label">Jenis Gedung & Bangunan</label>
                                                                    <select id="zkd_jnsbangun" name="zkd_jnsbangun" class="form-control" required="required" onchange="jns_bangunan(this,'edit')"></select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Bangunan</label>
                                                                    <input id="znm_bangunan" name="znm_bangunan" type="text" required="required" class="form-control" />
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Satuan</label>
                                                                    <input id="zsatuan" name="zsatuan" type="text" required="required" class="form-control" readonly/>
                                                                </div> 
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group"  id="hide_jenis_bangunan_1z">
                                                                    <label class="control-label">Luas</label>
                                                                    <input id="zluas" name="zluas" type="text" required="required" class="zluas form-control" />
                                                                </div>                                                              
                                                                <div class="form-group"  id="hide_jenis_bangunan_2z">
                                                                    <label class="control-label">Kuantitas/Jumlah</label>
                                                                    <input id="zkuantitas" name="zkuantitas" type="text" required="required" class="form-control" onkeypress="return isNumber(event)"/>
                                                                </div>                                                             
                                                                <!-- <div class="form-group">
                                                                    <label for="exampleInput8" onchnge="">Nilai Perolehan (Rupiah)</label>
                                                                    <input id="zjumlah" name="zjumlah"  type="text" class="form-control decformat number" required="required"/>
                                                                    <!--input id="xtotalpagu" name="xtotalpagu" type="hidden" /-->
                                                                <!-- </div>  -->
                                                                <div class="form-group">
                                                                    <label class="control-label">Tahun Perolehan</label>
                                                                    <input id="tpz" name="tpz" type="text" required="required" class="form-control" onkeypress="return isNumber(event)"/>
                                                                </div>  
                                                                <div class="form-group">
                                                                    <label class="control-label">Kondisi</label>
                                                                    <select id="zkondisi" name="zkondisi" class="form-control" required="required"></select>
                                                                </div>    
                                                                <div class="form-group">
                                                                    <label class="control-label">Aset Perolehan</label>
                                                                    <select id="zaset_perolehan" name="zaset_perolehan" class="form-control"></select>
                                                                </div>       
                                                                <!-- <div class="form-group">
                                                                    <label class="control-label">Satuan</label>
                                                                    <select id="zsatuan" name="zsatuan" class="form-control" required="required">
                                                                        <option value=''>--Pilih--</option>
                                                                        <option value='unit'>Unit</option>
                                                                        <option value='m2'>m2</option>
                                                                    </select>
                                                                </div>                                                           -->
                                                            </div>
                                                    </div>
                                                </div>
                                            </div>

                                            
                                        <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>LOKASI</label>
                                                    </div>
                                                    <div class="panel-body">                                   
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="exampleInput7" onchnge="">Provinsi</label>
                                                                    <select id="zkd_prov" name="zkd_prov" class="form-control" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="exampleInput7" onchnge="">Kabupaten/Kota</label>
                                                                    <select id="zkd_kabkot" name="zkd_kabkot" class="form-control">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>                                                           
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="exampleInput7" onchnge="">Kecamatan</label>
                                                                    <select id="zkd_camat" name="zkd_camat" class="form-control">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="exampleInput7" onchnge="">Kelurahan/Desa</label>
                                                                    <select id="zkd_lurah" name="zkd_lurah" class="form-control">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>SPESIFIKASI LOKASI</label>
                                                    </div>
                                                    <div class="panel-body">                                   
                                                            <div class="col-md-6">
                                                            <div class="form-group">
                                                                    <label class="control-label">Longitude</label>
                                                                    <input id="zlongitude" name="zlongitude" type="text" required="required" class="zlon form-control" />
                                                                </div>                                                           
                                                            </div>
                                                            <div class="col-md-6">
                                                            <div class="form-group">
                                                                    <label class="control-label">Latitude</label>
                                                                    <input id="zlatitude" name="zlatitude" type="text" required="required" class="zlat form-control" />
                                                                </div>
                                                            </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12" style="display:none;">
                                                    <textarea id="xgeom" name="xgeom" class="form-control"></textarea>
                                                </div>
                                            </div>
                                </form>
                            </div>
                            <div class="overlay">
                                <div id="loading-img"></div>
                            </div>
                            <!-- /.box-body -->

                            <div class="modal-footer">
                                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Tutup</button>
                                <button class="btn btn-sm btn-primary" type="button" onclick="javascript:update_form_pagu();" id="btnsave"><i class="fa fa-check"></i>Simpan</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal" id="modalruas">
    <div class="modal-dialog">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title">Data Ruas</h4>
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <div class="block-header">
                    <button class="btn btn-minw btn-success" onclick="javascript:dtTambahRowruass()">
                        <i class="fa fa-plus" aria-hidden="true">
                        </i>&nbsp;Tambah Ruas</button>
                </div>
                <table id="table_iid" class="display" style="width:100%">
                    <thead>
                        <tr>
                            <th><div style="width:180px;">Nomor Ruas</div></th>
                            <th><div style="width:180px;">Linkname</div></th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
        <!--        <iframe src="<?php //echo base_url(); ?>reff_ruas_nosk"></iframe>-->
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<div class="modal" id="modaljembatan">
    <div class="modal-dialog" style="width:1000px;">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title">Data Jembatan</h4>
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <div class="block-header">
                    <button class="btn btn-minw btn-success" onclick="javascript:dtTambahRowjembatan()">
                        <i class="fa fa-plus" aria-hidden="true">
                        </i>&nbsp;Tambah Ruas</button>
                </div>
                <table id="table_idjem" class="display" style="width:100%">
                    <thead>
                        <tr>
                            <th><div style="width:180px;">Nomor Jmembatan</div></th>
                            <th><div style="width:180px;">Nama Jembatan</div></th>
                            <th><div style="width:180px;">Id Jembatan</div></th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
        <!--        <iframe src="<?php echo base_url(); ?>reff_ruas_nosk"></iframe>-->
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!--new code-->
<div class="modal" id="modalSBM">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Tabel SBM
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <table id="tlist_sbm">
                    <thead>
                        <tr>
                            <th>KDSBU</th>
                            <th>Uraian</th>
                            <th>Satuan</th>
                            <th>Biaya</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal rujuk-->
<!--modal DPRD-->
<div class="modal" id="modalDprd">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan DPRD
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 1800px;" id="dprd2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Diskresi-->
<div class="modal" id="modalDiskresi">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Diskresi
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 1800px;" id="diskresi2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal KL-->
<div class="modal" id="modalKL">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan K/L
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 1800px;" id="kl2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                                <!-- <th>Penanda</th> -->
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal akademik-->
<div class="modal" id="modalAkademisi">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Akademisi
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 1800px;" id="akademisi2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                                <!-- <th>Penanda</th> -->
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal pemda-->
<div class="modal" id="modalPemda">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Pemda
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 1800px;" id="tusulanpemda2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal DPR-->
<div class="modal" id="modalDpr">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan DPR
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 100%;" id="tusulandpr2"  class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Renstra-->
<div class="modal" id="modalRenstra">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Renstra
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 100%;" id="trenstra2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>ID Renstra</th>
                                <th>Target</th>
                                <th>Nilai</th>
                                <th>Satuan</th>
                                <th>Tahun</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Eprogram-->
<div class="modal" id="modalEprogram">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                e-PROGRAM
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width:2200px !important;" id="teprogram2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>Usulan</th>
                                <th>Nama Skenario</th>
                                <th>Prioritas</th>
                                <th>Biaya Thn 1</th>
                                <th>Biaya Thn 2</th>
                                <th>Biaya Thn 3</th>
                                <th>Biaya Thn 4</th>
                                <th>Biaya Thn 5</th>
                                <th>Biaya Thn 6</th>
                                <th>Panjang Thn 1</th>
                                <th>Panjang Thn 2</th>
                                <th>Panjang Thn 3</th>
                                <th>Panjang Thn 4</th>
                                <th>Panjang Thn 5</th>
                                <th>Panjang Thn 6</th>
                                <th>Benefit</th>
                                <th>ID Skenario</th>
                                <th>GID</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Irms-->
<!--<div class="modal" id="modalIrms">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
             Modal Header
            <div class="modal-header">
                IRMSv3
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
             Modal body
            <div class="modal-body">
                <div>
                    <table id="tirmsv3_wp2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>PMS TREATMENT</th>
                                <th>TREATMENT COST</th>
                                <th>PMS BUDGET CAT</th>
                                <th>SCN YEAR NUM</th>
                                <th>ROUTE NAME</th>
                                <th>LENGTH</th>
                                <th>LANE DIR NAME</th>
                                <th>OFFSET FROM</th>
                                <th>OFFSET TO</th>
                                <th>BM REGION</th>
                                <th>BM PROVINCE</th>
                                <th>Penanda</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
             Modal footer
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>-->

<!--modal Sipro-->
<div class="modal" id="modalSipro">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Sipro
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tsipro_wp2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>Sa1thn_id</th>
                                <th>Jenis Arahan</th>
                                <th>Kegiatan</th>
                                <th>Output</th>
                                <th>Sub output</th>
                                <th>Komponen</th>
                                <th>Nama Sub Komponen</th>
                                <th>Rc FS</th>
                                <th>Rc DED</th>
                                <th>Rc Lahan</th>
                                <th>Rc Docklin</th>
                                <th>Isu Strategis</th>
                                <th>Sub Kawasan</th>
                                <th>Volume</th>
                                <th>Satuan</th>
                                <th>RPM</th>
                                <th>PHLN</th>
                                <th>SBSN</th>
                                <th>RMP</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Irms-->
<div class="modal" id="modalIrms">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                IRMSv3(jalan)
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tirmsv3_wp2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>OPTION</th>
                                <th>LINKNAME</th>
<!--                                <th>lane</th>-->
<!--                                <th>PMS section</th>-->
                                <th>START KM</th>
                                <th>END KM</th>
<!--                                <th>LENGTH</th>-->
<!--                                <th>SCENARIO YEAR</th>
                                <th>IRI</th>
                                <th>KPI</th>
                                <th>PCI</th>-->
                                <th>TREATMENT</th>
                                <th>Treatment CostE</th>
                                <th>Kegiatan</th>
                                <th>output</th>
                                <th>Sub Output</th>
                                <th>Komponen</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<div class="modal" id="modalIrmsJembatan">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                IRMSv3(jembatan)
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tirmsv3jembatan_wp2" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>OPTION</th>
                                <th>NAMA RUAS</th>
                                <th>NAMA JEMBATAN</th>
                                <th>PANJANG</th>
                                <th>PENANGANAN</th>
                                <th>ESTIMASI BIAYA</th>
                                <th>LATITUDE</th>
                                <th>LONGITUDE</th>
                                <th>KEGIATAN</th>
                                <th>OUTPUT</th>
                                <th>SUB OUTPUT</th>
                                <th>KOMPONEN</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<div class="modal" id="modalMoves">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Pindah Detail
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="modalMove" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>OPTION</th>
                                <th>KODE KEGIATAN</th>
                                <th>KODE OUTPUT</th>
                                <th>KODE SUB OUTPUT</th>
                                <th>KODE KOMPONEN</th>
                                <th>KODE PAKET</th>
                                <th>NAMA PAKET</th>
                                <!--<th>ID</th>-->
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<script type="text/javascript">
    /* Fungsi formatRupiah */
    function formatRupiah(angka, prefix) {
        var number_string = angka.replace(/[^.\d]/g, '').toString(),
                split = number_string.split(','),
                sisa = split[0].length % 3,
                rupiah = split[0].substr(0, sisa),
                ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        // tambahkan titik jika yang di input sudah menjadi angka ribuan
        if (ribuan) {
            separator = sisa ? ',' : '';
            rupiah += separator + ribuan.join(',');
        }

        rupiah = rupiah;
        return rupiah;
    }
</script>
<!-- END Slide Right Modal -->
