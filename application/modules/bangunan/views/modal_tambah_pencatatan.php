<style>
    table{
        width:100%:
    }
    td{
        padding:5px;
    }
</style>
<?php
   $hide ='';
   if ($this->session->users['role'] != 'admin') {
            $hide = 'none';
       } 

?>
<div class="modal fade rotate" id="modal-tambah-pencatatan"  tabindex="-1" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg" id="maxx">
        <div class="modal-content" id="maxxs">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title tbhItem">Tambah Paket Pagu Indikatif </h3>

                </div>
                <div class="block-content">
                    <div class="col-md-12" style="background:#a7a7a7;">
                        <div class="container" style="background:#fff;">
                            <div class="row">
                                <input type="hidden" id="modeform">
                                <form role="form" method="POST" id="frm-tambah" >
                                    <input type="hidden" id="source_rujukan">
                                            <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>Pencatatan Aset</label>
                                                    </div>
                                                    <div class="panel-body">                                   
                                                          
                                                            <div class="col-md-12">
                                                               <table border="2px">
                                                                   <thead>
                                                                    <tr>
                                                                        <th>Ruas</th>
                                                                        <th style="display:<?php echo $hide;?>">Jumlah Ruas</th>
                                                                        <th>Aset Perolahan</th>
                                                                        <th>Bentuk Aset</th>
                                                                        <th>Kuantitas</th>
                                                                        <th>Satuan</th>
                                                                        <th>Konstruksi Dasar</th>
                                                                        <th>Konstruksi Lapisan Permukaan Awal</th>
                                                                        <th>Pemeliharaan yang Menambah Masa Manfaaat</th>
                                                                        <th>Pengembangan/Peningkatan Kapasitas Aset</th>
                                                                        <th>Jumlah</th>
                                                                        </tr> 
                                                                    </thead>
                                                                    <tbody id ="isi_tabel"></tbody>       
                                                               </table> 

                                                            </div>
                                                    </div>
                                                </div>
                                            </div>      
                                </form>
                            </div>
                            <div class="overlay">
                                <div id="loading-img"></div>
                            </div>
                            <!-- /.box-body -->

                            <div class="modal-footer">
                                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Tutup</button>
                                <!-- <button class="btn btn-sm btn-primary" type="button" onclick="javascript:simpan_form_pagu();"><i class="fa fa-check"></i>Simpan</button> -->
                                <!--button type="button" class="js-swal-success btn btn-light push">
                                    <i class="fa fa-check-circle text-success mr-1"></i> Launch Dialog
                                </button-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




<script type="text/javascript">
    /* Fungsi formatRupiah */
    function formatRupiah(angka, prefix) {
        var number_string = angka.replace(/[^.\d]/g, '').toString(),
                split = number_string.split(','),
                sisa = split[0].length % 3,
                rupiah = split[0].substr(0, sisa),
                ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        // tambahkan titik jika yang di input sudah menjadi angka ribuan
        if (ribuan) {
            separator = sisa ? ',' : '';
            rupiah += separator + ribuan.join(',');
        }

        rupiah = rupiah;
        return rupiah;
    }
</script>