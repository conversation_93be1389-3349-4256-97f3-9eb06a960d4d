<!-- Slide Right Modal -->
<style>
    .modal-detail {
        width: 1000px;
    }

    #loading-img {
        background: url(<?= base_url(); ?>assets/img/load.gif) center center no-repeat;
        height: 100%;
        z-index: 1000;
    }

    .overlay {
        background: #e9e9e9;
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        opacity: 0.5;
    }

    .bootstrap-tagsinput {
        min-height: 100px;
        width: 100%;
    }
</style>
<div class="modal fade rotate" id="modal-detail-edit" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-detail">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title tbhItem">Tambah Detail Paket</h3>
                </div>
                <div class="block-content">
                    <h2 class="content-heading border-bottom mb-4 pb-2 rujuk">Rujukan</h2>
                    <hr class="rujuk">
                    <div class="row rujuk">

                        <!--
                       <div class="col-sm-6">
               
                           <div class="block">
                               <div class="block-header">	
                                   <h4>Sub Komponen/Paket</h4>
                               </div>
                               <div class="block-content">
                                   <table id="tappr_usul2">
                                       <thead>
                                           <tr>
                                               <th>ID Usulan</th>
                                               <th>Tahun Anggaran</th>     
                                               <th>Detail</th>
                                           </tr>
                                       </thead>
                                   </table>
                               </div>
                           </div>
                       </div>
                        -->
                        <div class="col-sm-12">
                            <div class="block">
                                <ul class="nav nav-tabs nav-tabs-right" data-toggle="tabs">
                                    <li class="active" onclick="tabirms('4')">
                                        <a href="#btabswo-static2-irmsv3">IRMSv3</a>
                                    </li>
                                    <li onclick="tabprogram('4')">
                                        <a href="#btabswo-static2-eprogram">e-PROGRAM</a>
                                    </li>
                                    <li  onclick="tabrenstra('4')">
                                        <a href="#btabswo-static2-renstra">Renstra</a>
                                    </li>
                                    <li onclick="tabdpr('4')">
                                        <a href="#btabswo-static2-usulandpr">Usulan DPR</a>
                                    </li>
                                    <li onclick="tabpemda('4')">
                                        <a href="#btabswo-static2-usulanpemda">Usulan PEMDA</a>
                                    </li>
                                    <li class="pull-left">
                                        <ul class="block-options push-10-t push-10-l">
                                            <!--li>
                                                <button type="button" data-toggle="block-option" data-action="close"><i class="si si-close"></i></button>
                                            </li-->
                                            <li>
                                                <button type="button" data-toggle="block-option" data-action="content_toggle"><i class="si si-arrow-up"></i></button>
                                            </li>
                                            <!--li>
                                                <button type="button" data-toggle="block-option" data-action="refresh_toggle" data-action-mode="demo"><i class="si si-refresh"></i></button>
                                            </li>
                                            <li>
                                                <button type="button" data-toggle="block-option" data-action="fullscreen_toggle"><i class="si si-size-fullscreen"></i></button>
                                            </li-->
                                        </ul>
                                    </li>
                                </ul>
                                <div class="block-content tab-content">
                                    <div class="tab-pane active" id="btabswo-static2-sipro" style="overflow:scroll">
                                        <h4 class="font-w300 push-15">WP SIPRO</h4>
                                        <p>
                                        <table id="tsipro_wp4">
                                            <thead>
                                                <tr>
                                                    <th>SA ID</th>
                                                    <th>Kegiatan</th>
                                                    <th>Output</th>
                                                    <th>Suboutput</th>
                                                    <th>Aktivitas</th>
                                                    <th>Sub. Aktivitas</th>
                                                    <th>TA</th>
                                                    <th>Volume</th>
                                                    <th>RPM</th>
                                                    <th>PHLN</th>
                                                    <th>SBSN</th>
                                                    <th>RMP</th>
                                                    <th>ID</th>
                                                    <th>Penanda</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        </p>
                                    </div>
                                    <!-- 
                                    <div class="tab-pane" id="btabswo-static2-rams" style="overflow:scroll">
                                        <h4 class="font-w300 push-15">WP RAMS</h4>
                                        <p>
                                        <table id="trams_wp">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Section ID</th>
                                                    <th>Section Name</th>
                                                    <th>Start M</th>
                                                    <th>End M</th>
                                                    <th>Lane</th>
                                                    <th>Total 2019</th>
                                                    <th>PAV 2019</th>
                                                    <th>Bahu 2019</th>
                                                    <th>Treatment</th>
                                                    <th>Penanda</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        </p>
                                    </div>
                                    -->
                                    
                                    <div class="tab-pane" id="btabswo-static2-irmsv3" style="overflow:scroll">
                                        <h4 class="font-w300 push-15">WP IRMSv3</h4>
                                        <p>
                                        <table id="tirmsv3_wp4">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Plan Year</th>
                                                    <th>Budget Group</th>
                                                    <th>Treatment</th>
                                                    <th>Estimated Cost</th>
                                                    <th>Road Code</th>
                                                    <th>Road Name</th>
                                                    <th>Direction</th>
                                                    <th>Lane</th>
                                                    <th>BMP</th>
                                                    <th>EMP</th>
                                                    <th>MWP Project status</th>
                                                    <th>Penanda</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        </p>
                                    </div>
                                    <div class="tab-pane" id="btabswo-static2-eprogram" style="overflow:scroll">
                                        <h4 class="font-w300 push-15">e-Program</h4>
                                        <p>
                                        <table style="width: 100%;" id="teprogram4">
                                            <thead>
                                                <tr>
                                                    <th>Usulan</th>
                                                    <th>Nama Skenario</th>
                                                    <th>Prioritas</th>
                                                    <th>Biaya Thn 1</th>
                                                    <th>Biaya Thn 2</th>
                                                    <th>Biaya Thn 3</th>
                                                    <th>Biaya Thn 4</th>
                                                    <th>Biaya Thn 5</th>
                                                    <th>Biaya Thn 6</th>
                                                    <th>Panjang Thn 1</th>
                                                    <th>Panjang Thn 2</th>
                                                    <th>Panjang Thn 3</th>
                                                    <th>Panjang Thn 4</th>
                                                    <th>Panjang Thn 5</th>
                                                    <th>Panjang Thn 6</th>
                                                    <th>Benefit</th>
                                                    <th>ID Skenario</th>
                                                    <th>Penanda</th>
               <!--                                <th>Remark</th>
                                                   <th>Panjang</th>
                                                   <th>Bol</th>
                                                   <th>Code</th>
                                                   <th>SID</th>
                                                   <th>No Provinsi</th>
                                                   <th>Data Dasar</th>
                                                   <th>Tahun Dasar</th>
                                                   <th>Inflasi</th>
                                                   <th>Status</th>
                                                   <th>Cakupan User</th>-->
                                                </tr>
                                            </thead>

                                        </table>
                                        </p>
                                    </div>
                                    <div class="tab-pane" id="btabswo-static2-renstra" style="overflow:scroll">
                                        <h4 class="font-w300 push-15">RENSTRA</h4>
                                        <p>
                                        <table style="width: 100%;" id="trenstra4">
                                            <thead>
                                                <tr>
                                                    <th>ID Renstra</th>
                                                    <th>ID Target</th>
                                                    <th>Target</th>
                                                    <th>Nilai</th>
                                                    <th>Satuan</th>
                                                    <th>Tahun</th>
                                                    <th>Penanda</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        </p>
                                    </div>
                                    <div class="tab-pane" id="btabswo-static2-usulandpr" style="overflow:scroll">
                                        <h4 class="font-w300 push-15">USULAN DPR</h4>
                                        <p>
                                        <table style="width: 100%;" id="tusulandpr4">
                                            <thead>
                                                <tr>
                                                    <th class="hidden">ID Usulan</th>
                                                    <th>Tahun Anggaran</th>
                                                    <th>Uraian</th>
                                                    <th>Sumber</th>
                                                    <th>Tanggal</th>
                                                    <th>Pengusul</th>
                                                    <th>Provinsi</th>
                                                    <th>RKAKL Vol.</th>
                                                    <th>RKAKL Biaya</th>
                                                    <th>Status Kewenangan</th>
                                                    <th>Evaluasi</th>
                                                    <th>FS</th>
                                                    <th>DED</th>
                                                    <th>Lahan</th>
                                                    <th>Dok. Ling</th>
                                                    <th>Keterangan</th>
                                                    <th>Usulan Volume</th>
                                                    <th>Usulan Biaya</th>
                                                    <th>Penanda</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        </p>
                                    </div>

                                    <div class="tab-pane" id="btabswo-static2-usulanpemda" style="overflow:scroll">
                                        <h4 class="font-w300 push-15">USULAN PEMDA</h4>
                                        <p>
                                        <table style="width: 100%;" id="tusulanpemda4">
                                            <thead>
                                                <tr>
                                                    <th class="hidden">ID Usulan</th>
                                                    <th>Tahun Anggaran</th>
                                                    <th>Uraian</th>
                                                    <th>Sumber</th>
                                                    <th>Tanggal</th>
                                                    <th>Pengusul</th>
                                                    <th>Provinsi</th>
                                                    <th>RKAKL Vol.</th>
                                                    <th>RKAKL Biaya</th>
                                                    <th>Status Kewenangan</th>
                                                    <th>Evaluasi</th>
                                                    <th>FS</th>
                                                    <th>DED</th>
                                                    <th>Lahan</th>
                                                    <th>Dok. Ling</th>
                                                    <th>Keterangan</th>
                                                    <th>Usulan Volume</th>
                                                    <th>Usulan Biaya</th>
                                                    <th>Penanda</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2 class="content-heading border-bottom mb-4 pb-2">Form Detail Paket</h2>
                    <hr>
                    <div class="row">
                        <input type="hidden" id="modeform">

                        <form role="form" id="frm-detail-edit" action="#">
                            <input type="hidden" id="source_rujukan">
                            <!--input type="hidden" id="id_paket" name="id_paket"-->
                            <div class="col-md-12">  
                                <div class="form-group">
                                    <label class="control-label">Tahun Anggaran</label>
                                    <select id="thang-sel" class="form-control" style="background-color:#dadada;">
                                        <option>--Pilih--</option>
                                    </select>
                                    <input type="hidden" name="thang-sel" />
                                </div>
                            </div>
                            <div class="col-md-12"> 
                                <div class="form-group">
                                    <label class="control-label">Kegiatan</label>
                                    <select id="kd_kegiatan-sel" class="form-control" style="background-color:#dadada;">
                                        <option>--Pilih--</option>
                                    </select>
                                    <input type="hidden" name="kd_kegiatan-sel" />
                                </div>
                            </div>
<!--                            <div class="col-md-12"> 
                                <div class="form-group">
                                    <label class="control-label" style="background-color:#dadada;">Jenis Kegiatan</label>
                                    <select id="jns_giat" class="form-control">
                                        <option value="#">--Pilih--</option>
                                        <option value="F">Fisik</option>
                                        <option value="NF">Non Fisik</option>
                                    </select>
                                    <input type="hidden" name="kd_kegiatan-sel" />
                                </div>
                            </div>-->
                            <div class="col-md-12">  
                                <div class="form-group">
                                    <label class="control-label">Output</label>
                                    <select id="kd_output-sel" name="kd_output-sel" class="form-control"  onchange="javascript:handleOutput2(this);">
                                        <option>--Pilih--</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12"> 
                                <div class="form-group">
                                    <label class="control-label">Sub Output</label>
                                    <select id="kd_sub_output-sel" name="kd_sub_output-sel" class="form-control" onchange="javascript:handleSoutput2(this);">
                                        <option>--Pilih--</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12"> 
                                <div class="form-group">
                                    <label class="control-label">Komponen</label>
                                    <select id="kd_komponen-sel" name="kd_komponen-sel" class="form-control">
                                        <option>--Pilih--</option>
                                    </select>
                                </div>
                            </div>    
                            <div class="col-md-12">    
                                <div class="form-group">
                                    <label class="control-label">Kode Paket</label>
                                    <input id="kd_sub_komponen-sel" type="text" required="required" class="form-control" />
                                    <input type="hidden" name="kd_sub_komponen-sel" />
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="control-label">Nama Paket</label>
                                    <input id="nama_sub_komponen-sel" type="text" required="required" class="form-control" />
                                    <input type="hidden" name="nama_sub_komponen-sel" />
                                </div>
                            </div>
                            <!--div class="col-md-12">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">KPPN</label>
                                    <select id="kdkppn-sel" class="form-control" />
                                    <option value="#">--Pilih--</option>
                                    </select>
                                    <input type="hidden" name="kdkppn-sel" />
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">PPK</label>
                                    <select id="id_ppk-sel" class="form-control" />
                                    <option value="#">--Pilih--</option>
                                    </select>
                                    <input type="hidden" name="id_ppk-sel" />
                                </div>
                            </div-->
                             <div class="col-md-12">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">Jenis Belanja</label>
                                    <select id="kdgbkpk" name="kdgbkpk" class="form-control" onchange="javascript:handleJnsBelanja(this);">
                                        <option value="#">--Pilih--</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">Jenis Akun</label>
                                    <select id="kdakun" name="kdakun" class="form-control">
                                        <option value="#">--Pilih--</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12 divNonFisik">   
                                <div class="form-group">
                                    <label for="exampleInput1" onchnge="">Uraian</label>
                                    <input id="detail" name="detail" type="text" required="required" class="form-control" />
                                </div>
                            </div>               
                            <div class="col-md-6 divRuas" style="display: none;">   
                                <div class="form-group">
                                    <label for="exampleInput1" onchnge="">Uraian</label>
                                    <select id="id_ruas" name="id_ruas" class="form-control" onchange="javascript:handleRuas(this);">
                                        <option value="#">--Pilih--</option>
                                    </select>
                                    <input type="hidden" name="nm_ruas" id="nm_ruas">
                                </div>
                            </div>
                            <div class="col-md-3 divRuas" style="display: none;">
                                <div class="form-group">
                                    <label for="exampleInput2" onchnge="">STA Awal</label>
                                    <select id="sta_awal" name="sta_awal" required="required" class="form-control" >
                                        <option value="#">--Pilih--</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 divRuas" style="display: none;">
                                <div class="form-group">
                                    <label for="exampleInput3" onchnge="">STA Akhir</label>
                                    <select id="sta_akhir" name="sta_akhir" required="required" class="form-control">
                                        <option value="#">--Pilih--</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12 divRuas" style="display: none;">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">Treatment</label>
                                    <select id="treatment" name="treatment" required="required" class="form-control">
                                        <option value="#">--Pilih--</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 divRuas" style="display: none;">
                                <div class="form-group">
                                    <label for="exampleInput4">Uraian</label>
                                    <select id="id_jembatan" name="id_jembatan"  class="form-control" onchange="javascript:handleJembatan(this);">
                                        <option value="#">--Pilih--</option>
                                    </select>
                                    <input type="hidden" name="nm_jembatan" id="nm_jembatan">
                                </div>
                            </div>
                            <div class="col-md-3 divRuas" style="display: none;">
                                <div class="form-group">
                                    <label for="exampleInput5" onchnge="">Longitude</label>
                                    <input id="longitude" name="longitude" type="text" required="required" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-3 divRuas" style="display: none;">
                                <div class="form-group">
                                    <label for="exampleInput6" onchnge="">Latitude</label>
                                    <input id="latitude" name="latitude" type="text" required="required" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exampleInput7" onchnge="">Volume</label>
                                    <input id="volume" name="volume" type="text" required="required" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">Satuan</label>
                                    <input id="satuan" name="satuan" type="text" required="required" class="form-control" />
                                </div>
                            </div>
                            <!--div class="col-md-12">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">Sumber Dana</label>
                                    <select id="kdsdana" name="kdsdana" class="form-control">
                                    </select>
                                </div>
                            </div-->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">Harga Satuan</label>
                                    <input id="hargasat" name="hargasat" type="text" required="required" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exampleInput8" onchnge="">Jumlah</label>
                                    <input id="jumlah" name="jumlah" type="text"  class="form-control jbiaya">
                                </div>
                            </div>                            
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="contacts">
                                        <label>Sumber Dana:</label>
                                        <div class="form-group multiple-form-group input-group">
                                            <div class="input-group-btn input-group-select">
                                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                                    <span class="concept">RM</span> <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu" role="menu" id="kdsdana">

                                                    <!--li><a href="#phone">Phone</a></li>
                                                    <li><a href="#fax">Fax</a></li>
                                                    <li><a href="#skype">Skype</a></li>
                                                    <li><a href="#email">E-mail</a></li>
                                                    <li><a href="#www">Web</a></li-->
                                                </ul>
                                                <input type="hidden" class="input-group-select-val" name="kdsdana['type'][]" value="RM">
                                            </div>
                                            <input type="text" id="rm" name="rm" class="form-control valuesdana">
                                            <span class="input-group-btn">
                                                <button type="button" class="btn btn-success btn-add">+</button>
                                            </span>
                                        </div>
                                    </div>
                                    <!--label for="exampleInput7" onchnge="">Biaya (Rupiah)</label>
                                    <input id="biaya" name="biaya" type="text" required="required" class="form-control" /-->
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="exampleInput7" onchnge="">Penandaan</label><br/>
                                    <div class="div-tags">
                                        <!--input id="tags" name="tags" data-role="tagsinput" type="text" class="form-control"/-->
                                    </div>
                                    <input id="tags" name="tags" type="hidden"  class="form-control">
                                </div>
                            </div>
                        </form> 
                    </div>
                    <div class="overlay">
                        <div id="loading-img"></div>
                    </div>    
                    <!-- /.box-body -->
                </div>                    
                <div class="modal-footer">
                    <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Tutup</button>
                    <button class="btn btn-sm btn-primary" type="button"  data-bs-dismiss="modal" onclick="javascript:simpanForm();"><i class="fa fa-check"></i>Simpan</button>
                    <!--button type="button" class="js-swal-success btn btn-light push">
                        <i class="fa fa-check-circle text-success mr-1"></i> Launch Dialog
                    </button-->
                </div>            
            </div>
        </div>
    </div>
</div>



<!-- END Slide Right Modal -->

