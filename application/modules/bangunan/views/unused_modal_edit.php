<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>

<div class="modal fade" id="modal-edit" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-slideright">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="modalTitle" class="block-title"></h3>
                </div>                

                <div class="block-content">
                    <?php                    
//                   $values['kd_komisi'] = '';
//                   $values['nama_komisi'] = '';
                    
                   $values[] = ''; 

                   Form::open("frm_edit_verifikasi", $values, array("way-data" => "formData", "way-persistent" => "true")); 
                   echo '<legend>Verifikasi</legend>';
                   Form::Textarea("Nama Paket", "nama_sub_komponen", array("required" => 1, "disabled" => 1));
                   Form::Textarea("Nama Kegiatan", "nmgiat", array("required" => 1, "disabled" => 1));
                   Form::Textarea("Nama Output", "nmoutput", array("required" => 1, "disabled" => 1));
                   Form::Textarea("Nama Sub Output", "nmsoutput", array("required" => 1, "disabled" => 1));
                   Form::Textarea("Nama Komponen", "nmkmpnen", array("required" => 1, "disabled" => 1));
                   Form::Textbox("Jumlah", "jumlah", array("required" => 1, "disabled" => 1));
//                    Form::Textbox("Pengusul", "nama_pengusul", ["required" => 1, "disabled" => 1, 'class' => 'form-control-lg']); //, 'placeholder' => 'bootstrap4 .form-control-lg']);
                   Form::Textarea("Satker Pengusul", "nmsatker_pengusul", ["required" => 1, "disabled" => 1, 'class' => 'form-control-lg']); //, 'placeholder' => 'bootstrap4 .form-control-lg']);
                   Form::Textarea("Alasan", "eval1", $attributes = null);
                   Form::Textarea("Alasan", "eval2", $attributes = null);
                   
                   Form::Hidden('kode_satker');
                   Form::Hidden('id_paket');
                   Form::Hidden('modeform');
                   
                   echo '<div class="col-lg-12"><div class="modal-footer">';
//                    Form::Button('Batal', 'button', ["id" => "btnBatal", "class" => "btn btn-default", 'icon' => 'glyphicon glyphicon-unchecked']);
                   Form::Button('Tolak', 'button', ["id" => "btnTolak", "class" => "btn btn-danger", 'icon' => 'glyphicon glyphicon-ban-circle']);
                   //Form::Button('Stock', 'button', ['id' => 'btnStock', "class" => "btn btn-info", "icon" => "glyphicon glyphicon-tag"]);
                   Form::Button('Hold', 'button', ["id" => "btnHold", "class" => "btn btn-primary", 'icon' => 'glyphicon glyphicon-paperclip']);
                   Form::Button('Setujui', 'button', ['id' => 'btnSetuju', "class" => "btn btn-success", "icon" => "glyphicon glyphicon-ok"]);
                   echo '</div></div>';
                    ?>
                </div>
            </div>


        </div>
    </div>
</div>


<?php
Form::close(0);
?>
