<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<script>
//    var xhrdatairms = null;
//    var xhrdatasipro = null;
//    var xhrdatarams = null;
//    var xhrdataeprogram = null;
//    var xhrdatarenstra = null;
//    var xhrdatausulandpr = null;
//    var xhrdatausulanpemda = null;
    
    //modal detail
//    var xhrdataxirms = null;
//    var xhrdatasxipro = null;
//    var xhrdataxrams = null;
//    var xhrdataxeprogram = null;
//    var xhrdataxrenstra = null;
//    var xhrdataxusulandpr = null;
//    var xhrdatausxulanpemda = null;

    var tlist_paket = null;




    var user_satker = "<?php echo $this->session->users['kode_satker']; ?>";
    var id_user_get = "<?php echo $this->session->users['id_user']; ?>";
    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";
    var id_user_group = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var yearnow = "<?php echo $this->session->konfig_tahun_ang; ?>"
    var kd_satker = "<?php echo $this->session->users['kode_satker']; ?>";


//     $columns = array(
//            array('db' => 'sa1thn_id', 'dt' => 0),//ditampilkan//tarik 
//            array('db' => 'tahun_anggaran', 'dt' => 1),//tarik
//            array('db' => 'kawasan_nama', 'dt' => 2),//tarik
//            array('db' => 'subkawasan_nama', 'dt' => 3),//tarik
//            array('db' => 'kegiatan_nama', 'dt' => 4),
//            array('db' => 'suboutput_nama', 'dt' => 5),//tarik
//            array('db' => 'output_nama', 'dt' => 6),//tarik
//            array('db' => 'sub_aktivitas', 'dt' => 7),           
//            array('db' => 'satuan_output', 'dt' => 8),
//            array('db' => 'volume', 'dt' => 9),//tarik                        
//            array('db' => 'rpm', 'dt' => 10), //tarik           
//            array('db' => 'phln', 'dt' => 11),//tarik                            
//            array('db' => 'sbsn', 'dt' => 12),//tarik           
//            array('db' => 'rmp', 'dt' => 13),//tarik         
//            array('db' => 'unit_id', 'dt' => 14),//tidak ditampilkan//tarik
//            array('db' => 'program_id', 'dt' =>15),//tarik
//            array('db' => 'kegiatan_id', 'dt' =>16),//tarik
//            array('db' => 'output_id', 'dt' => 17),//tarik           
//            array('db' => 'suboutput_id', 'dt' =>18),//tarik         
//            array('db' => 'provinsi_id', 'dt' =>19),//tarik 
//            array('db' => 'kabkot', 'dt' =>20), //tarik                                   
//            array('db' => 'jenis_kontrakID', 'dt' => 21), //tarik            
//            array('db' => 'rc_FS', 'dt' => 22), //tarik                            
//            array('db' => 'rc_DED', 'dt' => 23), //tarik            
//            array('db' => 'rc_Dokling', 'dt' => 24),//tarik                             
//            array('db' => 'rc_lahan', 'dt' => 25),//tarik 
//            array('db' => 'wps_kode', 'dt' => 26),//tarik 
//            array('db' => 'kws_kode', 'dt' => 27),//tarik           
//            array('db' => 'status_konreg', 'dt' => 28),//tarik 
//            array('db' => 'status_verifikasi', 'dt' =>29),//tarik 
//            array('db' => 'status_rakor', 'dt' => 30),//tarik 
//            array('db' => 'catatan', 'dt' => 31),//tarik 
//            array('db' => 'isu_strategis_id', 'dt' => 32),//tarik 
//            array('db' => 'isu_strategis_nama', 'dt' => 33),//tarik 
//            array('db' => 'wps_nama', 'dt' => 34)
//        );



    

    var data_cart = [];
    var obj_data_cart={};
    function add_to_chart(sa1thn_id, iDataIndex) {
        //alert(sa1thn_id);
        var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
        //console.log('---data selected---');
        //console.log(data_selected);
        //if ($().is(':checked')) {
        var obj_arahan = {
            "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#kd_komponen").val(),
            "kd_sub_komponen": $("#kd_sub_komponen").val(),
            "nama_sub_komponen": $("#nama_sub_komponen").val(),
            "kd_jns_belanja": $("#kdgbkpk").val(),
            "kdakun": $("#kdakun").val(),
            "kdkppn": $("#kdkppn").val(),
            "id_ppk": $("#id_ppk").val(),
            "rc_ded_status": $("#rc_ded_status").val(),
            "rc_fs_status": $("#rc_fs_status").val(),
            "rc_lahan_status": $("#rc_lahan_status").val(),
            "rc_doklin_status": $("#rc_doklin_status").val(),
            "jnskontrak": $("#jnskontrak").val(),
            "id_ruas": $("#id_ruas").val(),
            "sta_awal": $("#sta_awal").val(),
            "sta_akhir": $("#sta_akhir").val(),
            "id_jembatan": $("#xid_jembatan").val(),
            "longitude": $("#longitude").val(),
            "latitude": $("#latitude").val(),
            //"id_paket":$("#").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#hargasat").val(),
            "jumlah": data_selected[10],
        };
       
        //obj_data_cart=obj_arahan;
        //
        console.log("object arahan");
        console.log(obj_arahan);

        $("#thang").val(data_selected[1]);
        $("#kd_isu").val(data_selected[32]);

        //alert(check_rc((obj_arahan.rc_DED)));
        $("#rc_ded_status").val(check_rc((obj_arahan.rc_DED)));
        $("#rc_fs_status").val(check_rc((obj_arahan.rc_FS)));
        $("#rc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
        $("#rc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
        $("#xvolume").val(data_selected[9]);
        //$("#wps_kode").append("<option selected value="+data_selected[26]+" >"+data_selected[34]+"</option>");
       //alert(isNull(obj_arahan.wps_kode));
        if(isNull(obj_arahan.wps_kode)=="#"){
            //initCombobox('wps_kode',59);    
        }else{
            //refreshCombobox('wps_kode', 59, 'wps_kode', obj_arahan.wps_kode);
        }
        var rIdprov=$("#id_rprov").val();
        //refreshCombobox('prov', 60, 'kd_prov',rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',rIdprov,rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',id_rprov,id_rprov);
        //var xvalSelect = obj_arahan.provinsi_id + "::" + obj_arahan.kabkot;
        refreshComboboxOutput('kabkot', 61, 'id_kabkot',obj_arahan.kabkot,obj_arahan.kabkot);
        //refreshCombobox4('kabkot', 61, 'kd_prov::id_kabkot', xvalSelect, obj_arahan.kabkot);
        
       
        //refreshCombobox('kabkot', 61, 'kd_prov',rIdprov);
        //$("#wps_kode").val(data_selected[26]);
        //$("#kws_kode").append("<option selected value="+data_selected[27]+" >"+data_selected[2]+"</option>");

        //$("#subkw").append("<option selected value="+""+" >"+data_selected[36]+"</option>");
        $("#subkw").val(data_selected[36]);
        //$("#kd_kegiatan").append("<option selected value="+data_selected[16]+" >"+data_selected[4]+"</option>");
        $("#kd_kegiatan").val(data_selected[16]);

        $(".decformat").val(obj_arahan.rpm);
        $("#totalpagu").val(obj_arahan.rpm);
        $("#rm").val(obj_arahan.rpm);
        $("#volume").val(obj_arahan.volume);
        $("#satuan").val(obj_arahan.satuan);

        $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
        var j = function () {
            var defer = $.Deferred();




            refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);

            //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
            //initCombobox('kd_kegiatan-sel', 5);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };
        var a = function () {
            var defer = $.Deferred();

            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
            refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);
            //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        var b = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
            refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };




        var c = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode;
            //alert(obj_arahan.kws_kode);


            //value="-1"
            //alert(obj_arahan.kws_kode);
            var selected = "";
            if (obj_arahan.kws_kode == null) {
                selected = "-1"
            } else {

                selected = obj_arahan.kws_kode;
            }
            refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);

            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        //wps_kode,kws_kode
        var d = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
            refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        c().then(d).then(j).then(a).then(b);

        data_cart.push(obj_arahan);
        console.log("data cart")
        console.log(JSON.stringify(data_cart));
    }
    
    
    var data_cart_edit = [];
    var obj_data_cart_edit={};
    function add_to_chart_edit(sa1thn_id, iDataIndex) {
        //alert(sa1thn_id);
        var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
        //console.log('---data selected---');
        //console.log(data_selected);
        //if ($().is(':checked')) {
        var obj_arahan = {
            "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#zkd_komponen").val(),
            "kd_sub_komponen": $("#zkd_sub_komponen").val(),
            "nama_sub_komponen": $("#znama_sub_komponen").val(),
            "kd_jns_belanja": $("#zkdgbkpk").val(),
            "kdakun": $("#zkdakun").val(),
            "kdkppn": $("#zkdkppn").val(),
            "id_ppk": $("#zid_ppk").val(),
            "rc_ded_status": $("#zrc_ded_status").val(),
            "rc_fs_status": $("#zrc_fs_status").val(),
            "rc_lahan_status": $("#zrc_lahan_status").val(),
            "rc_doklin_status": $("#zrc_doklin_status").val(),
            "jnskontrak": $("#zjnskontrak").val(),
            "id_ruas": $("#zid_ruas").val(),
            "sta_awal": $("#zsta_awal").val(),
            "sta_akhir": $("#zsta_akhir").val(),
            "id_jembatan": $("#zxid_jembatan").val(),
            "longitude": $("#zlongitude").val(),
            "latitude": $("#zlatitude").val(),
            //"id_paket":$("#").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#zhargasat").val(),
            "jumlah": data_selected[10],
        };
       
        //obj_data_cart=obj_arahan;
        //
        console.log("object arahan");
        console.log(obj_arahan);

        $("#zthang").val(data_selected[1]);
        $("#zkd_isu").val(data_selected[32]);

        //alert(check_rc((obj_arahan.rc_DED)));
        $("#zrc_ded_status").val(check_rc((obj_arahan.rc_DED)));
        $("#zrc_fs_status").val(check_rc((obj_arahan.rc_FS)));
        $("#zrc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
        $("#zrc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
        $("#zvolume").val(data_selected[9]);
        //$("#wps_kode").append("<option selected value="+data_selected[26]+" >"+data_selected[34]+"</option>");
       //alert(isNull(obj_arahan.wps_kode));
        if(isNull(obj_arahan.wps_kode)=="#"){
            //initCombobox('wps_kode',59);    
        }else{
            //refreshCombobox('wps_kode', 59, 'wps_kode', obj_arahan.wps_kode);
        }
        var rIdprov=$("#id_rprov").val();
        //refreshCombobox('prov', 60, 'kd_prov',rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',rIdprov,rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',id_rprov,id_rprov);
        //var xvalSelect = obj_arahan.provinsi_id + "::" + obj_arahan.kabkot;
        refreshComboboxOutput('kabkot', 61, 'id_kabkot',obj_arahan.kabkot,obj_arahan.kabkot);
        //refreshCombobox4('kabkot', 61, 'kd_prov::id_kabkot', xvalSelect, obj_arahan.kabkot);
        
       
        //refreshCombobox('kabkot', 61, 'kd_prov',rIdprov);
        //$("#wps_kode").val(data_selected[26]);
        //$("#kws_kode").append("<option selected value="+data_selected[27]+" >"+data_selected[2]+"</option>");

        //$("#subkw").append("<option selected value="+""+" >"+data_selected[36]+"</option>");
        $("#zsubkw").val(data_selected[36]);
        //$("#kd_kegiatan").append("<option selected value="+data_selected[16]+" >"+data_selected[4]+"</option>");
        $("#zkd_kegiatan").val(data_selected[16]);

        $(".decformat").val(obj_arahan.rpm);
        $("#ztotalpagu").val(obj_arahan.rpm);
        $("#zrm").val(obj_arahan.rpm);
        $("#zvolume").val(obj_arahan.volume);
        $("#zsatuan").val(obj_arahan.satuan);

        $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
        var j = function () {
            var defer = $.Deferred();




            refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);

            //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
            //initCombobox('kd_kegiatan-sel', 5);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };
        var a = function () {
            var defer = $.Deferred();

            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
            refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);
            //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        var b = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
            refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };




        var c = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode;
            //alert(obj_arahan.kws_kode);


            //value="-1"
            //alert(obj_arahan.kws_kode);
            var selected = "";
            if (obj_arahan.kws_kode == null) {
                selected = "-1"
            } else {

                selected = obj_arahan.kws_kode;
            }
            refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);

            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        //wps_kode,kws_kode
        var d = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
            refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        c().then(d).then(j).then(a).then(b);

        data_cart_edit.push(obj_arahan);
        console.log("data cart")
        console.log(JSON.stringify(data_cart_edit));
    }
    
    
    //memanaggil API
    function get_data_wps(){
            //alert(typeof JSON.parse(usulans));
        var id_province=$("#id_rprov").val();
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_wps2') ?>"+"/"+id_province;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
                console.log("javascript response")
                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    
    function get_data_kawasan(kawasan_kode){
         var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_kws2') ?>"+"/"+kawasan_kode;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
                console.log("javascript response")
                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    
     function get_data_subkawasan(kawasan_kode){
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_subkws2') ?>"+"/"+kawasan_kode;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
//                console.log("javascript response")
//                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
   
    function handleRuas(el)
    {
        //refreshCombobox2('id_jembatan', 32, 'linkid', el.value);
        refreshCombobox3('sta_awal-sel', 35, 'kode_ruas', el.value);
        refreshCombobox3('sta_akhir-sel', 35, 'kode_ruas', el.value);
//
//
//        var tirmsv3_wp = $('#tirmsv3_wp').DataTable();
//        var trams_wp = $('#trams_wp').DataTable();
//        tirmsv3_wp.ajax.reload();
//        trams_wp.ajax.reload();
   
    }
    function isNull(data){
        //alert(data);
        var result="";
        if (data == "-") {
                result = "#"; //--pilih--
            } else if (data == "") {
                result = "#";
            } else if (data == null) {
                result = "#";
            } 
            
            return result;
    }


    function check_rc(data) {
        //alert(data);
        var result = "";
        if (isNaN(data) == false) {

            if (data < yearnow) {
                result = "siap";
            } else {
                result = "tidak_siap";
            }

        } else {

            if (data == "-") {
                result = "#"; //--pilih--
            } else if (data == "") {
                result = "#";
            } else if (data == null) {
                result = "#";
            }

        }
        return result;
    }




    function fnFormatDetails(table_id, html) {
        //var sOut = "<table id=\"tlist_detail_" + table_id + "\">";
        return "<table id=\"tlist_detail_" + table_id + "\">" +
                "<thead>" +
                "<tr>" +
                "<th>Rujukan</th>" +
                "<th>ID</th>" +
                "<th>Tahun</th>" +
                "<th>Paket</th>" +
                "<th>Uraian</th>" +
                "<th>Ruas</th>" +
                "<th>STA Awal</th>" +
                "<th>STA Akhir</th>" +
                "<th>Jembatan</th>" +
                "<th>Koord. X</th>" +
                "<th>Koord. Y</th>" +
                "<th>Penanganan</th>" +
                "<th>Volume</th>" +
                "<th>Satuan</th>" +
                "<th>Jenis Belanja</th>" +
                "<th>Akun</th>" +
                "<th>Jumlah</th>" +
                "<th>#</th>" +
                "</tr>" +
                "</thead>" +
                "</table>";
        //sOut += html;
        //sOut += "</table>";
//        return sOut;
    }



//function format ( d ) {
//    // `d` is the original data object for the row
//    return '<table cellpadding="5" cellspacing="0" border="0" style="padding-left:50px;">'+
//        '<tr>'+
//            '<td>Full name:</td>'+
//            '<td>'+'sefdsf'+'</td>'+
//        '</tr>'+
//        '<tr>'+
//            '<td>Extension number:</td>'+
//            '<td>'+'aaaaa'+'</td>'+
//        '</tr>'+
//        '<tr>'+
//            '<td>Extra info:</td>'+
//            '<td>And any further details here (images etc)...</td>'+
//        '</tr>'+
//    '</table>';
//}
    
    function bind_wps_by_province(){
        var obj_wps=get_data_wps();
         $("#wps_kode").empty();
          $("#wps_kode").append("<option value='#'>Pilih</option>");
        for(var i=0; i<= obj_wps.length-1; i++){
            var option_value=obj_wps[i].wps_kode+"::"+obj_wps[i].kws_kode
            var html_option = [
            "<option value="+option_value+" >",
                obj_wps[i].wps_nama,
            "</option>",
            ].join("\n");
            $("#wps_kode").append(html_option);
        }
    }
    
     function bind_kawasan_by_kws_kode(element){
        
        var kawasan_kode=element.value.split('::')[1];
      
        var obj_kawasan=get_data_kawasan(kawasan_kode);
        //alert(obj_kawasan);
        $("#kws_kode").empty();
        for(var i=0; i<= obj_kawasan.length-1; i++){
            var option_value=obj_kawasan[i].kws_kode+"::"+obj_kawasan[i].subkawasan_nama;
            var html_option = [
            "<option value="+option_value+" >",
                obj_kawasan[i].kws_nama,
            "</option>",
            ].join("\n");
            $("#kws_kode").append(html_option);
        }
        
        bind_subkawasan(kawasan_kode);
    }
    
    function bind_subkawasan(kawasan_kode){
       // var kawasan_kode=element.value.split('::')[0];
        //alert(kawasan_kode);
        var obj_subkawasan=get_data_subkawasan(kawasan_kode);
        for(var i=0; i<= obj_subkawasan.length; i++){
            var option_value=obj_subkawasan[i].subkawasan_nama;
            var html_option = [
            "<option value="+option_value+" >",
                obj_subkawasan[i].subkawasan_nama,
            "</option>",
            ].join("\n");
            $("#subkw").append(html_option);
        }
    }
    
    var iTableCounter = 1;
    var oTable;
    var oInnerTable;
    var TableHtml;

    function count_jumlah(element){
        //alert("AAAAAAa");
        var hargasatuan=element.value.replace(/\D/g,'');;
        var volume=$("#volume").val();
        var jumlah = parseFloat(hargasatuan)*volume;
        $("#yjumlah").val(jumlah);
        //alert(jumlah);
        
    }

    $(document).ready(function () {
        //bind_combo_wps();

        if (typeof (kd_satker) == 'undefined' || kd_satker == '' || kd_satker == null)
        {
            //do nothing

        } else
        {
            refreshCombobox('id_ruas', 33, 'kode_satker', kd_satker);//modal-detail
            refreshCombobox('xid_ruas', 33, 'kode_satker', kd_satker);//modal-tambah
            refreshCombobox('wid_ruas', 33, 'kode_satker', kd_satker);//modal-tambah
            refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
            refreshCombobox('kdkppn-sel', 46, 'kdsatker', kd_satker);
        }

        //initCombobox('id_ruas', 33);
        //initCombobox('kdgbkpk', 36);
        //initCombobox('id_satuan', 26);
        //multipleSelect('kdsdana', 38);
        var xkdprov=$("#prov").val();
        var id_rprov=$("#id_rprov").val();
        initCombobox('jnskontrak', 23);
        //initCombobox('wps_kode', 59);       
        //initCombobox('prov', 55);
        refreshComboboxOutput('prov', 60, 'kd_prov',id_rprov,id_rprov);
        refreshCombobox('kabkot', 61, 'kd_prov',id_rprov);
        bind_wps_by_province();
        //initCombobox('kabkot',61);  
        //refreshCombobox('kabkot', 49, 'kd_prov',id_rprov);
        

//       $('input[name^=sum]').on("keyup", function(){
//		var $this = $(this);
//		var $parent = $this.parents('tr');
//		var $chance = $parent.find('.chance');
//		var $number = $parent.find('.number');    
//		$chance.text($number.text() * $this.val());
//	});




//        $('.decformat').on("keyup", function (event) {
//            // skip for arrow keys
//            if (event.which >= 37 && event.which <= 40)
//                return;
//
//            // format number
//            $(this).val(function (index, value) {
//                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
//            });
//        });


//
////        alert(kd_satker);
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function () {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });

        $(document).on('hidden.bs.modal', '.modal', function () {
            $('.modal:visible').length && $(document.body).addClass('modal-open');
        });

        $('.rujuk').hide();


//        $('#modal-detail').on('hidden.bs.modal', function () {
//            // do something…
//            $('.div-tags').empty();
//
//        })


        var addFormGroup = function (event) {
            event.preventDefault();
            var $formGroup = $(this).closest('.form-group');
            var $multipleFormGroup = $formGroup.closest('.multiple-form-group');
            var $formGroupClone = $formGroup.clone();
            $(this).toggleClass('btn-success btn-add btn-danger btn-remove').html('–');
            $formGroupClone.find('input').val('');
            $formGroupClone.find('.concept').text('RM');
            $formGroupClone.insertAfter($formGroup);
            var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
            if ($multipleFormGroup.data('max') <= countFormGroup($multipleFormGroup)) {
                $lastFormGroupLast.find('.btn-add').attr('disabled', true);
            }
        };
        var removeFormGroup = function (event) {
            event.preventDefault();
            var $formGroup = $(this).closest('.form-group');
            var $multipleFormGroup = $formGroup.closest('.multiple-form-group');
            var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
            if ($multipleFormGroup.data('max') >= countFormGroup($multipleFormGroup)) {
                $lastFormGroupLast.find('.btn-add').attr('disabled', false);
            }

            $formGroup.remove();
        };
        var selectFormGroup = function (event) {
            event.preventDefault();
            var $selectGroup = $(this).closest('.input-group-select');
            var param = $(this).attr("href").replace("#", "");
            var concept = $(this).text();
            //alert(param);

            $selectGroup.find('.concept').text(concept);
            $selectGroup.find('.input-group-select-val').val(param);
            var lowParam = param.toLowerCase();
            var $selectEntryDana = $(this).closest('.input-group');
            $selectEntryDana.find('.valuesdana').attr("id", lowParam);
            $selectEntryDana.find('.valuesdana').attr("name", lowParam);
        }

        var countFormGroup = function ($form) {
            return $form.find('.form-group').length;
        };
        $(document).on('click', '.btn-add', addFormGroup);
        $(document).on('click', '.btn-remove', removeFormGroup);
        $(document).on('click', '.dropdown-menu a', selectFormGroup);
        initCombobox('kd_isu', 3);
//        initCombobox('kd_sasaran_pembangunan', 14);
//        initCombobox('kd_sasaran_strategis', 15);
        //initCombobox('kdunit', 22);

        initCombobox('thang', 28);
        // initCombobox('id_ppk', 47);

        initCombobox('thang-sel', 28);
        //initCombobox('id_ppk-sel', 47);
        initCombobox('kd_kegiatan', 5);


        if (typeof (kd_satker) == 'undefined' || kd_satker == '' || kd_satker == null)
        {
            //do nothing

        } else
        {
            refreshCombobox('xid_ruas', 33, 'kode_satker', kd_satker);
            refreshCombobox('id_ppk', 47, 'kdsatker', kd_satker);
            refreshCombobox('id_ppk-sel', 47, 'kdsatker', kd_satker);
            refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
            refreshCombobox('kdkppn-sel', 46, 'kdsatker', kd_satker);
        }




        $("#thang").change(function () {
            var thang = $('#thang').val();

            //refreshCombobox('kd_kegiatan', 5, 'thang', thang);
            $('#kd_output').empty();
            $('#kd_output').append(new Option("--Pilih--", -1));
            $('#kd_sub_output').empty();
            $('#kd_sub_output').append(new Option("--Pilih--", -1));
            $('#kd_komponen').empty();
            $('#kd_komponen').append(new Option("--Pilih--", -1));
            initCombobox('kd_kegiatan', 5);

        });





        $('#sta_awal-sel, #sta_akhir-sel').on('change', function () {

            tirmsv3_wp.ajax.reload();
            trams_wp.ajax.reload();
            var staa = $('#sta_awal-sel').val();
            var stae = $('#sta_akhir-sel').val();
            var volume = parseFloat((stae - staa) / 1000);
            $('#volume').val(volume);

        });
        
         //modal tambah paket
         $('#sta_awal, #sta_akhir').on('change', function () {

            tirmsv3_wp.ajax.reload();
            trams_wp.ajax.reload();
            var staa = $('#sta_awal').val();
            var stae = $('#sta_akhir').val();
            var volume = parseFloat((stae - staa) / 1000);
            $('#xvolume').val(volume);

        });
        
         //modal edit detail
         $('#wsta_awal, #wsta_akhir').on('change', function () {

            tirmsv3_wp.ajax.reload();
            trams_wp.ajax.reload();
            var staa = $('#wsta_awal').val();
            var stae = $('#wsta_akhir').val();
            var volume = parseFloat((stae - staa) / 1000);
            $('#wvolume').val(volume);

        });




        var tlist_paket = $("#tlist_paket").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "deferRender": true,
            "order": [[1, "desc"]],
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                      ?>",
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_paket",
                type: "POST"
//                data: function(d){
//                      d.thang = yearnow;  
//                }
            },
            "aoColumnDefs": [
//                {"aTargets": [8], "visible": false},
//                {
//                    "aTargets": [9],
//                    "mRender": function (data, type, row) {
//
//                        var id = row[1];
//                        var kdgiat = row[8];
//
//                        var html_button = [
//                            "<button onclick=upload_attachment('" + row[1] + "','" + row[9] + "','" + row[2] + "'); class='btn btn-success btn-xs' data-toggle='tooltip' title='Upload File'>",
//                            "<i class='fa fa-upload'>",
//                            "</i>",
//                            "</button></a>",
//                            "<button onclick= dtTambahRowDetail('" + id + "','" + kdgiat + "') class='btn btn-success btn-xs' data-toggle='tooltip' title='Tambah Detail Paket'>",
//                            "<i class='fa fa-plus'>",
//                            "</i>",
//                            "</button>",
//                            "<button onclick= dtEditRowPaket('" + id + "') class='btn btn-primary btn-xs' data-toggle='tooltip' title='Edit Paket'>",
//                            "<i class='fa fa-pencil'>",
//                            "</i>",
//                            "</button>",
//                            "<button onclick= dtDeleteRow('paket','" + id + "') class='btn btn-danger btn-xs' data-toggle='tooltip' title='Hapus Paket'>",
//                            "<i class='fa fa-trash'>",
//                            "</i>",
//                            "</button>",
//                            "<button onclick= dtHistory('" + id + "') class='btn btn-warning btn-xs'>History",
//                            "</button></a>"
//                        ].join("\n");
//                        return html_button;
//                    }
//
//
//                }
//                {
//                    "aTargets": [23], "visible": false
//                },
                {
                    "aTargets": [9],
                    "mRender": function (data, type, full) {
                        var html;
////                            console.log('--data from button--');
                        //console.log(data);
//                            var html_button = [
//                                "<button onclick= dtEditRowSipro('" + data + "') class='btn btn-primary btn-xs'>",
//                                "<i class='fa fa-eye'>",
//                                "</i>",
//                                "</button>"
////                                "<button onclick= dtHistory(\'" + data + "\',\'sipro\') class='btn btn-warning btn-xs'>History",
////                                "</button>",
//                            ].join("\n");
//                            return html_button;
                        var xdata = data.split('~')[0];
                        var id = data.split('~')[1];

//                           console.log(xdata + ' ----- ' + id);

                        var d = new Array();
                        var aData = xdata.split('|');
                        // console.log(aData[0]);

//                              console.log(aData[1]);
                        var html = '';
                        aData.forEach(function (e) {
                            eData = e.split('_');
//                              console.log(eData);
                            d.push(eData);
                        });
                        //console.log(d);
//
//                            var aktor = '';


                        for (var i = 0; i < d.length; i++) {
                            switch (i) {
                                case 0:
                                    aktor = full[15] + ' ' + full[17];
                                    break;
                                case 1:
                                    aktor = full[16];
                                    break;
                            }
//                                            alert(aktor);
                            var formtype;

//                                alert(d[i][2] );

                            if (d[i][2] != '0') {
                                formtype = 'add';
                            } else {
                                formtype = 'edit';
                            }

                            //console.log('------->'+d[i][2]);

                            switch (d[i][2]) {
                                case '0': //belum diisi
                                    if (d[i][0] != '0') {
                                        html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-default btn-xs">Belum verifikasi ' + aktor + '</button>\n';
                                    } else {

                                    }
                                    break;
                                case '1': //diterima
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-success btn-xs">Diterima ' + aktor + '</button>\n';
                                    break;
                                case '2': //reject
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-danger btn-xs">Belum bisa dilaksanakan</button>\n';
                                    break;
                                case '3': //hold
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-primary btn-xs">Dihold ' + aktor + '</button>\n';
                                    break;
                                case '4': //stock
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-info btn-xs">Distock ' + aktor + '</button>\n';
                                    break;
                            }
                        }

                        return html;
                    }
                },
                {
                    "aTargets": [10],
                    "mRender": function (data, type, row) {

                        var id = row[1];
                        var kdgiat = row[10];

                        var html_button = [
                            "<button onclick=upload_attachment('" + row[1] + "'); class='btn btn-success btn-xs' data-toggle='tooltip' title='Upload File'>",
                            "<i class='fa fa-upload'>",
                            "</i>",
                            "</button>",
                            "</button>",
                            "<button onclick=download_attachment('" + row[1] + "'); class='btn btn-success btn-xs' data-toggle='tooltip' title='List Attachment'> ",
                            "<i class='fa fa-file'>",
                            "</i>",
                            "</button>"
                                    ,
                            "<button onclick=dtTambahRowDetail('" + id + "','" + kdgiat + "') class='btn btn-success btn-xs' data-toggle='tooltip' title='Tambah Detail Paket'>",
                            "<i class='fa fa-plus'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtEditRowPaket('" + id + "') class='btn btn-primary btn-xs' data-toggle='tooltip' title='Edit Paket'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtDeleteRow('paket','" + id + "') class='btn btn-danger btn-xs' data-toggle='tooltip' title='Hapus Paket'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtHistory('paket','" + id + "') class='btn btn-warning btn-xs'>History",
                            "</button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"className": 'details-control', "width": "2px"},
                {"width": "5px"},
                {"width": "5px"},
                {"width": "125px"},
                {"width": "120px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "125px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        //Insert a 'details' column to the table
//        var nCloneTh = document.createElement('th');
//        var nCloneTd = document.createElement('td');
//        nCloneTd.innerHTML = '<img src="http://i.imgur.com/SD7Dz.png">';
//        nCloneTd.className = "center";
//
//        $('#tlist_paket thead tr').each(function () {
//            this.insertBefore(nCloneTh, this.childNodes[0]);
//        });
//
//        $('#tlist_paket tbody tr').each(function () {
//            this.insertBefore(nCloneTd.cloneNode(true), this.childNodes[0]);
//        });


        $('#tlist_paket tbody').on('click', 'td.details-control img', function () {
            TableHtml = $("#tlist_detail").html();

            var tr = $(this).closest('tr');
            var row = tlist_paket.row(tr);

            var drow = row.data();

            if (row.child.isShown()) {
                // This row is already open - close it
                this.src = "https://datatables.net/examples/resources/details_open.png";
                row.child.hide();
                tr.removeClass('shown');
            } else {
                // Open this row
                //console.log(drow);


                this.src = "https://datatables.net/examples/resources/details_close.png";
                row.child(fnFormatDetails(iTableCounter, TableHtml)).show();
                oInnerTable = $("#tlist_detail_" + iTableCounter).DataTable({
                    "draw": 0,
                    "responsive": true,
                    "processing": true,
                    "serverSide": true,
                    "scrollCollapse": true,
                    "ajax": {
                        url: "<?php echo base_url(); ?>pagu_indikatif/ssp_detail",
                        type: "POST",
                        data: function (d) {
                            // d.id_paket = drow[1];
                            //var kd_sub = drow[3];
                            //var kd = kd_sub.substr(0, 2);
                            d.kdsatker = kd_satker;
                            d.thang = yearnow;
                            d.kd_sub_komponen = drow[3].split(" - ")[0];
                            d.kd_komponen=drow[7].split(" - ")[0];
                            d.kd_giat=drow[4].split(" - ")[0];
                            d.kd_output=drow[5].split(" - ")[0];
                            d.kd_sub_output=drow[6].split(" - ")[0];
                            //d.stae = $('#sta_akhir').val();
                        }
                    },
                    "pagingType": "full_numbers",
                    "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
                    "aoColumnDefs": [
                        {
                            "aTargets": [0],
                            "width": "150px",
                            "mRender": function (data, type, row) {
                                var html = '';
                                var tags;
                                if (row[0] == '')
                                {
                                    tags = [];
                                } else
                                {
                                    tags = JSON.parse(row[0]);
                                }



                                $.each(tags, function (index, value) {
                                    switch (value.rujukan) {
                                        case 'PEMDA':
                                            html += '<span class="tag label label-default">' + value.text + '</span><br/>';
                                            break;
                                        case 'DPR':
                                            html += '<span class="tag label label-default">' + value.text + '</span><br/>';
                                            break;
                                        case 'SIPRO':
                                            html += '<span class="tag label label-primary">' + value.text + '</span><br/>';
                                            break;
                                        case 'IRMS':
                                            html += '<span class="tag label label-success">' + value.text + '</span><br/>';
                                            break;
                                        case 'RAMS':
                                            html += '<span class="tag label label-info">' + value.text + '</span><br/>';
                                            break;
                                        case 'EPROGRAM':
                                            html += '<span class="tag label label-warning">' + value.text + '</span><br/>';
                                            break;
                                        case 'RENSTRA':
                                            html += '<span class="tag label label-danger">' + value.text + '</span>';
                                            break;
                                    }
                                });

                                return html;
                            }
                        },
                        {
                            "aTargets": [1],
                            "width": "20px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [2],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [3],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [4],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [5],
                            "width": "100px",
                            "visible": false
//                        "searchable": true
                        },
                        {
                            "aTargets": [6],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [7],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [8],
                            "width": "100px",
                            "visible": false
//                        "searchable": true
                        },
                        {
                            "aTargets": [9],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [10],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [11],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [12],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [13],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [14],
                            "width": "100px",
                            "visible": false
//                        "searchable": true
                        },
                        {
                            "aTargets": [15],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [16],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },

                        {
                            "aTargets": [17],
                            "mRender": function (data, type, row) {

                                var id = row[1];
                                var html_button = [
                                    "<button onclick=dtEditRowDetail('" + id + "') class='btn btn-primary btn-xs'>",
                                    "<i class='fa fa-pencil'>",
                                    "</i>",
                                    "</button>",
                                    "<button onclick= dtDeleteRow('detail','" + id + "') class='btn btn-danger btn-xs'>",
                                    "<i class='fa fa-trash'>",
                                    "</i>",
                                    "</button>",
                                    "<button onclick=dtHistory('detail','" + id + "') class='btn btn-warning btn-xs'>History",
                                    "</button>",
                                    "<button onclick=editDetail('" + id + "') class='btn btn-dark btn-xs'><i class='fa fa-bars'></i>",
                                    "</button>",
                                ].join("\n");
                                return html_button;
                            }
                        }
                    ],
                    "pageLength": 5,
                    "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
                    "language": {
                        "decimal": "",
                        "emptyTable": "Data tidak ditemukan",
                        "info": "Data _START_ s/d _END_ dari _TOTAL_",
                        "infoEmpty": "Tidak ada data",
                        "infoFiltered": "(tersaring dari _MAX_)",
                        "infoPostFix": "",
                        "thousands": ",",
                        "lengthMenu": "_MENU_  data per halaman",
                        "loadingRecords": "Memuat...",
                        "processing": "Memroses...",
                        "search": "Cari:",
                        "zeroRecords": "Tidak ada data ditemukan",
                        "paginate": {
                            "first": "<i class='fa fa-angle-double-left'></i>",
                            "last": "<i class='fa fa-angle-double-right'></i>",
                            "next": "<i class='fa fa-angle-right'></i>",
                            "previous": "<i class='fa fa-angle-left'></i>"
                        },
                        "aria": {
                            "sortAscending": ": aktifkan untuk mengurutkan naik",
                            "sortDescending": ": aktifkan untuk mengurutkan turun"
                        }
                    }
                });

                $("#tlist_detail_" + iTableCounter).on('xhr', function () {
                    xhrdata = $("#tlist_detail_" + iTableCounter).ajax.json();
                    //            console.log(xhrdata);
                });

                $('#tlist_detail_' + iTableCounter + '_length').hide();
                $("#tlist_detail_" + iTableCounter).addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");

                iTableCounter = iTableCounter + 1;

                tr.addClass('shown');


            }


        });



        tlist_paket.on('xhr', function () {
            xhrdata = tlist_paket.ajax.json();
//            console.log(xhrdata);
        });

//        tlist_paket.on('order.dt search.dt', function () {
//            tlist_paket.column(0, {
//                search: 'applied',
//                order: 'applied'
//            }).nodes().each(function (cell, i) {
//                cell.innerHTML =  '<img src="http://i.imgur.com/d4ICC.png">';
//            });
//        }).draw();

        $("#tlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
       
        
        //arahan modal detail
        $("#xtlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
      
        
       
        
        
         //arahan modal edit detail
      
        $('#btnSetuju').click(function () {
            console.log('stuju');
            var wdata = way.get('formData');
            wdata.status = 1;
//            wdata.id_user = id_user_get;
            console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }

            var params = {"formData": wdata};
            //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {
                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
//                        setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");
        });

        $('#btnTolak').click(function () {

            //$("").insertAfter("#block-ct");


            console.log('tolak');
            var wdata = way.get('formData');
            wdata.status = 2;
            // wdata.id_user = id_user_get;
            //console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }

            var params = {"formData": wdata};
            //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {

                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
                        //setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");

        });


        $('#btnTutupView').click(function () {
            $('#modal-view').modal('hide');
        });


        $('#btnHold').click(function () {
            console.log('hold');
            var wdata = way.get('formData');
            wdata.status = 3;
//            wdata.id_user = id_user_get;
            console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }

            var params = {"formData": wdata};
            //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {
                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
//                        setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");

        });

        $('#btnStock').click(function () {
            console.log('stock');
            var wdata = way.get('formData');
            wdata.status = 4;
//            wdata.id_user = id_user_get;
            console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }

            var params = {"formData": wdata};
            //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {
                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
//                        setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");

        });

        // you would probably be using templates here
        //detailsTableHtml = $("#detailsTable").DataTables();

        //Insert a 'details' column to the table
//        var nCloneTh = document.createElement('th');
//        var nCloneTd = document.createElement('td');
//        nCloneTd.innerHTML = '<img src="https://datatables.net/examples/resources/details_open.png">';
//        nCloneTd.className = "center";
//
//        $('#exampleTable thead tr').each(function () {
//            this.insertBefore(nCloneTh, this.childNodes[0]);
//        });
//
//        $('#exampleTable tbody tr').each(function () {
//            this.insertBefore(nCloneTd.cloneNode(true), this.childNodes[0]);
//        });


        //Initialse DataTables, with no sorting on the 'details' column
//        var oTable = $('#exampleTable').dataTable({
//            "bJQueryUI": true,
//            "aaData": newRowData,
//            "bPaginate": false,
//            "aoColumns": [
//                {
//                    "mDataProp": null,
//                    "sClass": "control center",
//                    "sDefaultContent": '<img src="https://datatables.net/examples/resources/details_open.png">'
//                },
//                {"mDataProp": "race"},
//                {"mDataProp": "year"},
//                {"mDataProp": "total"}
//            ],
//            "oLanguage": {
//                "sInfo": "_TOTAL_ entries"
//            },
//            "aaSorting": [[1, 'asc']]
//        });

        /* Add event listener for opening and closing details
         * Note that the indicator for showing which row is open is not controlled by DataTables,
         * rather it is done here
         */
//        $('#exampleTable tbody td img').live('click', function () {
//            var nTr = $(this).parents('tr')[0];
//            var nTds = this;
//
//            if (oTable.fnIsOpen(nTr)) {
//                /* This row is already open - close it */
//                this.src = "https://datatables.net/examples/resources/details_open.png";
//                oTable.fnClose(nTr);
//            } else {
//                /* Open this row */
//                var rowIndex = oTable.fnGetPosition($(nTds).closest('tr')[0]);
//                var detailsRowData = newRowData[rowIndex].details;
//
//                this.src = "https://datatables.net/examples/resources/details_close.png";
//                oTable.fnOpen(nTr, fnFormatDetails(iTableCounter, detailsTableHtml), 'details');
//                oInnerTable = $("#exampleTable_" + iTableCounter).dataTable({
//                    "bJQueryUI": true,
//                    "bFilter": false,
//                    "aaData": detailsRowData,
//                    "bSort": true, // disables sorting
//                    "aoColumns": [
//                        {"mDataProp": "pic"},
//                        {"mDataProp": "name"},
//                        {"mDataProp": "team"},
//                        {"mDataProp": "server"}
//                    ],
//                    "bPaginate": false,
//                    "oLanguage": {
//                        "sInfo": "_TOTAL_ entries"
//                    },
//                    "fnRowCallback": function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
//                        var imgLink = aData['pic'];
//                        var imgTag = '<img width="100px" src="' + imgLink + '"/>';
//                        $('td:eq(0)', nRow).html(imgTag);
//                        return nRow;
//                    }
//                });
//                iTableCounter = iTableCounter + 1;
//            }
//        });


    });
    function get_wps() {
        var x = null;
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_wps') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    
    
    function get_detail_usulan(id_detail) {
        var x = null;
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_detail_usulan') ?>"+"/"+id_detail,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    
    function editDetail(id_detail){
       
        var obj_usulan= get_detail_usulan(id_detail);
       
        refreshComboboxOutput('wthang', 21, 'thang',obj_usulan.thang,obj_usulan.thang);
        updateComboboxAndSelected('wkd_isu', 3,obj_usulan.kd_isu);
        
       // updateComboboxAndSelected('zwps_kode', 3,data_selected[27]);
        
        refreshComboboxOutput('wkd_kegiatan', 5, 'kdgiat',obj_usulan.kd_kegiatan,obj_usulan.kd_kegiatan);
        
        setTimeout(set_kegiatan, 8000);//set kegiatan berdasarkan kode
        
        $("#wkd_sub_komponen").val(obj_usulan.kd_sub_komponen);
        
        $("#wnama_sub_komponen").val(obj_usulan.nama_sub_komponen);
        $("#wid_paket").val(obj_usulan.id_paket);
        $("#wid_usulan").val(obj_usulan.id_usulan);
        
        
        var id_rprov=$("#id_rprov").val();
        refreshComboboxOutput('wprov', 60, 'kd_prov',id_rprov,id_rprov);
        
        refreshComboboxOutput('wkd_output', 30, 'kdgiat',obj_usulan.kd_kegiatan,obj_usulan.kd_output);
    
        //alert(obj_usulan.kd_output+"--#####--"+obj_usulan.kd_sub_output);
        
        refreshComboboxOutput('wkd_sub_output', 44, 'kdoutput',obj_usulan.kd_output,obj_usulan.kd_sub_output);
        
        refreshComboboxOutput('wkd_komponen', 18, 'kdsoutput',obj_usulan.kd_sub_output,obj_usulan.kd_komponen);
        
        updateComboboxAndSelected('wjnskontrak',23,obj_usulan.jnskontrak);
        
        updateComboboxAndSelected('wkdkppn',9,obj_usulan.kdkppn);
        
        updateComboboxAndSelected('wid_ppk',11,obj_usulan.id_ppk);
        
        updateComboboxAndSelected('wwps_kode',57,obj_usulan.wps_kode);
        
        updateComboboxAndSelected('wkdgbkpk',36,obj_usulan.kdgbkpk);
        //alert(obj_usulan.id_ruas);
        //alert(obj_usulan.no_ruas);
        updateComboboxAndSelected('wid_ruas',33,obj_usulan.no_ruas);
        $("#wsta_awal").append("<option selected value="+obj_usulan.sta_awal+">"+obj_usulan.sta_awal+"</option>");
        $("#wsta_akhir").append("<option selected value="+obj_usulan.sta_akhir+">"+obj_usulan.sta_akhir+"</option>")
       
        $("#wvolume").val(obj_usulan.volume);
        //alert((typeof obj_usulan.id_jembatan));
        
        if(typeof obj_usulan.id_jembatan != "object"){
            
            //$("#wid_jembatan").append("<option selected value="+obj_usulan.id_jembatan+">"+"Jembatan 5555"+"</option>");
            updateComboboxAndSelected('wid_jembatan',56,obj_usulan.id_jembatan);
            $("#wlongitude").val(obj_usulan.longitude);
            $("#wlatitude").val(obj_usulan.latitude);
        }
        $("#wsatuan").val(obj_usulan.satuan);
        $("#wjumlah").val(obj_usulan.jumlah);
        $("#whargasat").val(obj_usulan.hargasat);
        //updateComboboxAndSelected('zid_ppk',11,data_selected[30]);
        
        //refreshComboboxOutput('ykdgbkpk', 36, 'kdgbkpk',data_selected[20],data_selected[20]);
        //initCombobox("zkdgbkpk",36);
        
        initCombobox("wkdakun",37);
        
        $("#wrc_ded_status").append("<option selected value="+obj_usulan.rc_ded_status+">"+obj_usulan.rc_ded_status+"</option>");
        $("#wrc_fs_status").append("<option selected "+obj_usulan.rc_fs_status+">"+obj_usulan.rc_fs_status+"</option>");
        $("#wrc_lahan_status").append("<option selected "+obj_usulan.rc_lahan_status+">"+obj_usulan.rc_lahan_status+"</option>");
        $("#wrc_doklin_status").append("<option selected "+obj_usulan.rc_doklin_status+">"+obj_usulan.rc_doklin_status+"</option>");
        
        //updateComboboxAndSelected('wid_ruas',33,obj_usulan.id_ruas);
        //updateComboboxAndSelected('zkws_kode',57,obj_usulan.kws_kode);
       // alert( $('#yjns_giat option:eq(1)').val());
       //alert($("#ykd_kegiatan-sel").val());
        
        setTimeout(set_kegiatan, 8000);//
        
        
        $("#modal-edit-detail").modal("show")
    }
    
    function update_form_pagu_detail(){
         var obj_usulan = {
            "id_ruas":$("#wid_ruas").val().split('::')[0],           
            "sta_awal":$("#wsta_awal").val(),
            "sta_akhir":$("#wsta_awal").val(),
            "id_jembatan":$("#wid_jembatan").val(),//not exists in form exists in table
            "longitude": $("#wlongitude").val(),//not exists in form exists in table
            "latitude": $("#wlatitude").val(),//not exists in form exists in table
            "volume":$("#wvolume").val(),//not exists in form exists in table
            "satuan": $("#wsatuan").val(),//not exists in form exists in table
            "hargasat": parseFloat($("#whargasat").val()),
            "jumlah":parseFloat($("#wjumlah").val()),
            "id_paket":$("#wid_paket").val(),
            "id_usulan":$("#wid_usulan").val()
        };
        
        var url="<?php echo base_url("pagu_indikatif/update_detail_usulan"); ?>"
        $.ajax({
           type: "POST",
            url:url,
            // The key needs to match your method's input parameter (case-sensitive).
            data: JSON.stringify({ data_detail: obj_usulan}),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (response)
            {


                if (response.status) //if success close modal and reload ajax table
                {

                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();
                    
                    $('#frm-tambah')[0].reset();

                    // Coding
                     $('#modal-edit-detail').modal('hide'); //or  $('#IDModal').modal('hide');

                } else
                {
                    for (var i = 0; i < response.inputerror.length; i++)
                    {
                        $('[name="' + response.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + response.inputerror[i] + '"]').next().text(response.error_string[i]); //select span help-block class set text error string
                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
    }
    
    
    
    function bind_combo_wps(){
        $("#wps_kode").empty();
        var data = get_wps();
        //alert(data);
        for (var i = 0; i <= data.length - 1; i++) {
            $("#wps_kode").append("<option value=" + data[i].wps_kode + ">" + data[i].wps_nama + "</option>")
            console.log(data[i].wps_kode);

        }
    }

    function wps()
    {
        var x = $("#wps_kode").val();
        // alert(x);
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_kws/') ?>" + x,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)

                x = data;
                lookkws(x)

            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });


    }

    function lookkws(x)
    {
        $("#kws_kode").empty();
        var dataa = x;
        var html_looku = "<option value=''>--Pilih KWS----</option>";
        $("#kws_kode").append(html_looku);
        for (var i = 0; i <= dataa.length - 1; i++) {

            var html_lookup = ["<option value=" + dataa[i].kws_kode + " >",
                dataa[i].kws_nama,
                "</option>",
            ].join("\n");

            $("#kws_kode").append(html_lookup);

        }
    }
    function kws()
    {
        var x = $("#kws_kode").val();
        // alert(x);
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_sub/') ?>" + x,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)

                x = data;
                looksub(x)

            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });


    }

    function looksub(x)
    {
        $("#subkw").empty();
        var dataa = x;
        var html_looku = "<option value=''>--Pilih Sub Kawasan----</option>";
        $("#subkw").append(html_looku);
        for (var i = 0; i <= dataa.length - 1; i++) {
            console.log(dataa[i].subkawasan_nama);
            var nm = dataa[i].subkawasan_nama;
            var html_lookup = ['<option value="' + nm + '" >',
                dataa[i].subkawasan_nama,
                '</option>',
            ].join("\n");

            $("#subkw").append(html_lookup);

        }
    }
//    var set_val_thang = function (yearnow) {
//        setTimeout(function () {
//            tlist_paket.columns(2)
//                    .search(yearnow)
//                    .draw();
//        }, 1000);
//    };

    function btnProsesVerifikasiIndi(data) {
        //console.log(data);

        var adata = data.split('~');

        var id_paket = adata[1];
        var idx = parseInt(adata[2], 10);

        console.log('idx ' + idx);
        var sstat = adata[0].split('|')[idx];
        var astat = sstat.split('_');


        var roleberhak = astat[0];

        // console.log(roleberhak);

//        console.log(id_paket);
//        console.log(idx);
//        console.log(sstat);
//        console.log(astat);

        var thedata = xhrdata.data.filter(x => x[1] == id_paket)[0];  //kolom 18 (id paket) sama dengan id
        console.log(thedata);


        var waydata = {
            status_idx: idx,
            id_paket: id_paket,
////            id_flow: (thedata[3 + idx] ? thedata[3 + idx] : ''),
            nama_sub_komponen: (thedata[3] ? thedata[3] : ''),
            nmgiat: (thedata[4] ? thedata[4] : ''),
            nmoutput: (thedata[5] ? thedata[5] : ''),
            nmsoutput: (thedata[6] ? thedata[6] : ''),
            nmkmpnen: (thedata[7] ? thedata[7] : ''),
            kode_satker: (thedata[23] ? thedata[23] : ''),
            modeform: 'tambah',
            //nama_satker: (thedata[24] ? thedata[24] : ''),
//            ket_lokasi: (thedata[2] ? thedata[2] : ''),
            // kd_kegiatan: (thedata[18] ? thedata[18] : ''),
            //   kd_output: (thedata[19] ? thedata[19] : ''),
            //    kd_sub_output: (thedata[20] ? thedata[20] : ''),
//            aktivitas_nama: (thedata[6] ? thedata[6] : ''),
//            sub_aktivitas: (thedata[7] ? thedata[7] : ''),
            tahun_anggaran: (thedata[8] ? thedata[8] : ''),
//            jenis_kontrakNama: (thedata[9] ? thedata[9] : ''),
//            kewenangan: (thedata[10] ? thedata[10] : ''),
//            sumber_dana: (thedata[11] ? thedata[11] : ''),
            volume: (thedata[7] ? thedata[7] : ''),
            satuan: (thedata[6] ? thedata[6] : ''),
            jumlah: (thedata[8] ? thedata[8] : ''),
//            rpm: (thedata[14] ? thedata[14] : ''),
//            phln: (thedata[15] ? thedata[15] : ''),
//            sbsn: (thedata[16] ? thedata[16] : ''),
//            rmp: (thedata[17] ? thedata[17] : ''),
            eval1: (thedata[11] ? thedata[11] : ''),
            eval2: (thedata[13] ? thedata[13] : ''),
//            jenis_arahan: (thedata[26] ? thedata[26] : '')

//            status_idx: idx,
//            id_paket: id_paket,
//            id_flow: (thedata[3 + idx] ? thedata[3 + idx] : ''),
//            nama_paket: (thedata[1] ? thedata[1] : ''),
//            kode_satker: (thedata[7] ? thedata[7] : ''),
//            status_kewenangan: (thedata[12] ? thedata[12] : ''),
//            modeform: 'tambah',
            nmsatker_pengusul: (thedata[24] ? thedata[24] : ''),
//            nama_satker: (thedata[8] ? thedata[8] : ''),
//            keterangan: (thedata[17 + 2 * idx] ? thedata[17 + 2 * idx] : ''),
//            evaluasi: (thedata[20] ? thedata[20] : ''),
//            rc_ded: (thedata[14] ? thedata[14] : ''),
//            rc_fs: (thedata[13] ? thedata[13] : ''),
//            rc_lahan: (thedata[15] ? thedata[15] : ''),
//            rc_dokling: (thedata[16] ? thedata[16] : ''),
//            rkakl_volume: (thedata[21] ? thedata[21] : ''),
//            rkakl_biaya: (thedata[22] ? thedata[22] : ''),
//            kddisposisi: (thedata[23] ? thedata[23] : '0')


        }

//        console.log(waydata);

        way.set('formData', waydata);


        $('#modalTitle').text('Verifikasi');

        console.log("======ready to show form");
        console.log("role yang aktif saat ini: " + role);
        console.log("role alias yang aktif saat ini: " + roledesc);
        console.log("role yang harusnya melakukan approval: " + roleberhak);
        console.log('kode satker pengusul: ' + waydata.kode_satker);
        console.log('kode satker user saat ini: ' + user_satker);

        //verifikasi hanya bisa dilakukan oleh orang yang:
        //- rolenya sesuai dengan yang diminta
        //- role pusat, atau role yang satkernya sama dengan satker tempat usulan ini dibuat
        // (role = roleberhak) and (waydata.kode_satker = user_satker)
        // (role = roleberhak) and (user_satker in pemrog, adps, kpsj, konstruksi, pavement, structures, metrokobes, jbh, mejd, tanah, gml, lkj, kompetensi, sditjen, buk, ditbang, ditpreservasi, ditjembatan, ditjbh, pjj
        var rolePusat = ['pemrog', 'adps', 'kpsj', 'konstruksi', 'pavement', 'structures', 'metrokobes', 'jbh', 'mejd', 'tanah', 'gml', 'lkj', 'kompetensi', 'sditjen', 'buk', 'ditbang', 'ditpreservasi', 'ditjembatan', 'ditjbh', 'pjj'];

        console.log('child satker: ' + child_satker);
        var aChildSatker = child_satker.replace(/'/g, "").split(',');
        console.log(aChildSatker);

        if (role == roleberhak) {
            if ((waydata.kode_satker == user_satker) && (!isEmpty(user_satker))) {
                //boleh approve
                console.log('boleh aprove, role cocok, satker sama');
                $('#modal-edit').modal('show');
                $('.elem-eval_usulan1').show();
                $('.elem-eval_usulan2').hide();
            } else if (aChildSatker.indexOf(waydata.kode_satker) >= 0) {
                //jika user_satker yang sekarang login , punya child yang salah satunya adalah satker pengusul
                console.log('boleh aprove, role cocok, satker seinduk');
                $('#modal-edit').modal('show');
                $('.elem-eval_usulan1').show();
                $('.elem-eval_usulan2').hide();
            } else {
                if (rolePusat.indexOf(roledesc) >= 0) {
                    //boleh approve
                    console.log('boleh aprove, role cocok, oleh satker pusat');
                    $('#modal-edit').modal('show');
                } else {
                    console.log('gak boleh approve karena meskipun role sama, tapi beda satker');
                    alert('Verifikasi tidak bisa dilakukan (satker tidak sesuai!)');
                }
            }
        } else {
            console.log('gak boleh approve karena rolenya beda');
            alert('Verifikasi tidak bisa dilakukan (role tidak sesuai!)');
        }

    }

    //*
    function dtTagging(strTag) {
        // alert(strTag);

        //console.log(strTag);

        var str = strTag.split("::");

        var id = str[1];
        //console.log(id);
        var label = str[0] + '|' + str[1];
        var rujukan = str[0];

        //console.log(label);



//        console.log(id);
//        console.log(label);
//        console.log(rujukan);
        var elt = $('.div-tags');
        elt.tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to set id of tag
            itemText: 'text' // this will be used to set text of tag
        });

        elt.tagsinput('add', {value: id, text: label, rujukan: rujukan});
    }
    //*/


    function handleKegiatan(el)
    {
        //refreshCombobox4('kd_output', 30, 'kdgiat::thang', el.value + '::' + $('#thang').val());
        refreshCombobox('kd_output', 30, 'kdgiat', el.value);
        $('#kd_sub_output').empty();
        $('#kd_sub_output').append(new Option("--Pilih--", -1));
        $('#kd_komponen').empty();
        $('#kd_komponen').append(new Option("--Pilih--", -1));

    }

    function handleJnsBelanja(el)
    {
        refreshCombobox('kdakun', 37, 'kdgbkpk', el.value);
        
        refreshCombobox('ykdakun', 37, 'kdgbkpk', el.value);
    }

    function handleSoutput2(el) {
        var kdgiat = $('#kd_kegiatan-sel').val();
        var kdoutput = $('#kd_output-sel').val();
        var kdsoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput + "::" + $('#thang-sel').val();
        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
        refreshCombobox4('kd_komponen-sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);
    }

    function handleSoutput(el) {
        var kdgiat = $('#kd_kegiatan').val();
        var kdoutput = $('#kd_output').val();
        var kdsoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput + "::" + $('#thang').val();
        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
        //refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput::thang', valSelect);
        //alert(valSelect);
        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);
        
        
        
        var ykdgiat = $('#ykd_kegiatan').val();
        var ykdoutput = $('#ykd_output').val();
        var ykdsoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput + "::" + $('#thang').val();
        var yvalSelect = ykdgiat + "::" + ykdoutput + "::" + ykdsoutput;
        //refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput::thang', valSelect);
        //alert(valSelect);
        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', yvalSelect);
        
       
    }

//    function handleKomponen(el) {
//        var kdkmpnen = el.value;
//        var thang = yearnow;
//
//        //if($('#kd_kegiatan-sel').val() === '2409'){
//        //setInputVal3('hargasat', 50, 'kdkmpnen::thang', kdkmpnen + '::' + thang);
////        }
//
//
//
//    }

    function handleOutput(el) {
    
        var kdgiat = $('#kd_kegiatan').val();
        var kdoutput = el.value;
        
        //var valSelect = kdgiat + "::" + kdoutput + "::" + $('#thang').val();
        var valSelect = kdgiat + "::" + kdoutput;
        // refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput::thang', valSelect);
        refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);


//        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
//        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

        var selectedText = el.options[el.selectedIndex].text;

        //alert(selectedText);


        var x = function () {
            var defer = $.Deferred();

//            var vreff = kdgiat + "::" + kdoutput;
            //console.log('a() called');
//            setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);



            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        var y = function () {
            var defer = $.Deferred();

            //alert();
            //var sat = $('#satuan').val();
           

            if (kdoutput == '001' || kdoutput == '002'|| kdoutput == '003'|| kdoutput == '004'|| kdoutput == '005'|| kdoutput == '006'|| kdoutput == '007'|| kdoutput == '008'|| kdoutput == '009'|| kdoutput == '010'|| kdoutput == '011'|| kdoutput == '012'|| kdoutput == '013')
            {
                $('.divNonFisik').hide();
                $('#detail').prop('disabled', true);

                $('.divRuas').show();
                $("#xid_ruas").prop('disabled', false);
                $("#id_ruas").prop('disabled', false);
                $("#sta_awal").prop('disabled', false);
                $("#sta_akhir").prop('disabled', false);
                $("#treatment").prop('disabled', false);

                $("#xid_jembatan").prop('disabled', false);
                $("#longitude").prop('disabled', false);
                $("#latitude").prop('disabled', false);

            } else
            {
                $('.divNonFisik').show();
                $('#detail').prop('disabled', false);

                $('.divRuas').hide();
                $("#xid_ruas").prop('disabled', true);
                $("#sta_awal").prop('disabled', true);
                $("#sta_akhir").prop('disabled', true);
                $("#treatment").prop('disabled', true);

                $("#xid_jembatan").prop('disabled', true);
                $("#longitude").prop('disabled', true);
                $("#latitude").prop('disabled', true);


            }

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        x().then(y);



//        alert(sat);


    }

    function handleOutput2(el) {

        var kdgiat = $('#kd_kegiatan-sel').val();
        var kdoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + $('#thang-sel').val();
        var valSelect = kdgiat + "::" + kdoutput;
        refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect);


//        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
//        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

        var selectedText = el.options[el.selectedIndex].text;

        //alert(selectedText);


        var x = function () {
            var defer = $.Deferred();

//            var vreff = kdgiat + "::" + kdoutput;
//            //console.log('a() called');
//            setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);



            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        var y = function () {
            var defer = $.Deferred();

            //alert();
            var sat = $('#satuan').val();

            if (sat == 'km' || sat == 'm')
            {
                $('.divNonFisik').hide();
                $('#detail').prop('disabled', true);

                $('.divRuas').show();
                $("#xid_ruas").prop('disabled', false);
                $("#sta_awal").prop('disabled', false);
                $("#sta_akhir").prop('disabled', false);
                $("#treatment").prop('disabled', false);

                $("#xid_jembatan").prop('disabled', false);
                $("#longitude").prop('disabled', false);
                $("#latitude").prop('disabled', false);

            } else
            {
                $('.divNonFisik').show();
                $('#detail').prop('disabled', false);

                $('.divRuas').hide();
                $("#xid_ruas").prop('disabled', true);
                $("#sta_awal").prop('disabled', true);
                $("#sta_akhir").prop('disabled', true);
                $("#treatment").prop('disabled', true);

                $("#xid_jembatan").prop('disabled', true);
                $("#longitude").prop('disabled', true);
                $("#latitude").prop('disabled', true);


            }

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        x().then(y);



//        alert(sat);


    }

    function handleRuas(el)
    {
        refreshCombobox2('xid_jembatan', 32, 'linkid', el.value);
        //modal tambah
        refreshCombobox3('sta_awal', 35, 'kode_ruas', el.value);
        refreshCombobox3('sta_akhir', 35, 'kode_ruas', el.value);
        
        //modal edit detail
        refreshCombobox2('id_jembatan', 32, 'linkid', el.value);
        refreshCombobox3('sta_awal-sel', 35, 'kode_ruas', el.value);
        refreshCombobox3('sta_akhir-sel', 35, 'kode_ruas', el.value);
        
        
         //modal edit detail
        
        //alert("wsta awal - akhir")
        
        refreshCombobox2('wid_jembatan', 32, 'linkid', el.value);
        refreshCombobox3('wsta_awal', 35, 'kode_ruas', el.value);
        refreshCombobox3('wsta_akhir', 35, 'kode_ruas', el.value);
        
        if($("#xid_ruas").val()!=""){
            $("#detail").val($("#id_ruas").val());
        }
        if($("#xid_jembatan").val() != ""){
            $("#xdetail").val($("#id_ruas").val());
        }

//        var tirmsv3_wp = $('#tirmsv3_wp').DataTable();
//        var trams_wp = $('#trams_wp').DataTable();
//        tirmsv3_wp.ajax.reload();
//        trams_wp.ajax.reload();
        //console.log(result);
    }

    function handleJembatan(el)
    {
        setInputVal2('longitude', 40, 'id_jembatan', el.value);
        setInputVal2('latitude', 41, 'id_jembatan', el.value);
    }

    function dtEditRowDetail(id) {

        $(".decformat2").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

            var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");



            $('#hargasat').val(decform);

            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();

            var hasil = volume * hargasat;

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val(hasil);



        });

        $(".decformat").change(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

        });


        $(".decformat2").select(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });
        });


        $('#volume').on("focus", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            //$('#rm').val($(".decformat").val());
            $('#rm').val(hasil);
        });

        $('#volume').on("keyup", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val($(".decformat").val());
            //$('#rm').val(hasil);
        });


        $('#thang-sel').prop('disabled', true);
        // $('#jns_giat').prop('disabled', true);
        $('#kd_kegiatan-sel').prop('disabled', true);
        // $('#kd_output-sel').prop('disabled', true);
        // $('#kd_sub_output-sel').prop('disabled', true);
        //  $('#kd_komponen-sel').prop('disabled', true);
        //  $('#kd_sub_komponen-sel').prop('disabled', true);
        //  $('#nama_sub_komponen-sel').prop('disabled', true);

        $('.div-tags').tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
        });


        //$('#frm-detail-edit')[0].reset();

        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // c


        $('#modeform').val('edit_detail');
        $('#modal-detail').modal('show'); // show bootstrap modal when complete loaded
        $('.tbhItem').text('Edit Detail Paket');


        $('.rujuk').hide();// Set title to Bootstrap modal title



        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/detail/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {
                // console.log(data.kd_komponen);
                $('#id_paket').val(data.id_paket);
                $('#id_usulan').val(id);
                $('#thang-sel').val(data.thang);
                $('#id_ppk-sel').val(data.id_ppk);
                $('#kdkppn-sel').val(data.kdkppn);
                $('[name="thang-sel"]').val(data.thang);
                $('[name="id_ppk-sel"]').val(data.id_ppk);
                $('[name="kdkppn-sel"]').val(data.kdkppn);
                $('#kd_sub_komponen-sel').val(data.kd_sub_komponen);
                $('#nama_sub_komponen-sel').val(data.nama_sub_komponen);
                $('[name="kd_sub_komponen-sel"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen-sel"]').val(data.nama_sub_komponen);
                $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);
                console.log(data);
                console.log("-----");
                // $('[name="kdgbkpk"]').val(data.kdgbkpk);
                var jmlmath = Math.round(data.jumlah);
                var rjml = jmlmath + " ";

                var hsmath = Math.round(data.hargasat);
                var rhs = hsmath + " ";


                $('[name="volume"]').val(data.volume);
                $('[name="satuan"]').val(data.satuan);
                $('[name="hargasat"]').val(parseInt(rhs));
                $('[name="totalpagu"]').val(parseInt(rjml));
                $('[name="treatment"]').val(data.treatment);
                $('[name="xid_ruas"]').val(data.no_ruas);
                $('[name="latitude"]').val(data.latitude);
                $('[name="longitude"]').val(data.longitude);
                $('[name="rm"]').val(data.rpm);


                updateCombobox('kd_kegiatan-sel', 5, data.kd_kegiatan);

                if (role == 7) {
                    $('#jns_giat').val('NF');
                    $('.rujuk').hide();
                    $('.divNonFisik').show();
                    $('#detail').prop('disabled', false);
                    $('.divRuas').hide();
                    $("#xid_ruas").prop('disabled', true);
                    $("#sta_awal").prop('disabled', true);
                    $("#sta_akhir").prop('disabled', true);
                    $("#treatment").prop('disabled', true);
                    $("#xid_jembatan").prop('disabled', true);
                    $("#longitude").prop('disabled', true);
                    $("#latitude").prop('disabled', true);
                } else if (role == 3) {
                    $('#jns_giat').val('F');
                    $('.rujuk').show();
                    $('.divNonFisik').hide();
                    $('#detail').prop('disabled', true);
                    $('.divRuas').show();
                    $("#xid_ruas").prop('disabled', false);
                    $("#sta_awal").prop('disabled', false);
                    $("#sta_akhir").prop('disabled', false);
                    $("#treatment").prop('disabled', false);
                    $("#xid_jembatan").prop('disabled', false);
                    $("#longitude").prop('disabled', false);
                    $("#latitude").prop('disabled', false);

                }


                if (data.kd_kegiatan == '2409')
                {
                    $('#jns_giat').val('F');
                    $('.rujuk').show();
                    $('.divNonFisik').hide();
                    $('#detail').prop('disabled', true);
                    $('.divRuas').show();
                    $("#xid_ruas").prop('disabled', false);
                    $("#sta_awal").prop('disabled', false);
                    $("#sta_akhir").prop('disabled', false);
                    $("#treatment").prop('disabled', false);
                    $("#xid_jembatan").prop('disabled', false);
                    $("#longitude").prop('disabled', false);
                    $("#latitude").prop('disabled', false);
                } else {
                    $('#jns_giat').val('NF');
                    $('.rujuk').hide();
                    $('.divNonFisik').show();
                    $('#detail').prop('disabled', false);
                    $('.divRuas').hide();
                    $("#xid_ruas").prop('disabled', true);
                    $("#sta_awal").prop('disabled', true);
                    $("#sta_akhir").prop('disabled', true);
                    $("#treatment").prop('disabled', true);
                    $("#xid_jembatan").prop('disabled', true);
                    $("#longitude").prop('disabled', true);
                    $("#latitude").prop('disabled', true);
                }







                var j = function () {
                    var defer = $.Deferred();
                    updateCombobox('kdgbkpk', 36, data.kdgbkpk);

                    refreshComboboxOutput('kd_output-sel', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);
                    refreshComboboxOutput('kdakun', 37, 'kdgbkpk', data.kdgbkpk, data.kdakun);






                    //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
                    //initCombobox('kd_kegiatan-sel', 5);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                var a = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);

//                      var tirmsv3_wp = $('#tirmsv3_wp').DataTable();
//                    var trams_wp = $('#trams_wp').DataTable();
//                    tirmsv3_wp.ajax.reload();
//                    trams_wp.ajax.reload();
//                    
                    //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                var b = function () {
                    var defer = $.Deferred();
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen-sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);

                    //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                var c = function () {
                    var defer = $.Deferred();
                    //console.log('a() called');
                    var tlist_detail = $('#tlist_detail').DataTable();
                    tlist_detail.ajax.reload();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };

                var g = function () {
                    var defer = $.Deferred();

                    // alert(data.id_jembatan);
//                    alert(data.sta_akhir);
                    //refreshComboboxOutput('id_jembatan', 32, 'linkid', ij,data.id_jembatan);
                    refreshComboboxJBT('xid_jembatan', 32, 'linkid', data.no_ruas, data.id_jembatan);

                    refreshComboboxSTA('sta_awal-sel', 35, 'kode_ruas', data.no_ruas, data.sta_awal);
                    refreshComboboxSTA('sta_akhir-sel', 35, 'kode_ruas', data.no_ruas, data.sta_akhir);

                    //$('[name="id_jembatan"]').val(data.longitude);




                    $(".decformat").val(rjml.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

                    $(".decformat2").val(rhs.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));
//                    var rm = data.rpm;
//                    var pln = data.phln;
//                    var rmp = data.rmp;
//                    var sbsn = data.sbsn;
//                    
//                    addFormGroup();
//                    selectFormGroup();





                    //console.log('a() called');

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                //$("#id_jembatan").val(data.id_jembatan);
//                        var i = function () {
//                        var defer = $.Deferred();
//                                //console.log('a() called');
//                                //alert()
//                               
//
//                                    setTimeout(function () {
//                                    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                                    }, 1000);
//                                return defer;
//                        };




                var pbar = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $(".overlay").show();

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };
                var da = get_prov();

                $("#prov").append("<option value=" + da.kd_prov_irmsv3 + ">" + da.nama_prov + "</option>")
                // alert(da.kd_prov_irmsv3);
                refreshComboboxOutput('kabkot', 49, 'kd_prov_irmsv3', da.kd_prov_irmsv3, data.kdkabkota);
                //  refreshCombobox('kabkot', 49, 'kd_prov_irmsv3', da.kd_prov_irmsv3);
                // $("#prov").val(data.kdlokasi);
                //  $("#kabkot").val(data.kdkabkota);

                $.when(
                        pbar().then(j).then(a).then(b).then(g).then(c)
                        // Deferred object (probably Ajax request),
                        ).then(function () {
                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
//                    $('#modal-load').modal('toggle');
                });
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }
        });
    }

    function dtEditRowPaket(id) {
        var id_paket=id;
        //alert(id_paket);
        var data_selected = xhrdata.data.filter(x => x[1] == id_paket)[0];
        //alert(data_selected[2]);
        refreshComboboxOutput('zthang', 21, 'thang',data_selected[2],data_selected[2]);
        
        updateComboboxAndSelected('zkd_isu', 3,data_selected[27]);
        
       // updateComboboxAndSelected('zwps_kode', 3,data_selected[27]);
        
        refreshComboboxOutput('zkd_kegiatan', 5, 'kdgiat',data_selected[10],data_selected[10]);
        
        setTimeout(set_kegiatan, 8000);//set kegiatan berdasarkan kode
        
        $("#zkd_sub_komponen").val(data_selected[25]);
        
        $("#znama_sub_komponen").val(data_selected[26]);
        $("#zid_paket").val(id);
        
        
        var id_rprov=$("#id_rprov").val();
        refreshComboboxOutput('zprov', 60, 'kd_prov',id_rprov,id_rprov);
        
        refreshComboboxOutput('zkd_output', 30, 'kdgiat',data_selected[10],data_selected[19]);
    
        refreshComboboxOutput('zkd_sub_output', 44, 'kdoutput',data_selected[19],data_selected[20]);
        
        refreshComboboxOutput('zkd_komponen', 18, 'kdsoutput',data_selected[20],data_selected[24]);
        
        updateComboboxAndSelected('zjnskontrak',23,data_selected[28]);
        
        updateComboboxAndSelected('zkdkppn',9,data_selected[29]);
        
        updateComboboxAndSelected('zid_ppk',11,data_selected[30]);
        
        updateComboboxAndSelected('zwps_kode',57,data_selected[32]);
        
        updateComboboxAndSelected('zkdgbkpk',36,data_selected[37]);
        
        //updateComboboxAndSelected('zid_ppk',11,data_selected[30]);
        
        //refreshComboboxOutput('ykdgbkpk', 36, 'kdgbkpk',data_selected[20],data_selected[20]);
        //initCombobox("zkdgbkpk",36);
        
        initCombobox("zkdakun",37);
        
        $("#zrc_ded_status").append("<option selected value="+data_selected[33]+">"+data_selected[33]+"</option>");
        $("#zrc_fs_status").append("<option selected "+data_selected[34]+">"+data_selected[34]+"</option>");
        $("#zrc_lahan_status").append("<option selected "+data_selected[35]+">"+data_selected[35]+"</option>");
        $("#zrc_doklin_status").append("<option selected "+data_selected[36]+">"+data_selected[36]+"</option>");
        
        updateComboboxAndSelected('zkws_kode',57,data_selected[31]);
       // alert( $('#yjns_giat option:eq(1)').val());
       //alert($("#ykd_kegiatan-sel").val());
        
        setTimeout(set_kegiatan, 8000);//
       
        
        $("#modal-edit-paket").modal("show");
      
    }

     function dtEditRowPaket2(id) {

//        $('#frm-paket')[0].reset();

        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // c


        $('#modeform').val('edit_paket');
        $('#modal-tambah').modal('show'); // show bootstrap modal when complete loaded
        $('.tbhPaketDetail').text('Edit Paket');// Set title to Bootstrap modal title
//        $('div#block-ct + div.modal-footer').remove();

        $('#blockItmPaket').hide();
//        $('[name="thang"]').prop('disabled', false);
//        $('[name="kd_kegiatan"]').prop('disabled', false);
//        $('[name="kd_output"]').prop('disabled', false);
//        $('[name="kd_sub_output"]').prop('disabled', false);
//        $('[name="kd_komponen"]').prop('disabled', false);
//        $('[name="kd_sub_komponen"]').prop('disabled', false);
//        $('[name="nama_sub_komponen"]').prop('disabled', false);
//        $('[name="id_ppk"]').prop('disabled', false);
//        $('[name="kdkppn"]').prop('disabled', false);


//        $("<div class='modal-footer'><button class='btn btn-sm btn-default' type='button' data-bs-dismiss='modal'>Tutup</button><button class='btn btn-sm btn-success' type='button' onclick='simpanFormPaket()'><i class='fa fa-check'></i>Simpan</button></div> ").insertAfter("#block-ct");




        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {
                var x = $("#wps_kode").val();
                // alert(x);
                $.ajax({
                    //type: "GET",
                    url: "<?php echo base_url('pagu_indikatif/get_kws/') ?>" + data.wps_kode,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (dat) {
                        //console.log("--ajax data--");
                        console.log(data)

                        x = dat;
                        lookkws(x)

                    },
                    failure: function (errMsg) {
                        alert(errMsg);
                    }
                });
                $.ajax({
                    //type: "GET",
                    url: "<?php echo base_url('pagu_indikatif/get_sub/') ?>" + data.kws_kode,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                        //console.log("--ajax data--");
                        console.log(data)

                        x = data;
                        looksub(x)

                    },
                    failure: function (errMsg) {
                        alert(errMsg);
                    }
                });

                console.log(data);
                console.log("----------");
                //alert(data.jnskontrak);

                $('[name="id_paket"]').val(data.id_paket);
                $('[name="thang"]').val(data.thang);
                updateCombobox('kd_isu', 3, data.kd_isu);
                updateCombobox('kd_kegiatan', 5, data.kd_kegiatan);
                //updateCombobox('jnskontrak', 23, data.jnskontrak);

                // $('[name="kd_output"]').val(data.kd_output);
                var j = function () {
                    var defer = $.Deferred();

                    refreshComboboxOutput('kd_output', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);

                    //console.log('a() called');
                    //refreshCombobox('kd_kegiatan', 5, 'thang', data.thang);
                    //initCombobox('kd_kegiatan', 5);


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };

                var a = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);

                    //console.log('a() called');
//                    $('[name="kd_kegiatan"]').val(data.kd_kegiatan);
//                    refreshCombobox('kd_output', 30, 'kdgiat', data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };

                var b = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);

                    //console.log('a() called');
                    //$('[name="kd_output"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };



//                var c = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    updateCombobox('jnskontrak', 23, data.jnskontrak);
//                    //refreshCombobox('jnskontrak', 23, 'thang', data.jnskontrak);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 500);
//
//                    return defer;
//                };

//                var d = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    //$('[name="kd_komponen"]').val(data.kd_komponen);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 500);
//
//                    return defer;
//                };

//                var e = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    var tlist_detail = $('#tlist_detail').DataTable();
//                    tlist_detail.ajax.reload();
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };

//                var f = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
//                    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };
//
//                var g = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    $('[name="kd_sub_output"]').val(data.kd_sub_output);
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };

                $('[name="jnskontrak"]').val(data.jnskontrak);
                $('[name="id_ppk"]').val(data.id_ppk);
                $('[name="kdkppn"]').val(data.kdkppn);
                $('[name="kd_isu"]').val(data.kd_isu);
                $('[name="wps_kode"]').val(data.wps_kode);
                $('[name="kws_kode"]').val(data.kws_kode);
                $('[name="subkw"]').val(data.subkawasan_nama);
                $('[name="rc_ded_status"]').val(data.rc_ded_status);
                $('[name="rc_fs_status"]').val(data.rc_fs_status);
                $('[name="rc_lahan_status"]').val(data.rc_lahan_status);
                $('[name="rc_doklin_status"]').val(data.rc_doklin_status);



//                var i = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    //alert();
//                    var sat = $('#satuan').val();
//
//                    if (sat == 'km' || sat == 'm')
//                    {
//                        $('.divNonFisik').hide();
//                        $('#detail').prop('disabled', true);
//
//                        $('.divRuas').show();
//                        $("#id_ruas").prop('disabled', false);
//                        $("#sta_awal").prop('disabled', false);
//                        $("#sta_akhir").prop('disabled', false);
//                        $("#treatment").prop('disabled', false);
//
//                        $("#id_jembatan").prop('disabled', false);
//                        $("#longitude").prop('disabled', false);
//                        $("#latitude").prop('disabled', false);
//
//                    } else
//                    {
//                        $('.divNonFisik').show();
//                        $('#detail').prop('disabled', false);
//
//                        $('.divRuas').hide();
//                        $("#id_ruas").prop('disabled', true);
//                        $("#sta_awal").prop('disabled', true);
//                        $("#sta_akhir").prop('disabled', true);
//                        $("#treatment").prop('disabled', true);
//
//                        $("#id_jembatan").prop('disabled', true);
//                        $("#longitude").prop('disabled', true);
//                        $("#latitude").prop('disabled', true);
//
//                    }
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };


                var pbar = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $(".overlay").show();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;


                };



                $.when(
                        //pbar().then(j).then(a).then(b).then(f).then(g).then(c).then(d).then(e)
                        pbar().then(j).then(a).then(b)
                        // Deferred object (probably Ajax request),

                        // Deferred object (probably Ajax request),

                        // Deferred object (probably Ajax request)

                        ).then(function () {


                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
//                    alert('Selesai Menyiapkan Data');

                });





                $('[name="kd_sub_komponen"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen"]').val(data.nama_sub_komponen);

                //$('#archive-preview div').detach();
//                $('#archive-preview').show(); // show photo preview modal
//                //
//
//                if (data.archive)
//                {
//
//                    $('#label-archive').text('Ubah File'); // label photo upload
//                    $('#archive-preview div').html('<input type="checkbox" id="remove_archive" name="remove_archive" value="' + data.archive + '"/>&nbsp;Hapus file saat save<br><br>'); // remove photo
//
//                } else
//                {
//                    $('#label-archive').text('Unggah File'); // label photo upload
//                    $('#archive-preview div').text('(File Tidak Tersedia)');
//                }

            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }
        });
    }

    function dtEditRow(id) {

        //alert(id);

        $('#frm-detail')[0].reset();

        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // c


        //$('#modeform').val('edit');
        $('#modal-tambah').modal('show'); // show bootstrap modal when complete loaded
        $('.tbhPaketDetail').text('Tambah Detail Paket');// Set title to Bootstrap modal title
        $('#blockItmPaket').show();

//        $('[name="thang"]').prop('readonly', true);
//        $('[name="kd_kegiatan"]').prop('readonly', true);
//        $('[name="kd_output"]').prop('readonly', true);
//        $('[name="kd_sub_output"]').prop('readonly', true);
//        $('[name="kd_komponen"]').prop('readonly', true);
//        $('[name="kd_sub_komponen"]').prop('readonly', true);
//        $('[name="nama_sub_komponen"]').prop('readonly', true);
//        $('[name="id_ppk"]').prop('readonly', true);
//        $('[name="kdkppn"]').prop('readonly', true);


        $('div#block-ct + div.modal-footer').remove();

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {

//                alert(data.kd_kegiatan);

                $('[name="id_paket"]').val(data.id_paket);
                $('[name="thang"]').val(data.thang);

                // $('[name="kd_output"]').val(data.kd_output);
                var j = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    initCombobox('kd_kegiatan', 5);



                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var a = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_kegiatan"]').val(data.kd_kegiatan);
                    refreshCombobox('kd_output', 30, 'kdgiat', data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var b = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_output"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };



                var c = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var d = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_komponen"]').val(data.kd_komponen);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var e = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    var tlist_detail = $('#tlist_detail').DataTable();
                    tlist_detail.ajax.reload();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                var f = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                var g = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_sub_output"]').val(data.kd_sub_output);


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                var i = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    //alert();
                    var sat = $('#satuan').val();

                    if (sat == 'km' || sat == 'm')
                    {
                        $('.divNonFisik').hide();
                        $('#detail').prop('disabled', true);

                        $('.divRuas').show();
                        $("#xid_ruas").prop('disabled', false);
                        $("#sta_awal").prop('disabled', false);
                        $("#sta_akhir").prop('disabled', false);
                        $("#treatment").prop('disabled', false);

                        $("#xid_jembatan").prop('disabled', false);
                        $("#longitude").prop('disabled', false);
                        $("#latitude").prop('disabled', false);

                    } else
                    {
                        $('.divNonFisik').show();
                        $('#detail').prop('disabled', false);

                        $('.divRuas').hide();
                        $("#xid_ruas").prop('disabled', true);
                        $("#sta_awal").prop('disabled', true);
                        $("#sta_akhir").prop('disabled', true);
                        $("#treatment").prop('disabled', true);

                        $("#xid_jembatan").prop('disabled', true);
                        $("#longitude").prop('disabled', true);
                        $("#latitude").prop('disabled', true);

                    }

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                j().then(a).then(b).then(f).then(g).then(c).then(d).then(e).then(i);



                $('[name="kd_sub_komponen"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen"]').val(data.nama_sub_komponen);

                //$('#archive-preview div').detach();
                $('#archive-preview').show(); // show photo preview modal
                //

                if (data.archive)
                {

                    $('#label-archive').text('Ubah File'); // label photo upload
                    $('#archive-preview div').html('<input type="checkbox" id="remove_archive" name="remove_archive" value="' + data.archive + '"/>&nbsp;Hapus file saat save<br><br>'); // remove photo

                } else
                {
                    $('#label-archive').text('Unggah File'); // label photo upload
                    $('#archive-preview div').text('(File Tidak Tersedia)');
                }

            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }
        });
    }

    function dtHistory($db, id) {
        //console.log(id);
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/history/" + id + "/" + $db,
            type: "GET",
//            data: {id:id},
            dataType: "html",
            success: function (data)
            {
                $('#content-history').html(data);
                $('#modal-history').modal('show');
                $('.block-title').text('History Paket');// Set title to Bootstrap modal title
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }

        });
    }

    function dtTambahRowPaket() {
        var kd_kegiatan = $("#kd_kegiatan").val();
        //alert(role);
        if (role == 7) {
            $('#jns_giat').val('NF');
            $('.rujuk').hide();
            $('.divNonFisik').show();
            $('#detail').prop('disabled', false);
            $('.divRuas').hide();
            $("#xid_ruas").prop('disabled', true);
            $("#sta_awal").prop('disabled', true);
            $("#sta_akhir").prop('disabled', true);
            $("#treatment").prop('disabled', true);
            $("#xid_jembatan").prop('disabled', true);
            $("#longitude").prop('disabled', true);
            $("#latitude").prop('disabled', true);
        } else if (role == 3) {
            $('#jns_giat').val('F');
            $('.rujuk').show();
            $('.divNonFisik').css("display", "none");
            $('#detail').prop('disabled', true);
            $('.divRuas').css("display", "");
            $("#xid_ruas").prop('disabled', false);
            $("#sta_awal").prop('disabled', false);
            $("#sta_akhir").prop('disabled', false);
            $("#treatment").prop('disabled', false);
            $("#xid_jembatan").prop('disabled', false);
            $("#longitude").prop('disabled', false);
            $("#latitude").prop('disabled', false);

        }

        //alert(kd_kegiatan);

//                if (kd_kegiatan == '2409')
//                {
//                    $('#jns_giat').val('F');
//                    $('.rujuk').show();
//                    $('.divNonFisik').hide();
//                    $('#detail').prop('disabled', true);
//                    $('.divRuas').show();
//                    $("#id_ruas").prop('disabled', false);
//                    $("#sta_awal").prop('disabled', false);
//                    $("#sta_akhir").prop('disabled', false);
//                    $("#treatment").prop('disabled', false);
//                    $("#id_jembatan").prop('disabled', false);
//                    $("#longitude").prop('disabled', false);
//                    $("#latitude").prop('disabled', false);
//                } else {
//                    $('#jns_giat').val('NF');
//                    $('.rujuk').hide();
//                    $('.divNonFisik').show();
//                    $('#detail').prop('disabled', false);
//                    $('.divRuas').hide();
//                    $("#id_ruas").prop('disabled', true);
//                    $("#sta_awal").prop('disabled', true);
//                    $("#sta_akhir").prop('disabled', true);
//                    $("#treatment").prop('disabled', true);
//                    $("#id_jembatan").prop('disabled', true);
//                    $("#longitude").prop('disabled', true);
//                    $("#latitude").prop('disabled', true);
//                }

        $(".decformat2").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

            var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");



            $('#hargasat').val(decform);

            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();

            var hasil = volume * hargasat;

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val(hasil);



        });

        $(".decformat").change(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

        });


        $(".decformat2").select(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });
        });


        $('#volume').on("focus", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            //$('#rm').val($(".decformat").val());
            $('#rm').val(hasil);
        });

        $('#volume').on("keyup", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val($(".decformat").val());
            //$('#rm').val(hasil);
        });






//
//        $('#kd_komponen-sel').on("change", function () {
//            var kdkmpnen = $('#kd_komponen-sel').val();
//
//            setInputVal10('hargasat', 50, 'kdkmpnen', kdkmpnen);
//
//
//            var hargasat = $('#hargasat').val();
//            var volume = $('#volume').val();
//////            console.log(hargasat);
//////            console.log(volume);
//            var hasil = volume * hargasat;
//            //console.log('dsjhsf');
//
////            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
//
//            $("#totalpagu").val(hasil);
//            $('#rm').val(hasil);
//        });

//        $('#hargasat').keyup(function (event) {
//                var hargasat = $('#hargasat').val();
//            var volume = $('#volume').val();
//            
////            alert(parseFloat(volume * hargasat));
//            $('#jumlah').val(volume * hargasat);
//
//            // format number
//          
//        });








        //initCombobox('id_ruas', 33);
        initCombobox('kdgbkpk', 36);
       
        //initCombobox('id_satuan', 26);
        //modal tambah 
        multipleSelect('kdsdana', 38);
        
        //modal edit
        multipleSelect('ykdsdana', 38);
        initCombobox('ykdgbkpk', 36);
        initCombobox('treatment', 42);




//        $(".div-tags").tagsinput('removeAll');
        // $('<div class="div-tags"></div>').insertBefore( "#tags" );
        // $('<input type="text" placeholder="">').insertAfter( ".div-tags" );
        //$('.div-tags").tagsinput('add', []);
        $('.div-tags').tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
        });


        $('#modeform').val('tambah_detail');
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
//        $('#modalTitle').text('Tambah Data Detail');
        //$('#modal-detail').modal('show');
        $('#modal-tambah').modal('show');
        $('.tbhItem').text('Tambah Paket Pagu');



//        $('#thang-sel').prop('disabled', true);
//        $('#kd_kegiatan-sel').prop('disabled', true);
//        $('#kd_output-sel').prop('disabled', false);
//        $('#kd_sub_output-sel').prop('disabled', false);
//        $('#kd_komponen-sel').prop('disabled', false);
//        $('#kd_sub_komponen-sel').prop('disabled', true);
//        $('#nama_sub_komponen-sel').prop('disabled', true);
//        $('#id_ppk-sel').prop('disabled', false);
//        $('#kdkppn-sel').prop('disabled', false);





//        if (isFisik == '2409')
//        {
//
//            $('#jns_giat').on('change', function () {
//                var jnsg = $('#jns_giat').val();
//
//                //var jnsg = $('#jns_giat').val();
//
//
//                if (jnsg == 'F')
//                {
//                    $('.rujuk').show();
//                    $('.divNonFisik').hide();
//                    $('#detail').prop('disabled', true);
//                    $('.divRuas').show();
//                    $("#id_ruas").prop('disabled', false);
//                    $("#sta_awal").prop('disabled', false);
//                    $("#sta_akhir").prop('disabled', false);
//                    $("#treatment").prop('disabled', false);
//                    $("#id_jembatan").prop('disabled', false);
//                    $("#longitude").prop('disabled', false);
//                    $("#latitude").prop('disabled', false);
//                } else {
//                    $('.rujuk').hide();
//                    $('.divNonFisik').show();
//                    $('#detail').prop('disabled', false);
//                    $('.divRuas').hide();
//                    $("#id_ruas").prop('disabled', true);
//                    $("#sta_awal").prop('disabled', true);
//                    $("#sta_akhir").prop('disabled', true);
//                    $("#treatment").prop('disabled', true);
//                    $("#id_jembatan").prop('disabled', true);
//                    $("#longitude").prop('disabled', true);
//                    $("#latitude").prop('disabled', true);
//                }
//
//                //alert(jnsg);
//
//
//            });
//
//
//            $('#jns_giat').val('F');
//            $('.rujuk').show();
//            $('.divNonFisik').hide();
//            $('#detail').prop('disabled', true);
//            $('.divRuas').show();
//            $("#id_ruas").prop('disabled', false);
//            $("#sta_awal").prop('disabled', false);
//            $("#sta_akhir").prop('disabled', false);
//            $("#treatment").prop('disabled', false);
//            $("#id_jembatan").prop('disabled', false);
//            $("#longitude").prop('disabled', false);
//            $("#latitude").prop('disabled', false);
//        } else {
//            $('#jns_giat').val('NF');
//            $('#jns_giat').hide();
//            $('.rujuk').hide();
//            $('.divNonFisik').show();
//            $('#detail').prop('disabled', false);
//            $('.divRuas').hide();
//            $("#id_ruas").prop('disabled', true);
//            $("#sta_awal").prop('disabled', true);
//            $("#sta_akhir").prop('disabled', true);
//            $("#treatment").prop('disabled', true);
//            $("#id_jembatan").prop('disabled', true);
//            $("#longitude").prop('disabled', true);
//            $("#latitude").prop('disabled', true);
//        }




//        $.ajax({
//            url: "<?php // echo base_url();  ?>pagu_indikatif/ajax_edit/paket/" + id,
//            type: "GET",
//            dataType: "JSON",
//            success: function (data)
//            {
//
//
//                $('[name="id_paket"]').val(data.id_paket);
//                $('#thang-sel').val(data.thang);
//                $('#id_ppk-sel').val(data.id_ppk);
//                $('#kdkppn-sel').val(data.kdkppn);
//                $('[name="thang-sel"]').val(data.thang);
//                $('[name="id_ppk-sel"]').val(data.id_ppk);
//                $('[name="kdkppn-sel"]').val(data.kdkppn);
//                $('#kd_sub_komponen-sel').val(data.kd_sub_komponen);
//                $('#nama_sub_komponen-sel').val(data.nama_sub_komponen);
//                $('[name="kd_sub_komponen-sel"]').val(data.kd_sub_komponen);
//                $('[name="nama_sub_komponen-sel"]').val(data.nama_sub_komponen);
//
//
//
//
//                updateCombobox('kd_kegiatan-sel', 5, data.kd_kegiatan);
//
//                if (data.kd_kegiatan == '2409')
//                {
//                    $('#jns_giat').val('F');
//
//                } else
//                {
//                    $('#jns_giat').val('NF');
//
//                }
//
//
//                $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);
//
//
//
//
//
//                var j = function () {
//                    var defer = $.Deferred();
//
//
//
//
//                    refreshComboboxOutput('kd_output-sel', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);
//
//                    //console.log('a() called');
////                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
//                    //initCombobox('kd_kegiatan-sel', 5);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//                var a = function () {
//                    var defer = $.Deferred();
//
//                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
//                    refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);
//                    //console.log('a() called');
////                      updateCombobox('kd_output-sel', 30, data.kd_output);
////                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
////                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//                var b = function () {
//                    var defer = $.Deferred();
//                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
//                    refreshCombobox4('kd_komponen-sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);
//
//
//
//                    //console.log('a() called');
////                    $('#kd_output-sel').val(data.kd_output);
////                    $('[name="kd_output-sel"]').val(data.kd_output);
////                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
////                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//                var c = function () {
//                    var defer = $.Deferred();
//                    setInputVal10('hargasat', 50, 'kdkmpnen', data.kd_komponen);
//
//
//
//
//
//
//
//                    //console.log('a() called');
//                    var tlist_detail = $('#tlist_detail').DataTable();
//                    tlist_detail.ajax.reload();
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//
//                var d = function () {
//                    var defer = $.Deferred();
//                    //console.log('a() called');$(".decformat2").val($('#hargasat').val());
//                    //$(".decformat2").val($('#hargasat').val());
//
//                    $(".decformat2").trigger("select");
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
////                        var i = function () {
////                        var defer = $.Deferred();
////                                //console.log('a() called');
////                                //alert()
////                               
////
////                                    setTimeout(function () {
////                                    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
////                                    }, 1000);
////                                return defer;
////                        };
//
//
//
//
//                var pbar = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    $(".overlay").show();
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };
//
//
//
//                $.when(
//                        pbar().then(j).then(a).then(b).then(c).then(d)
//                        // Deferred object (probably Ajax request),
//                        ).then(function () {
//                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
////                    $('#modal-load').modal('toggle');
//                });
//            },
//            error: function (jqXHR, textStatus, errorThrown)
//            {
//                alert('Error Tambah Item Paket');
//            }
//        });
//        $("#prov").empty();
//        var data = get_prov();
//        //  alert(data.nama_prov);
//        $("#prov").append("<option value=" + data.kd_prov_irmsv3 + ">" + data.nama_prov + "</option>")
//        refreshCombobox('kabkot', 49, 'kd_prov_irmsv3', data.kd_prov_irmsv3);
//    




        //        console.log('nambah row');
//
//        $('#frm-paket')[0].reset();
//        $('#modeform').val('tambah_paket');
//        $('.form-group').removeClass('has-error'); // clear error class
//        $('.help-block').empty(); // clear error string
//        $('#modal-tambah').modal('show');
//        $('.tbhPaketDetail').text('Tambah Paket');
//
//
//        initCombobox('jnskontrak', 23);
//
//        if (role == 3)
//        {
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//        }




//        $("#kd_kegiatan").change(function () {
//            var kdgiat = $('#kd_kegiatan').val();
//            var kdkmpnen = $('#kd_komponen').val();
//            var thang = yearnow;
//
//
//
//
////            if (kdgiat == '2409') {
////                setInputVal3('hargasat', 50, 'kdkmpnen::thang', kdkmpnen + '::' + thang);
////            }
//
//
//        });



        //$('#modalTitle').text('Tambah Paket');
//        $('[name="thang"]').prop('disabled', false);
//        $('[name="kd_kegiatan"]').prop('disabled', false);
//        $('[name="kd_output"]').prop('disabled', false);
//        $('[name="kd_sub_output"]').prop('disabled', false);
//        $('[name="kd_komponen"]').prop('disabled', false);
//        $('[name="kd_sub_komponen"]').prop('disabled', false);
//        $('[name="nama_sub_komponen"]').prop('disabled', false);
//        $('[name="id_ppk"]').prop('disabled', false);
//        $('[name="kdkppn"]').prop('disabled', false);

//        $('#archive-preview').hide(); // hide photo preview modal

//        $('#label-archive').text('Upload File'); // label photo upload

//          var tlist_detail = $('#tlist_detail').DataTable();
        //$('#tlist_detail').DataTable().clear().draw();

    }

    // modal detail
    function set_kegiatan(){
        if($("#ykd_kegiatan-sel").val()=="2409"){
            //$("#yjns_giat").val("F");
             $('#yjns_giat option:eq(1)').attr('selected', 'selected');;
        } 
        if($("#zkd_kegiatan").val()=="2409"){
            //$("#yjns_giat").val("F");
             $('#zjns_giat option:eq(1)').attr('selected', 'selected');;
        }
        
        if($("#wkd_kegiatan").val()=="2409"){
            //$("#yjns_giat").val("F");
             $('#wjns_giat option:eq(1)').attr('selected', 'selected');;
        } 
    }
    
    function dtTambahRowDetail(id, isFisik) {

        //$('.block block-opt-hidden > block-content').css('display', 'none');

        //        console.log(ambah row');
        //  $('#frm-detail')[0].reset();
        //  $('.div-tags').remove();
        //  simpanForm
        //$('.div-tags').remove();
        //$('.div-tags').html();
        //alert(id)//
        //$("#modal-detail").modal("show");
        var id_paket=id;
        var data_selected = xhrdata.data.filter(x => x[1] == id_paket)[0];
        //alert(data_selected[2]);
        refreshComboboxOutput('ythang-sel', 21, 'thang',data_selected[2],data_selected[2]);
        
        refreshComboboxOutput('ykd_kegiatan-sel', 5, 'kdgiat',data_selected[10],data_selected[10]);
        
        refreshComboboxOutput('ykd_output-sel', 30, 'kdgiat',data_selected[10],data_selected[19]);
    
        refreshComboboxOutput('ykd_sub_output-sel', 44, 'kdoutput',data_selected[19],data_selected[20]);
        
        refreshComboboxOutput('ykd_komponen-sel', 18, 'kdsoutput',data_selected[20],data_selected[24]);
        
        //refreshComboboxOutput('ykdgbkpk', 36, 'kdgbkpk',data_selected[20],data_selected[20]);
        initCombobox("ykdgbkpk",36);
        
        initCombobox("ykdakun",37);
       // alert( $('#yjns_giat option:eq(1)').val());
       //alert($("#ykd_kegiatan-sel").val());
        
        setTimeout(set_kegiatan, 8000);//
       
        
        
        
        $("#ykd_sub_komponen-sel").val(data_selected[25]);
        $("#ynama_sub_komponen-sel").val(data_selected[26]);
        $("#yid_paket").val(id_paket);
        
        var id_rprov=$("#id_rprov").val();
        refreshComboboxOutput('yprov', 60, 'kd_prov',id_rprov,id_rprov);
        refreshCombobox('ykabkot', 61, 'kd_prov',id_rprov);
     
        
        //refreshComboboxOutput('ykd_kegiatan-sel', 21, 'kdgiat',data_selected[10],data_selected[10]);
       // $("#ythang").val(data_selected[2]);
        //alert
        //$("#ythang-sel").val(data_selected[2]);
        //document.write( initCombobox('thang-sel', 28););
        //initCombobox('ythang-sel', 28);
        $(".decformat2").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

            var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");



            $('#hargasat').val(decform);

            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();

            var hasil = volume * hargasat;

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val(hasil);



        });

        $(".decformat").change(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

        });


        $(".decformat2").select(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });
        });


        $('#volume').on("focus", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            //$('#rm').val($(".decformat").val());
            $('#rm').val(hasil);
        });

        $('#volume').on("keyup", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val($(".decformat").val());
            //$('#rm').val(hasil);
        });






//
//        $('#kd_komponen-sel').on("change", function () {
//            var kdkmpnen = $('#kd_komponen-sel').val();
//
//            setInputVal10('hargasat', 50, 'kdkmpnen', kdkmpnen);
//
//
//            var hargasat = $('#hargasat').val();
//            var volume = $('#volume').val();
//////            console.log(hargasat);
//////            console.log(volume);
//            var hasil = volume * hargasat;
//            //console.log('dsjhsf');
//
////            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
//
//            $("#totalpagu").val(hasil);
//            $('#rm').val(hasil);
//        });

//        $('#hargasat').keyup(function (event) {
//                var hargasat = $('#hargasat').val();
//            var volume = $('#volume').val();
//            
////            alert(parseFloat(volume * hargasat));
//            $('#jumlah').val(volume * hargasat);
//
//            // format number
//          
//        });








        //initCombobox('id_ruas', 33);
        initCombobox('kdgbkpk', 36);
        //initCombobox('id_satuan', 26);
        multipleSelect('kdsdana', 38);
        initCombobox('treatment', 42);




//        $(".div-tags").tagsinput('removeAll');
        // $('<div class="div-tags"></div>').insertBefore( "#tags" );
        // $('<input type="text" placeholder="">').insertAfter( ".div-tags" );
        //$('.div-tags").tagsinput('add', []);
        $('.div-tags').tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
        });


        $('#modeform').val('tambah_detail');
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
//        $('#modalTitle').text('Tambah Data Detail');
        //$('#modal-detail').modal('show');
        $('#modal-detail').modal('show');
        $('.tbhItem').text('Tambah Detail Paket');



        $('#thang-sel').prop('disabled', true);
        $('#kd_kegiatan-sel').prop('disabled', true);
        $('#kd_output-sel').prop('disabled', false);
        $('#kd_sub_output-sel').prop('disabled', false);
        $('#kd_komponen-sel').prop('disabled', false);
        $('#kd_sub_komponen-sel').prop('disabled', true);
        $('#nama_sub_komponen-sel').prop('disabled', true);
        $('#id_ppk-sel').prop('disabled', false);
        $('#kdkppn-sel').prop('disabled', false);





        if (isFisik == '2409')
        {

            $('#jns_giat').on('change', function () {
                var jnsg = $('#jns_giat').val();

                //var jnsg = $('#jns_giat').val();


                if (jnsg == 'F')
                {
                    $('.rujuk').show();
                    $('.divNonFisik').hide();
                    $('#detail').prop('disabled', true);
                    $('.divRuas').show();
                    $("#id_ruas").prop('disabled', false);
                    $("#sta_awal").prop('disabled', false);
                    $("#sta_akhir").prop('disabled', false);
                    $("#treatment").prop('disabled', false);
                    $("#xid_jembatan").prop('disabled', false);
                    $("#longitude").prop('disabled', false);
                    $("#latitude").prop('disabled', false);
                } else {
                    $('.rujuk').hide();
                    $('.divNonFisik').show();
                    $('#detail').prop('disabled', false);
                    $('.divRuas').hide();
                    $("#id_ruas").prop('disabled', true);
                    $("#sta_awal").prop('disabled', true);
                    $("#sta_akhir").prop('disabled', true);
                    $("#treatment").prop('disabled', true);
                    $("#xid_jembatan").prop('disabled', true);
                    $("#longitude").prop('disabled', true);
                    $("#latitude").prop('disabled', true);
                }

                //alert(jnsg);


            });


            $('#jns_giat').val('F');
            $('.rujuk').show();
            $('.divNonFisik').hide();
            $('#detail').prop('disabled', true);
            $('.divRuas').show();
            $("#id_ruas").prop('disabled', false);
            $("#sta_awal").prop('disabled', false);
            $("#sta_akhir").prop('disabled', false);
            $("#treatment").prop('disabled', false);
            $("#xid_jembatan").prop('disabled', false);
            $("#longitude").prop('disabled', false);
            $("#latitude").prop('disabled', false);
        } else {
            $('#jns_giat').val('NF');
            $('#jns_giat').hide();
            $('.rujuk').hide();
            $('.divNonFisik').show();
            $('#detail').prop('disabled', false);
            $('.divRuas').hide();
            $("#id_ruas").prop('disabled', true);
            $("#sta_awal").prop('disabled', true);
            $("#sta_akhir").prop('disabled', true);
            $("#treatment").prop('disabled', true);
            $("#xid_jembatan").prop('disabled', true);
            $("#longitude").prop('disabled', true);
            $("#latitude").prop('disabled', true);
        }




        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {

//                alert(data.id_ppk);
//             alert(data.kdkppn);

                $('[name="id_paket"]').val(data.id_paket);
                $('#thang-sel').val(data.thang);
                $('#id_ppk-sel').val(data.id_ppk);
                $('#kdkppn-sel').val(data.kdkppn);
                $('[name="thang-sel"]').val(data.thang);
                $('[name="id_ppk-sel"]').val(data.id_ppk);
                $('[name="kdkppn-sel"]').val(data.kdkppn);
                $('#kd_sub_komponen-sel').val(data.kd_sub_komponen);
                $('#nama_sub_komponen-sel').val(data.nama_sub_komponen);
                $('[name="kd_sub_komponen-sel"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen-sel"]').val(data.nama_sub_komponen);




                updateCombobox('kd_kegiatan-sel', 5, data.kd_kegiatan);

                if (data.kd_kegiatan == '2409')
                {
                    $('#jns_giat').val('F');

                } else
                {
                    $('#jns_giat').val('NF');

                }


                $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);





                var j = function () {
                    var defer = $.Deferred();




                    refreshComboboxOutput('kd_output-sel', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);

                    //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
                    //initCombobox('kd_kegiatan-sel', 5);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
                var a = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);
                    //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
                var b = function () {
                    var defer = $.Deferred();
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen-sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);



                    //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
                var c = function () {
                    var defer = $.Deferred();
                    setInputVal10('hargasat', 50, 'kdkmpnen', data.kd_komponen);







                    //console.log('a() called');
                    var tlist_detail = $('#tlist_detail').DataTable();
                    tlist_detail.ajax.reload();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };

                var d = function () {
                    var defer = $.Deferred();
                    //console.log('a() called');$(".decformat2").val($('#hargasat').val());
                    //$(".decformat2").val($('#hargasat').val());

                    $(".decformat2").trigger("select");

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
//                        var i = function () {
//                        var defer = $.Deferred();
//                                //console.log('a() called');
//                                //alert()
//                               
//
//                                    setTimeout(function () {
//                                    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                                    }, 1000);
//                                return defer;
//                        };




                var pbar = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $(".overlay").show();

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };



                $.when(
                        pbar().then(j).then(a).then(b).then(c).then(d)
                        // Deferred object (probably Ajax request),
                        ).then(function () {
                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
//                    $('#modal-load').modal('toggle');
                });
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error Tambah Item Paket');
            }
        });
        $("#prov").empty();
        var data = get_prov();
        //  alert(data.nama_prov);
        $("#prov").append("<option value=" + data.kd_prov_irmsv3 + ">" + data.nama_prov + "</option>")
        refreshCombobox('kabkot', 49, 'kd_prov_irmsv3', data.kd_prov_irmsv3);
    }
    function get_prov() {
        var x = null;
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_prov') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    function bind_combo_prov() {


    }

    function dtDeleteRow(type, id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {
            // ajax delete data to database
            $.ajax({
                url: "<?php echo base_url(); ?>pagu_indikatif/ajax_delete/" + type + "/" + id,
                type: "POST",
                dataType: "JSON",
                success: function (data)
                {
                    //if success reload ajax table
                    if (type == 'paket')
                    {
                        $('#modal-tambah').hide();
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload();
                    } else
                    {

                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload();
                    }
                    //();
                },
                error: function (jqXHR, textStatus, errorThrown)
                {
                    alert('Error deleting data');
                }
            });
        }
    }

    function simpanFormPaket() {

//        var ret = false;
        //console.log(jsonstring);
//        if ($("#thang").val() == "#" || $("#kd_kegiatan").val() == "#" || $("#kd_output").val() == -1 || $("#kd_sub_output").val() == -1 || $("#kd_komponen").val() == -1 || $("#kd_sub_komponen").val() == "" || $("#nama_sub_komponen").val() == "")
//        {
//            alert("Data tidak dapat disimpan, lengkapi form data");
//            return ret;
//            
//        }
//        else {
//            return true;
//        }    
        //return true;

        var mode = $('#modeform').val();
        var url;
        //        $('#btnSave').text('saving...'); //change button text
        //        $('#btnSave').attr('disabled', true); //set button disable


        var formDataPaket = new FormData($("#frm-paket")[0]);
        console.log(formDataPaket);
//        var formDataPaket =JSON.stringify({
//                            "thang":$("#thang").val(),
//                            "kd_kegiatan":$("#kd_kegiatan").val(),
//                            "kd_output":$("#kd_output").val(),
//                            "kd_sub_output":$("#kd_sub_output").val(),
//                            "kd_komponen":$("#kd_komponen").val(),
//                            "kd_sub_komponen":$("#kd_sub_komponen").val(),
//                            "nama_sub_komponen":$("#nama_sub_komponen").val(),
//                            "kd_dept":$("#kd_dept").val(),
//                            "kd_unit":$("#kd_unit").val(),
//                            "kd_program":$("#kd_program").val(),
//                            "id_paket":$("#id_paket").val(),                       
//                            });
        //alert(JSON.stringify(formDataPaket));

        if (mode == 'tambah_paket') {
            url = "<?php echo base_url(); ?>pagu_indikatif/ajax_add/paket";

        } else if (mode == 'edit_paket') {
            url = "<?php echo base_url(); ?>pagu_indikatif/ajax_update/paket";
        }
        //alert(url);
        // ajax adding data to database        
        $.ajax({
            url: url,
            type: "POST",
            data: formDataPaket,
            dataType: "JSON",
            //async : false,
            //cache : false,
            contentType: false,
            processData: false,
            success: function (data)
            {

//                console.log(data);

                if (data.status) //if success close modal and reload ajax table
                {
                    $("#modal-tambah").modal('hide');
                    var tlist_paket = $('#tlist_paket').DataTable();


                    tlist_paket.ajax.reload();

                } else
                {

//                    for (var i = 0; i < data.inputerror.length; i++)
//                    {
//                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
//                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
//                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
    }
      
   
    function simpanForm(){
    
           if($("#id_ruas").val() != "-1" || $("#id_ruas").val() != "#"){
                
                if($("#id_jembatan").val() == "#" || $("#id_jembatan").val() == "-1"){
             
                      $("#ydetail").val($("#id_ruas option:selected").text());
                }else{
                    
                 
                    $("#ydetail").val($("#id_jembatan option:selected").text());
                   
                   
                }
            }
 
         
            var uraian     = $("#ydetail").val();
        var objmasterdetail={
              "kd_kegiatan" :$("#ykd_kegiatan-sel").val(),//master
              "kd_output" :$("#ykd_output-sel").val(),
              "kd_sub_output" :$("#ykd_sub_output-sel").val(),
              "kd_komponen" :$("#ykd_komponen-sel").val(),
              "kd_sub_komponen" :$("#ykd_sub_komponen-sel").val(),
              "thang" :$("#ythang-sel").val(),
              "satuan" :$("#ysatuan").val(),
              //"id_user" :$("#").val(),
              //"created_by":$("#").val(),
              "volume":$("#volume").val(),
              "kdgbkpk":$("#ykdgbkpk").val(),
              "kdakun":$("#ykdakun").val(),
              //"phln":$("#").val(),
              //"rmp":$("#").val(),
              //"sbsn":$("#").val(),
              "jumlah":$("#yjumlah").val().replace( /^\D+/g, ''),
              //"kdkppn":$("#").val(),
              //"kdsatker":$("#").val(),
              "kdlokasi":$("#yprov").val(),
              "kdkabkota":$("#ykabkot").val(),
              //"kd_isu":$("#").val(),
              //"wps_kode":$("#").val(),
              //"kws_kode":$("#").val(),
              //"id_paket":$("#").val(),
              "id_jembatan":$("#yid_jembatan").val(),
              "id_ruas":$("#id_ruas").val().split("::")[1],
              "sta_awal":$("#sta_awal-sel").val(),
              "sta_akhir":$("#sta_akhir-sel").val(),
              "longitude":$("#ylongitude").val(),
              "latitude":$("#ylatitude").val(),
              "id_paket":$("#yid_paket").val(),
              "detail"  :uraian,
        };
        //console.log("-----------uuuuuuuuuuuuuuuuuuuuuuuuu----------------")
        //console.log({ data_master_detail: JSON.stringify(objmasterdetail)});
        
        //$("#data_master_detail").val(objmasterdetail);
        //alert(JSON.stringify({"data_master_detail":JSON.stringify($("#data_master_detail").val())}));
         var url="<?php echo base_url("pagu_indikatif/simpan_detail") ?>"
         $.ajax({
           type: "POST",
            url:url,
            // The key needs to match your method's input parameter (case-sensitive).
            data: JSON.stringify({ data_master_detail: objmasterdetail}),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (response)
            {

//                console.log(data);
                //alert('Data berhasil disimpan');
                //alert(response.status);
                if (response.status) //if success close modal and reload ajax table
                {

                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();
                    //var tlist_detail = $('#tlist_detail').DataTable();
                    //$('#tlist_detail_1').DataTable().ajax.reload();
                    $('#frm-detail')[0].reset();

                    // Coding
                     $('#modal-detail').modal('hide'); //or  $('#IDModal').modal('hide');
                    //  return false;


//                    $('.div-tags').remove();
//
//                    $('<div class="div-tags"></div>').insertBefore( "#tags" );
                } else
                {
                    for (var i = 0; i < response.inputerror.length; i++)
                    {
                        $('[name="' + response.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + response.inputerror[i] + '"]').next().text(response.error_string[i]); //select span help-block class set text error string
                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
        
        
        //$("#frm-detail").submit();
     
        //alert('here');
//        $(".div-tags").tagsinput('add', []);

        //console.log($(".div-tags").tagsinput('items'));
        
        /***
        var seen = [];
        var objTagging = $(".div-tags").tagsinput('items');

        //console.log(objTagging);



        //console.log(objTagging[0].value);


        //var jsonstring = JSON.stringify(objTagging);
        var replacer = function (key, val) {
            if (val != null && typeof val == "object") {
                if (seen.indexOf(val) >= 0) {
                    return;
                }
                seen.push(val);
            }
            return val;
        };

        var jsonstring = JSON.stringify(objTagging, replacer);

        $('#tags').val(jsonstring);


        var vruas = $("#id_ruas option:selected").text();
        $('#nm_ruas').val(vruas);
        var vbridge = $("#xid_jembatan option:selected").text();
        $('#nm_jembatan').val(vbridge);


        /// $('#rm').text().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");
        //  $('#rmp').text().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");
        // $('#pln').text().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");
        //  $('#sbsn').text().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");
        //  $('#pnbp').text().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");


//        $('#jumlah').val($(this).val().replace(/./g, ""));

        //alert();
        //$('#tags').val(jsonstring);
        //console.log(jsonstring);


        var url;
        var mode = $('#modeform').val();

//        var dataSend;
        //        $('#btnSave').text('saving...'); //change button text
        //        $('#btnSave').attr('disabled', true); //set button disable


        var formDataPaket = new FormData($("#frm-paket")[0]);
        var formDetail = $("#frm-detail");
        var formDataDetail = formDetail.serializeArray();
        for (var i = 0; i < formDataDetail.length; i++)
            formDataPaket.append(formDataDetail[i].name, formDataDetail[i].value);
        if (mode == 'edit_detail') {
            url = "<?  php echo base_url(); ?>pagu_indikatif/ajax_update/detail";
        } else if (mode == 'tambah_detail') {
            url = "<?  php echo base_url(); ?>pagu_indikatif/ajax_add/detail";
        }


        //console.log(formDataPaket);
        // ajax adding data to database        
        $.ajax({
            url: url,
            type: "POST",
            data: formDataPaket,
            dataType: "JSON",
            //async : false,
            //cache : false,
            contentType: false,
            processData: false,
            success: function (data)
            {

//                console.log(data);
                alert('Data berhasil disimpan');

                if (data.status) //if success close modal and reload ajax table
                {

                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();
//                    var tlist_detail = $('#tlist_detail').DataTable();
//                    $('#tlist_detail').DataTable().ajax.reload();
                    $('#frm-detail')[0].reset();

                    //  e.preventDefault();
                    // Coding
                    //   $('#modal-detail').modal('hide'); //or  $('#IDModal').modal('hide');
                    //  return false;


//                    $('.div-tags').remove();
//
//                    $('<div class="div-tags"></div>').insertBefore( "#tags" );
                } else
                {
                    for (var i = 0; i < data.inputerror.length; i++)
                    {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
        
        ***/



    }

    function preview()
    {

        $('#imagepreview').attr('src', $('#imageresource').attr('src')); // here asign the image to the modal when the user click the enlarge link
        $('#imagemodal').modal('show'); // imagemodal is the id attribute assigned to the bootstrap modal, then i use the show function

    }

    function upload_attachment(id) {
        //console.log(xhrdata.data);
        //alert(JSON.stringify(xhrdata));
        var data_selected = xhrdata.data.filter(x => x[1] == id)[0];
        // console.log("data_selected");
        //console.log(data_selected);
        var xiduser = id_user_get;
        var xidusulan = data_selected[1];
        var xthang = data_selected[2];
        var str_src = "<?php echo base_url('/upload_pagu_indikatif/fileupload_pagu_indikatif?id_user='); ?>" + xiduser + "&id_usulan=" + xidusulan + "&thang=" + xthang;
        //alert(str_src);
        $("#iframeupload").attr("src", str_src);
        $("#modal-upload").modal("show");
    }

    function download_attachment(id) {

        listing_attachment2(id);



        $("#modal-download").modal("show");
    }

    function close_modal_attachment() {

        $("#modal-download").modal("hide");
        // location.reload();
    }

    var table_attachment = null;

    function listing_attachment2(id) {
        //alert(id);

        var data_selected = xhrdata.data.filter(x => x[1] == id)[0];
        var xiduser = role;
        // var xidusulan = data_selected[1];

        //alert(id+"xxxxx"+role)
        var xthang = data_selected[2];
        if ($.fn.dataTable.isDataTable('#table_id2')) {
            table_attachment = $('#table_id2').DataTable();
        } else {
            table_attachment = $('#table_id2').DataTable({
                "createdRow": function (row, data, index) {
                    var ico_class = get_extentsion_file(data[0]);
                    var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp"
                    $('td', row).eq(0).prepend(html_icon);
                },
                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]],
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>pagu_indikatif/ssp_attachment",
                    data: function (d) {
                        d.id = id;
                        d.role = role;
                    }
                },
                "aoColumnDefs": [{
                        "aTargets": [0],
                        "mRender": function (data, type, full) {
                            //console.log("full attachment");
                            //console.log(full);
                            var htm = '';
                            switch (full[3]) {
                                case '47':
                                    htm += full[0] + "<br><h6 style='color:#903509;'>Berkas File Usulan DPR<h6>";
                                    break;
                                case '6':
                                    htm += full[0] + "<br><h6 style='color:#5c9009;'>Berkas File Yang Telah Ditelaah Oleh KPSJ</h6>";
                                    break;
                                case '60':
                                    htm += full[0] + "<br><h6 style='color:#186d71;'>Berkas File Yang Telah Ditelaah Oleh BALAI<h6>";
                                    break;
                                case null:
                                    htm += "...";
                                    break;
                            }

                            return htm;
                        }
                    },
                    {
                        "aTargets": [1],
                        "mRender": function (data, type, full) {
                            //console.log("full attachment");
                            //console.log(full);
                            var data_full_attachment = full[0];
                            var html_button = [
                                "<button onclick=download_file('" + data_full_attachment + "') class='btn btn-primary btn-xs'>",
                                "<i class='fa fa-download'>",
                                "</i>",
                                "</button>",
                            ].join("\n");
                            return html_button;
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "Memuat...",
                    "processing": "Memroses...",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();

                //        console.log('xhr data: ' );
                console.log(xhrdata1);
            });




//            table_attachment.reload();
            //});

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        }
        //alert("Halaman akan direload harap menunggu...")
    }

    function download_file(data) {
        //  alert(data);
        //var data = table.row( $(this).parents('tr') ).data();
        var filname = data;
        window.open('<?php echo base_url("/pagu_indikatif/download/"); ?>' + filname, '_blank');
    }
    function get_extentsion_file(file) {
        var extension = file.substr((file.lastIndexOf('.') + 1));
        switch (extension) {
            case 'jpg':
            case 'png':
            case 'jpeg':
            case 'gif':
                //alert('was jpg png gif');
                return 'fa fa-image';  // There's was a typo in the example where
                break;                         // the alert ended with pdf instead of gif.
            case 'zip':
            case 'rar':
                //alert('was zip rar');
                return 'fa fa-file-archive-o'
                break;
            case 'pdf':
                //alert('was pdf');
                return 'fa fa-file-pdf-o';
            case 'xlsx':
                return 'fa fa-file-excel-o';
                break;
            default:
                "fa fa-file"
        }
    }
    
    
//     var obj_arahan = {
//            "sa1thn_id": sa1thn_id,
//            "tahun_anggaran": data_selected[1],
//            "kawasan_nama": data_selected[2],
//            "subkawasan_nama": data_selected[3],
//            "kegiatan_nama": data_selected[4],
//            "suboutput_nama": data_selected[5],
//            "output_nama": data_selected[6],
//            "sub_aktivitas": data_selected[7],
//            "satuan_output": data_selected[8],
//            "volume": data_selected[9],
//            "rpm": data_selected[10],
//            "phln": data_selected[11],
//            "sbsn": data_selected[12],
//            "rmp": data_selected[13],
//            "unit_id": data_selected[14],
//            "program_id": data_selected[15],
//            "kd_kegiatan": data_selected[16],
//            "kd_output": data_selected[17],
//            "kd_suboutput": data_selected[18],
//            "provinsi_id": data_selected[19],
//            "kabkot": data_selected[20],
//            "jenis_kontrakID": data_selected[21],
//            "rc_FS": data_selected[22],
//            "rc_DED": data_selected[23],
//            "rc_Dokling": data_selected[24],
//            "rc_lahan": data_selected[25],
//            "wps_kode": data_selected[26],
//            "kws_kode": data_selected[27],
//            "status_konreg": data_selected[28],
//            "status_verifikasi": data_selected[29],
//            "status_rakor": data_selected[30],
//            "catatan": data_selected[31],
//            "kd_isu": data_selected[32],
//            "kd_komponen": $("#kd_komponen").val(),
//            "kd_sub_komponen": $("#kd_sub_komponen").val(),
//            "nama_sub_komponen": $("#nama_sub_komponen").val(),
//            "kd_jns_belanja": $("#kdgbkpk").val(),
//            "kdakun": $("#kdakun").val(),
//            "kdkppn": $("#kdkppn").val(),
//            "id_ppk": $("#id_ppk").val(),
//            "rc_ded_status": $("#rc_ded_status").val(),
//            "rc_fs_status": $("#rc_fs_status").val(),
//            "rc_lahan_status": $("#rc_lahan_status").val(),
//            "rc_doklin_status": $("#rc_doklin_status").val(),
//            "jnskontrak": $("#jnskontrak").val(),
//            "id_ruas": $("#id_ruas").val(),
//            "sta_awal": $("#sta_awal").val(),
//            "sta_akhir": $("#sta_akhir").val(),
//            "id_jembatan": $("#id_jembatan").val(),
//            "longitude": $("#longitude").val(),
//            "latitude": $("#latitude").val(),
//            //"id_paket":$("#").val(),
//            "volume": data_selected[9],
//            "satuan": data_selected[8],
//            "hargasat": $("#hargasat").val(),
//            "jumlah": data_selected[10],
//        };
    function simpan_form_pagu(){
            var phln=0;
            var sbsn=0;
            var jumlah=0;
            var volume=0;
            var rmp=0;
            var id_ruas    = $("#xid_ruas").val().split("::")[1];
            
            var id_jembatan= $("#xid_jembatan").val();
            var sta_awal   = $("#sta_awal").val();
            var sta_akhir  = $("#sta_akhir").val();
            var longitude  = $("#longitude").val();
            var latitude   = $("#latitude").val();
            
            //alert(longitude);
            //alert(latitude);
            if($("#xid_ruas").val() != "-1" || $("#xid_ruas").val() != "#"){
                
                if($("#xid_jembatan").val() == "#" || $("#xid_jembatan").val() == "-1"){
             
                      $("#xdetail").val($("#xid_ruas option:selected").text());
                }else{
                    
                 
                    $("#xdetail").val($("#xid_jembatan option:selected").text());
                   
                   
                }
            }
 
            
            var uraian     = $("#xdetail").val();
            $("#xtotalpagu").val($(".decformat").val());
            if(check_rc(obj_data_cart.phln)=="#"){
                phln=0;
            }
            
            if(check_rc(obj_data_cart.sbsn)=="#"){
                sbsn=0;
            }
            
            if(check_rc(obj_data_cart.rmp)=="#"){
                rmp=0;
            }
            
            if($("#xtotalpagu").val()=="#"){
                jumlah=0;
            }else{
                jumlah=$("#xtotalpagu").val();
            }
            
            if($("#xvolume").val()=="#"){
                volume=0;
            }else{
               volume=$("#xvolume").val();
            }
            var wps_kode=$("#wps_kode").val().split('::')[0];
            var kws_kode=$("#kws_kode").val().split('::')[1];
            var sub_kawasan_nama=$("#subkw").val();
            //alert("jumlah :"+jumlah);
            //alert("volume :"+volume);
            
            //alert($("#xnama_sub_komponen").val());
            
            //alert(id_jembatan);
           // alert(id_ruas);
            
            var xobj_usulan = {
            "thang":$("#thang").val(),
            //"kawasan_nama": data_selected[2],//not exists in usulan indikatif
            //"subkawasan_nama": data_selected[3],
            //"kegiatan_nama": data_selected[4],
            //"suboutput_nama": data_selected[5],
            //"output_nama": data_selected[6],
            //"sub_aktivitas": data_selected[7],
            //"satuan_output": data_selected[8],
            "volume":volume,
            //"rpm": data_selected[10],
            "phln":phln,//not exists in form exists in table
            "sbsn": sbsn,//not exists in form exists in table
            "rmp": rmp,//not exists in form exists in table
            "kd_unit": obj_data_cart.kd_unit,//not exists in form exists in table
            "kd_program": obj_data_cart.kd_program,//not exists in form exists in table
            "kd_kegiatan": $("#kd_kegiatan").val(),//not exists in form exists in table
            "kd_output": $("#kd_output").val(),
            "kd_sub_output":$("#kd_sub_output").val(),
            "kdlokasi":$("#prov").val(),//kode provinsi
            "kdkabkota":$("#kabkot").val(),
            "id_jenis_kontrak":$("#jnskontrak").val(),
            //"rc_FS": data_selected[22],
            //"rc_DED": data_selected[23],
            //"rc_Dokling": data_selected[24],
            //"rc_lahan": data_selected[25],
            "wps_kode":wps_kode,
            "kws_kode":kws_kode,
            //"status_konreg": data_selected[28],
            //"status_verifikasi": data_selected[29],
            //"status_rakor": data_selected[30],
            //"catatan": data_selected[31],
            "kd_isu":$("#kd_isu").val(),
            "kd_komponen": $("#kd_komponen").val(),
            "kd_sub_komponen": $("#kd_sub_komponen").val(),
            "nama_sub_komponen": $("#xnama_sub_komponen").val(),//tanpa x id elemen sama dengan modal detail 
            "kd_jns_belanja": $("#kdgbkpk").val(),//yang ada pada form sama dengan jenis belanja
            "kdgbkpk": $("#kdgbkpk").val(),
            "kdakun": $("#kdakun").val(),
            "kdkppn": $("#kdkppn").val(),
            "id_ppk": $("#id_ppk").val(),
            "rc_ded_status": $("#rc_ded_status").val(),
            "rc_fs_status": $("#rc_fs_status").val(),
            "rc_lahan_status": $("#rc_lahan_status").val(),
            "rc_doklin_status": $("#rc_doklin_status").val(),
            //"jnskontrak": $("#jnskontrak").val(),
            "id_ruas":id_ruas,
            "sta_awal":sta_awal,
            "sta_akhir":sta_akhir,
            "id_jembatan":id_jembatan,
            "longitude":longitude,
            "latitude":latitude,
            "detail":uraian,
            "satuan":$("#xsatuan").val(),
            "hargasat": $("#hargasat").val(),
            "jumlah": jumlah,
        };
        
        var url="<?php echo base_url("pagu_indikatif/save_usulan_pagu"); ?>"
        $.ajax({
           type: "POST",
            url:url,
            // The key needs to match your method's input parameter (case-sensitive).
            data: JSON.stringify({ data_master_detail: xobj_usulan}),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (response)
            {


                if (response.status) //if success close modal and reload ajax table
                {

                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();
                    
                    $('#frm-tambah')[0].reset();

                    // Coding
                     $('#modal-tambah').modal('hide'); //or  $('#IDModal').modal('hide');

                } else
                {
                    for (var i = 0; i < response.inputerror.length; i++)
                    {
                        $('[name="' + response.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + response.inputerror[i] + '"]').next().text(response.error_string[i]); //select span help-block class set text error string
                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
    
//        $("#modal-tambah").modal("hide");
//        $("#data_arahan_terpilih").val(JSON.stringify(xobj_usulan));
//        $("#frm-tambah").submit();
    }
    
    
    
    function update_form_pagu(){
      
        var objmasterdetail={
              "kd_kegiatan" :$("#zkd_kegiatan").val(),//master
              "kd_output" :$("#zkd_output").val(),
              "kd_sub_output" :$("#zkd_sub_output").val(),
              "kd_komponen" :$("#zkd_komponen").val(),
              "kd_sub_komponen" :$("#zkd_sub_komponen").val(),
              "nama_sub_komponen" :$("#znama_sub_komponen").val(),
              "thang" :$("#zthang").val(),
              //"satuan" :$("#ysatuan").val(),
              //"id_user" :$("#").val(),
              //"created_by":$("#").val(),
              //"volume":$("#volume").val(),
              "kdgbkpk":$("#zkdgbkpk").val(),
              //"kdakun":$("#ykdakun").val(),
              //"phln":$("#").val(),
              //"rmp":$("#").val(),
              //"sbsn":$("#").val(),
              //"jumlah":$("#yjumlah").val().replace( /^\D+/g, ''),
              //"kdkppn":$("#").val(),
              //"kdsatker":$("#").val(),
              "kdlokasi":$("#zprov").val(),
              //"kdkabkota":$("#ykabkot").val(),
              "kd_isu":$("#zkd_isu").val(),
              //"wps_kode":$("#zwps_kode").val().split('::')[0],
              //"kws_kode":$("#zkws_kode").val().split('::')[1],
              "id_paket":$("#zid_paket").val(),
              "jnskontrak":$("#zjnskontrak").val(),
              "kdkppn":$("#zkdkppn").val(),
              "id_ppk":$("#zid_ppk").val(),
              "rc_ded_status":$("#zrc_ded_status").val(),
              "rc_fs_status":$("#zrc_fs_status").val(),
              "rc_lahan_status":$("#zrc_lahan_status").val(),
              "rc_doklin_status":$("#zrc_doklin_status").val(),   
        };
        //console.log("-----------uuuuuuuuuuuuuuuuuuuuuuuuu----------------")
        //console.log({ data_master_detail: JSON.stringify(objmasterdetail)});
        
        //$("#data_master_detail").val(objmasterdetail);
        //alert(JSON.stringify({"data_master_detail":JSON.stringify($("#data_master_detail").val())}));
         var url="<?php echo base_url("pagu_indikatif/update_paket_pagu") ?>"
         $.ajax({
           type: "POST",
            url:url,
            // The key needs to match your method's input parameter (case-sensitive).
            data: JSON.stringify({ data_master_detail: objmasterdetail}),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (response)
            {

//                console.log(data);
                //alert('Data berhasil disimpan');
                //alert(response.status);
                if (response.status) //if success close modal and reload ajax table
                {

                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();
                    //var tlist_detail = $('#tlist_detail').DataTable();
                    //$('#tlist_detail_1').DataTable().ajax.reload();
                   // $('#frm-detail')[0].reset();

                    // Coding
                     $('#modal-edit-paket').modal('hide'); //or  $('#IDModal').modal('hide');
                    //  return false;


//                    $('.div-tags').remove();
//
//                    $('<div class="div-tags"></div>').insertBefore( "#tags" );
                } else
                {
                    for (var i = 0; i < response.inputerror.length; i++)
                    {
                        $('[name="' + response.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + response.inputerror[i] + '"]').next().text(response.error_string[i]); //select span help-block class set text error string
                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
    }
    
    
    
    
    
    function handle_form_fisik(element){
        var field_value = element.value;
        if(field_value == "F"){
           $(".divRuas").css("display","");
           $(".divNonFisik").css("display","none");
        }else {
           $(".divRuas").css("display","none");
           $(".divNonFisik").css("display","");
        }
    }
    
    //reff_index= table_name
function refreshComboboxString(divname, refindex, refresh_field, refresh_value) {
    url = "<?= base_url(); ?>lookup/refreshlookstringid/" + refindex + "/" + refresh_field + "/" + refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", -1));
        $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("error");
            })
            .always(function () {
                // alert("finished");
            });
}

function updateComboboxAndSelected(divname, refindex, selvalue) {
    url = "<?= base_url(); ?>lookup/fieldlook/" + refindex;
    
    $.get(url).done(function (data) {
        var jdata = JSON.parse(data);
        $('#' + divname).empty();
        $('#' + divname).append("<option value='#' >"+"--Pilih--"+"</option>");
        //alert(selvalue);
        for(var i=0; i<= jdata.length-1; i++ ){
            if(jdata[i].id==selvalue){
                //alert("BBBB");
                $('#' + divname).append("<option selected value="+jdata[i].id+" >"+jdata[i].val+"</option>");
            }else{
                $('#' + divname).append("<option value="+jdata[i].id+" >"+jdata[i].val+"</option>");
            }
        }

       
    })
    .fail(function () {
        alert("error");
    })
    .always(function () {
        // alert("finished");
    });
}
    
</script>
