<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<script>
    var xhrdatairms = null;
    var xhrdatasipro = null;
    var xhrdatarams = null;
    var xhrdataeprogram = null;
    var xhrdatarenstra = null;
    var xhrdatausulandpr = null;
    var xhrdatausulanpemda = null;
    var tablex = null;
    //modal detail
    var xhrdataxirms = null;
    var xhrdatasxipro = null;
    var xhrdataxrams = null;
    var xhrdataxeprogram = null;
    var xhrdataxrenstra = null;
    var xhrdataxusulandpr = null;
    var xhrdatausxulanpemda = null;
    var tlist_paket = null;
    var user_satker = "<?php echo $this->session->users['kode_satker']; ?>";
    var prrr = "<?php echo $this->session->users['kdlokasi']; ?>";
    var id_user_get = "<?php echo $this->session->users['id_user']; ?>";
    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";
    var id_user_group = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var yearnow = "<?php echo $this->session->konfig_tahun_ang; ?>"
            var kd_satker = "<?php echo $this->session->users['kode_satker']; ?>";
    var data_cart = [];
    var obj_data_cart = {};
    var tahap = "<?php echo $this->session->konfig_tahapan; ?>";
    var kdtahap="<?php echo $this->session->konfig_kd_tahapan;?>";
    var tahun_anggaran="<?php echo $this->session->konfig_tahun_ang;?>";

    function add_to_chart(sa1thn_id, iDataIndex) {
//        var tag = $('.div-tags').val();
//        console.log(tag);
        var statement_tag = 'SIPRO::' + sa1thn_id;
        dtTagging(statement_tag);
//        if (tag === null || tag === ''){
//            console.log("isi");
//            var statement_tag = 'SIPRO::' + sa1thn_id;
//            dtTagging(statement_tag);
//        } else {
//            console.log("udah ada");
//        }
    var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
    var obj_arahan = {
    "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#kd_komponen").val(),
            "kd_sub_komponen": $("#kd_sub_komponen").val(),
            "nama_sub_komponen": $("#nama_sub_komponen").val(),
            "kd_jns_belanja": $("#kdgbkpk").val(),
            "kdakun": $("#kdakun").val(),
            "kdkppn": $("#kdkppn").val(),
            "id_ppk": $("#id_ppk").val(),
            "rc_ded_status": $("#rc_ded_status").val(),
            "rc_fs_status": $("#rc_fs_status").val(),
            "rc_lahan_status": $("#rc_lahan_status").val(),
            "rc_doklin_status": $("#rc_doklin_status").val(),
            "jnskontrak": $("#jnskontrak").val(),
            "id_ruas": $("#id_ruas").val(),
            "sta_awal": $("#sta_awal").val(),
            "sta_akhir": $("#sta_akhir").val(),
            "id_jembatan": $("#xid_jembatan").val(),
            "longitude": $("#longitude").val(),
            "latitude": $("#latitude").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#hargasat").val(),
            "jumlah": data_selected[10]
    };
    refreshComboboxOutput('xid_ruas', 70, 'KODE_KABKOTA', data_selected[20]);
    var kdgi=obj_arahan.kd_kegiatan;
    var kdo=obj_arahan.kd_output;
    var kdou=kdo.split('-')[1];
//    alert(kdou);
    if(kdgi=='2409' && (kdou=='014' || kdou=='015' || kdou=='016' || kdou=='017' || kdou=='018' || kdou=='951' || kdou=='970' || kdou=='994' || kdou=='999'))
               {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                       $(".uraians").show();
                   //$("#fsk").hide();
                  // alert("ilang");
                  $("#VolumeKegiatan").show();
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").show();
                    $("input[name='xradioVolume']").val([3]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                    $('#'+prefix+'maxRuas').text('( Panjang ruas maksimum: ' + (Math.round(jdata.length)) + ' )');
               }
               //fisik
               else if(kdgi=='2409' && (kdou=='001' || kdou=='002' || kdou=='003' || kdou=='004' || kdou=='005' || kdou=='006' || kdou=='007' || kdou=='008' || kdou=='009' || kdou=='010' || kdou=='011' || kdou=='012' || kdou=='013'))
                {
                    //jembatan
                    if(kdou=='004' || kdou=='005' || kdou=='006' || kdou=='010')
                    {
                     $(".ruasjalan").show();
                     $(".ruasjalan").removeClass('col-md-6');
                     $(".ruasjalan").addClass('col-md-12');
                    $(".staw").hide();
                    $("#sta_awal").empty();
                    $("#sta_akhir").empty();
                    $(".jembatans").show();
                    $("#l1").text("Longitude (X1)")
                    $("#l2").text("Latitude (Y1)")
                    $("#longitudes").empty()
                    $("#latitudes").empty()
                    $("#longitude").empty()
                    $("#latitude").empty()
                    $(".longs").show();
                    $(".longsa").hide();
                    $("#VolumeKegiatan").show();
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").show();
                    $(".id_radioHonor").hide();
                    $("input[name='xradioVolume']").val([2]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                    }
                    //ruas
                    else
                    {
                    $(".ruasjalan").show();
                   $(".ruasjalan").removeClass('col-md-12');
                     $(".ruasjalan").addClass('col-md-6');
                    $(".staw").show();
                    $(".jembatans").hide();
                    $("#xid_jembatan").empty()
                    $("#l1").text("Longitude (X1)")
                    $("#l2").text("Latitude (Y1)")
                     $("#l3").text("Longitude (X2)")
                    $("#l4").text("Latitude (Y2)")
                    $(".longs").show();
                    $(".longsa").show();
                    $("#VolumeKegiatan").show();
                    $(".id_radioRuas").show();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").hide();
                    $("input[name='xradioVolume']").val([1]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                    }
                }
                //tambah paket
                else if(kdgi !='2409')
                {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                    $(".uraians").show();
                     $(".longsa").hide();
                     $("#VolumeKegiatan").show();
                     $(".id_radioRuas").hide();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").show();
                    $("input[name='xradioVolume']").val([3]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                }


    //cek jika kode prov dari rujukan != null atau kosong
    if (data_selected[19] != "" || typeof data_selected[19] != "object"){
    var pkdprov = data_selected[19];
    bind_prov_from_vprovsatker(kd_satker, pkdprov);
    }

    $("#thang").val(data_selected[1]);
    $("#kd_isu").val(data_selected[32]);
    $("#rc_ded_status").val(check_rc((obj_arahan.rc_DED)));
    $("#rc_fs_status").val(check_rc((obj_arahan.rc_FS)));
    $("#rc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
    $("#rc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
    $("#xvolume").val(data_selected[9]);
    if (isNull(obj_arahan.wps_kode) == "#") {

    } else {

    }
    var rIdprov = $("#id_rprov").val();
    refreshComboboxOutput('kabkot', 61, 'id_kabkot', obj_arahan.kabkot, obj_arahan.kabkot);
    $("#subkw").val(data_selected[36]);
    $("#kd_kegiatan").val(data_selected[16]);
    $(".decformat").val(obj_arahan.rpm);
    $("#totalpagu").val(obj_arahan.rpm);
    $("#rm").val(obj_arahan.rpm);
    $("#volume").val(obj_arahan.volume);
    $("#satuan").val(obj_arahan.satuan);
    $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
    var j = function () {
    var defer = $.Deferred();
    refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var a = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var b = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var c = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode;
    var selected = "";
    if(obj_arahan.kws_kode == null) {
    selected = "-1";
    } else {

    selected = obj_arahan.kws_kode;
    }
    refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    //wps_kode,kws_kode
    var d = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
    refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    c().then(d).then(j).then(a).then(b);
    var xhargasat = numberWithCommas($("#xhargasat").val());
    var newhargasat = xhargasat.split(".")[0];
    $("#xhargasat").val(newhargasat);
    var xjumlah = numberWithCommas($("#xjumlah").val());
    $("#xjumlah").val(numberWithCommas(xjumlah));
    $("#xrm").val(xjumlah);
    var xvolume = $("#xvolume").val();
    $("#xvolume").val(numberWithCommas(xvolume));
    }

    function bind_prov_from_vprovsatker(kode_satker, kdprov){
    var obj_vprovsatker = get_vprov_satker(kode_satker);
    $("#prov").empty();
    $("#prov").append("<option value='#'>Pilih</option>");
    for (var i = 0; i <= obj_vprovsatker.length - 1; i++) {
    var option_value = obj_vprovsatker[i].kdlokasi;
    if (kdprov == kdprov){
    var html_option = [
            "<option selected value=" + option_value + " >",
            obj_vprovsatker[i].nama_prov,
            "</option>"
    ].join("\n");
    } else{
    var html_option = [
            "<option value=" + option_value + " >",
            obj_vprovsatker[i].nama_prov,
            "</option>"
    ].join("\n");
    }

    $("#prov").append(html_option);
    }
    }

    function get_vprov_satker(kode_satker){
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_vprov_satker') ?>" + "/" + kode_satker;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }


    var data_cart_edit = [];
    var obj_data_cart_edit = {};
    function add_to_chart_edit(sa1thn_id, iDataIndex) {;
    var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
    var obj_arahan = {
    "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#zkd_komponen").val(),
            "kd_sub_komponen": $("#zkd_sub_komponen").val(),
            "nama_sub_komponen": $("#znama_sub_komponen").val(),
            "kd_jns_belanja": $("#zkdgbkpk").val(),
            "kdakun": $("#zkdakun").val(),
            "kdkppn": $("#zkdkppn").val(),
            "id_ppk": $("#zid_ppk").val(),
            "rc_ded_status": $("#zrc_ded_status").val(),
            "rc_fs_status": $("#zrc_fs_status").val(),
            "rc_lahan_status": $("#zrc_lahan_status").val(),
            "rc_doklin_status": $("#zrc_doklin_status").val(),
            "jnskontrak": $("#zjnskontrak").val(),
            "id_ruas": $("#zid_ruas").val(),
            "sta_awal": $("#zsta_awal").val(),
            "sta_akhir": $("#zsta_akhir").val(),
            "id_jembatan": $("#zxid_jembatan").val(),
            "longitude": $("#zlongitude").val(),
            "latitude": $("#zlatitude").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#zhargasat").val(),
            "jumlah": data_selected[10]
    };

    console.log("object arahan");
    console.log(obj_arahan);
    $("#zthang").val(data_selected[1]);
    $("#zkd_isu").val(data_selected[32]);
    $("#zrc_ded_status").val(check_rc((obj_arahan.rc_DED)));
    $("#zrc_fs_status").val(check_rc((obj_arahan.rc_FS)));
    $("#zrc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
    $("#zrc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
    $("#zvolume").val(data_selected[9]);

    if (isNull(obj_arahan.wps_kode) == "#") {

    } else {

    }
    var rIdprov = $("#id_rprov").val();
    refreshComboboxOutput('kabkot', 61, 'id_kabkot', obj_arahan.kabkot, obj_arahan.kabkot);

    $("#zsubkw").val(data_selected[36]);
    $("#zkd_kegiatan").val(data_selected[16]);
    $(".decformat").val(obj_arahan.rpm);
    $("#ztotalpagu").val(obj_arahan.rpm);
    $("#zrm").val(obj_arahan.rpm);
    $("#zvolume").val(obj_arahan.volume);
    $("#zsatuan").val(obj_arahan.satuan);
    $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
    var j = function () {
    var defer = $.Deferred();
    refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var a = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var b = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var c = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode;

    var selected = "";
    if (obj_arahan.kws_kode == null) {
    selected = "-1";
    } else {

    selected = obj_arahan.kws_kode;
    }
    refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    //wps_kode,kws_kode
    var d = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
    refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    c().then(d).then(j).then(a).then(b);
    data_cart_edit.push(obj_arahan);
    console.log("data cart")
            console.log(JSON.stringify(data_cart_edit));
    }

    //memanaggil API
    function get_data_wps() {

    var id_province = $("#id_rprov").val();
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_wps2') ?>" + "/" + id_province;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }


    function get_data_kawasan(kawasan_kode) {
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_kws2') ?>" + "/" + kawasan_kode;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function get_data_sub_kawasan(kawasan_kode) {
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_kws2') ?>" + "/" + kawasan_kode;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }


    function get_ruas_by_province() {
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_ruas_by_province') ?>";
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function isNull(data) {
    var result = "";
    if (data == "-") {
    result = "#"; //--pilih--
    } else if (data == "") {
    result = "#";
    } else if (data == null) {
    result = "#";
    }

    return result;
    }


    function check_rc(data) {
    var result = "";
    if (isNaN(data) == false) {

    if (data < yearnow) {
    result = "siap";
    } else {
    result = "tidak_siap";
    }

    } else {

    if (data == "-") {
    result = "#"; //--pilih--
    } else if (data == "") {
    result = "#";
    } else if (data == null) {
    result = "#";
    }

    }
    return result;
    }

    function fnFormatDetails(table_id, html) {

    return "<table style='overflow=scroll' id=\"tlist_detail_" + table_id + "\">" +
            "<thead>" +
            "<tr>" +
            "<th>Rujukan</th>" +
            "<th>ID</th>" +
            // "<th>Tahun</th>" +
            // "<th>Paket</th>" +
            "<th>Uraian</th>" +
            "<th>Jenis Beban</th>" +
              "<th>Akun</th>" +
//            "<th>Ruas</th>" +,
            // "<th>STA Awal</th>" +
            // "<th>STA Akhir</th>" +
            // "<th>Koord. X</th>" +
            // "<th>Koord. Y</th>" +
            // "<th>Koord. X2</th>" +
            //  "<th>Koord. Y2</th>" +
            "<th>Volume</th>" +
            "<th>Satuan</th>" +


            "<th>Jumlah</th>" +
            "<th>Action</th>" +
            "</tr>" +
            "</thead>" +
            "</table>";
    }

    function bind_wps_by_province() {
    var obj_wps = get_data_wps();
    $("#wps_kode").empty();
    $("#wps_kode").append("<option value='#'>Pilih</option>");
    for (var i = 0; i <= obj_wps.length - 1; i++) {
    var option_value = obj_wps[i].wps_kode + "::" + obj_wps[i].kws_kode
            var html_option = [
                    "<option value=" + option_value + " >",
                    obj_wps[i].wps_nama,
                    "</option>"
            ].join("\n");
    $("#wps_kode").append(html_option);
    }
    }

    function bind_kawasan_by_kws_kode(element) {

        if(element.value=='#')
            {
                $("#kws_kode").prop("disabled",true);
                $("#subkw").prop("disabled",true);



            }
            else
                {
                $("#kws_kode").prop("disabled",false);
                       $("#subkw").prop("disabled",true);
//                $("#subkw").prop("disabled",false);
    var kawasan_kode = element.value.split('::')[1];
    var obj_kawasan = get_data_kawasan(kawasan_kode);
    $("#kws_kode").empty();
    $("#subkw").empty();

    $("#kws_kode").append("<option value='#'>--Pilih--</option>");
    for (var i = 0; i <= obj_kawasan.length - 1; i++) {
    var option_value = obj_kawasan[i].kws_kode + "::" + obj_kawasan[i].subkawasan_nama;
    var html_option = [
            "<option value=" + option_value + " >",
            obj_kawasan[i].kws_nama,
            "</option>"
    ].join("\n");
    $("#kws_kode").append(html_option);
    }
    bind_subkawasan(kawasan_kode);
                }

    }

    function get_data_subkawasan(kawasan_kode){
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_subkws2') ?>" + "/" + kawasan_kode;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {

            x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function bind_subkawasan(kawasan_kode) {
                if(kawasan_kode.value=='#')
            {
                //$("#kws_kode").prop("disabled",true);
                $("#subkw").prop("disabled",true);

            }
        else
            {
//        alert(kawasan_kode.value);
                 $("#subkw").prop("disabled",false);
    var obj_subkawasan = get_data_subkawasan(kawasan_kode);
//    $("#subkw").append("<option value='#'>--Pilih--</option>");
    for (var i = 0; i <= obj_subkawasan.length; i++) {
    var option_value = obj_subkawasan[i].subkawasan_nama;
    var html_option = [
            "<option value='" + option_value + "' >",
            obj_subkawasan[i].subkawasan_nama,
            "</option>",
    ].join("\n");
    $("#subkw").append(html_option);
    }
    }
    }

    var iTableCounter = 1;
    var oTable;
    var oInnerTable;
    var TableHtml;
    function count_jumlah(element) {
    var hargasatuan = element.value.replace(/\D/g, '');
    //modal lain
    var volume = $("#volume").val();
    var jumlah = parseFloat(hargasatuan) * volume;
    $("#yjumlah").val(jumlah);
    //modal tambah
    var xvolume = $("#xvolume").val();
    var xjumlah = parseFloat(hargasatuan) * parseFloat(xvolume);
    $("#xjumlah").val(xjumlah);
    var wvolume = $("#wvolume").val();
    var wjumlah = parseFloat(hargasatuan) * parseFloat(wvolume);
    $("#wjumlah").val(wjumlah);
    //formating number
    $("#xhargasat").val(numberWithCommas(hargasatuan));
    $("#xjumlah").val(numberWithCommas($("#xjumlah").val()));
    $("#xrm").val(numberWithCommas($("#xjumlah").val()));
    $("#yhargasat").val(numberWithCommas(hargasatuan));
    $("#yjumlah").val(numberWithCommas($("#yjumlah").val()));
    $("#yrm").val(numberWithCommas($("#yjumlah").val()));
    $("#whargasat").val(numberWithCommas(hargasatuan));
    $("#wjumlah").val(numberWithCommas($("#wjumlah").val()));
    //modal tambah item
    $("#rm").val(numberWithCommas($("#yjumlah").val()));
    }

    function get_sum_batas(p) {
    $("#conte").show();
    var x = p.value;
    var z = 'kosong';
    alert(x);
            $.ajax({
            url: "<?php echo base_url('pagu_tahapan/sum_batas') ?>/" + x + "/" + z,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                    alert(data);
                            $("#box-bts-prov").empty();
                    $("#box-bts-prov").append(data);
                            $("#btspagu").empty();
                    $("#btspagu").append(data);
                            $("#textbtspagu").text("Total Batas Pagu Provinsi");
                    },
                    failure: function (errMsg) {
                    alert(errMsg);
                    }
            });
    }

    function get_sum_batas_output(p) {
    $("#conte").show();
    var x = p.value;
    var z = $("#kd_prov").val();
    alert(x);
            $.ajax({
            url: "<?php echo base_url('pagu_tahapan/sum_batas') ?>/" + z + "/" + x,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                    alert(data);
                            $("#box-bts-output").empty();
                    $("#box-bts-output").append(data);
                            $("#btspagu").empty();
                    $("#btspagu").append(data);
                            $("#textbtspagu").text("Total Batas Pagu Output");
                    },
                    failure: function (errMsg) {
                    alert(errMsg);
                    }
            });
    }

    function filters() {
    var x = $("#kd_prov").val();
    var z = $("#outs").val();
    if (z == '' || z == 'undefined' || z == '#')
    {
    var l = 'abc';
    } else
    {
    var l = $("#outs").val();
    }
    var yuhu = '';
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/sum_usulan') ?>/" + x + "/" + l,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            alert(data);
            yuhu = data;

            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    alert(yuhu);
    if (parseInt($("#btspagu").text()) < yuhu)
    {
    var ale = "<div class='alert alert-danger'> <strong>Danger!</strong> Indicates a dangerous or potentially negative action.</div>";

            $("#colo").css({"color": "red"});
    } else if (parseInt($("#btspagu").text()) == 0 && yuhu == 0)
    {
    $("#conte").css({"display": "none"});
    $("#alertss").css({"display": "none"});
    }
    else
    {
    var ale = "<div class='alert alert-success'> <strong>Success!</strong> Indicates a successful or positive action.</div>";
            $("#colo").css({"color": "green"});
    }
    $("#alertss").empty();
    setTimeout(function () {
    $("#alertss").append(ale);
    }, 2000);
    var tlist_paket = $('#tlist_paket').DataTable();
    tlist_paket.ajax.reload();
    }


    function dataTablepaket() {

    tlist_paket = $("#tlist_paket").DataTable({
                  //  "initComplete": function(settings, json) {
                 //alert( 'DataTables has finished its initialisation.' );
//                 $("#tlist_paket").wrap( "<div class='double-scroll-tlist-paket'></div>" );
//                 $('.double-scroll-tlist-paket').doubleScroll();
//                 $('#tlist_paket').doubleScroll({
//                            resetOnWindowResize: true
//                 });
                 //alert("99999");
            // },
            // "scrollY":600,
            // "scrollX":true,
            "draw": 0,
            "fixedHeader": true,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "deferRender": true,
            "ajax": {
            url: "<?php echo base_url(); ?>pagu_tahapan/ssp_paket",
                    type: "POST",
                    "iTotalRecords":  8500,
                    "iTotalDisplayRecords": 5,
                    "data": function (d) {
                      d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>",
                      d.konfig_tahapan = kd_tahapan
                    }

            },
            "aoColumnDefs": [
            {
            "aTargets": [0],
                    "mRender": function (data, type, full) {
                            return "<img src='<?php echo base_url();?>assets/img/details_open.png'>";
                    }
            },            {
            "aTargets": [1],
                    "mRender": function (data, type, full) {
                            return full[0];
                    }
            },            {
            "aTargets": [2],
                    "mRender": function (data, type, full) {
                        return full[2];
                    }
            },            {
            "aTargets": [3],
                    "mRender": function (data, type, full) {
                        return full[3];
                    }
            },            {
            "aTargets": [4],
                    "mRender": function (data, type, full) {
                        return full[4];
                    }
            },            {
            "aTargets": [5],
                    "mRender": function (data, type, full) {
                            return full[5];
                    }
            },            {
            "aTargets": [6],
                    "mRender": function (data, type, full) {
                    return full[6];
                    }
            },            {
            "aTargets": [7],
                    "mRender": function (data, type, full) {
                           return full[7];
                    }
            },
            // {
            //     "aTargets": [8],
            //         "mRender": function (data, type, full) {
            //                  return full[8];
            //         }
            // },
            {
             "aTargets": [8],
                        "mRender": function (data, type, full) {
//                            console.log(full);
//                            console.log(data);
                            var d = new Array();
                            var datas=full[8].split('~');
                            var datastat=datas[0]+"|0_0_0|0_0_0~"+datas[1];
//                            alert(datastat)
                            var aData = datastat.split('|');
                            var html = '';
                            aData.forEach(function (e) {
                                eData = e.split('_');
//                            console.log(eData);
                                d.push(eData);
                            });
                            console.log(d);

//                        var aktor = '';

                            for (var i = 0; i < d.length; i++) {
                                aktor = full[31 + i];

                                console.log(aktor);

                                var formtype;
                                if (d[i][2] != '0') {
                                    formtype = 'add';
                                } else {
                                    formtype = 'edit';
                                }

                                switch (d[i][2]) {
                                    case '0': //belum diisi
                                        if (d[i][0] != '0') {
//                                            html += '<button onclick="btnBelumVerifikasi(' + "'" + idpaket + '~' + i + '~' + nmpaket + '~' + rolename + '~' + username + '~' + catatan1 + '~' + catatan2 + '~' + catatan3 + '~' + catatan4 + '~' + catatan5+ '~' + namasatker + '##' + data + "'" + ')" class="btn btn-default btn-xs">Belum verifikasi ' + aktor + '</button>\n';
                                            html += '<button onclick="btnProsesVerifikasi(' + "'" + datastat + "~" + i + "'" + ')" class="btn btn-default btn-xs">Belum verifikasi ' + aktor + '</button>\n';
                                        } else {

                                        }
                                        break;
                                    case '1': //diterima
                                        html += '<button onclick="btnProsesVerifikasi(' + "'" + datastat + "~" + i + "'" + ')" class="btn btn-success btn-xs">Diterima ' + aktor + '</button>\n';
                                        break;
                                    case '2': //reject
                                        d[i + 1][0] = '0'
                                        html += '<button onclick="btnProsesVerifikasi(' + "'" + datastat + "~" + i + "'" + ')" class="btn btn-danger btn-xs">Belum bisa dilaksanakan</button>\n';
                                        break;
                                    case '4': //hold
                                        d[i + 1][0] = '0'
                                        html += '<button onclick="btnProsesVerifikasi(' + "'" + datastat + "~" + i + "'" + ')" class="btn btn-primary btn-xs">Dihold ' + aktor + '</button>\n';
                                        break;
                                    case '3': //stock
                                        d[i + 1][0] = '0'
                                        html += '<button onclick="btnProsesVerifikasi(' + "'" + datastat + "~" + i + "'" + ')" class="btn btn-info btn-xs">Distock ' + aktor + '</button>\n';
                                        break;
                                }
                            }

                            return html;
                        }
            },
            {
            "aTargets": [9],
                    "mRender": function (data, type, row) {

                    var id = row[0];
                    var kdgiat = row[9];
                        var hasi='';
                          if(kd_tahapan==kdtahap)
                               {
                                   var hasi='display:block';
                               }else
                                   {
                                       var hasi='display:none';
                                   }

                    var html_button = [
//                            "<button onclick=upload_attachment('" + row[0] + "'); class='btn btn-success btn-xs' data-toggle='tooltip' title='Upload File'>",
//                            "<i class='fa fa-upload'>",
//                            "</i>",
//                            "</button>",
                            "<button onclick=download_attachment('" + row[0] + "'); class='btn btn-warning btn-xs' data-toggle='tooltip' title='List Attachment' style='"+hasi+"'> ",
                            "<i class='fa fa-file'>",
                            "</i>",
                            "</button>"
                            ,
                            "<button onclick=dtTambahRowDetail('" + id + "','" + kdgiat + "') class='btn btn-success btn-xs' data-toggle='tooltip' title='Tambah Detail Paket' style='"+hasi+"'>",
                            "<i class='fa fa-plus'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtEditRowPaket('" + id + "') class='btn btn-primary btn-xs' data-toggle='tooltip' title='Edit Paket' style='"+hasi+"'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtDeleteRow('paket','" + row[0] + "') class='btn btn-danger btn-xs' data-toggle='tooltip' title='Hapus Paket' style='"+hasi+"'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtHistory('paket','" + id + "') class='btn btn-default btn-xs'>History",
                            "</button>"
                    ].join("\n");
                    return html_button;
                    }
            }
            ],
            "order": [[15, "desc"]],
            "autoWidth": false,
            "columns": [
            {"className": 'details-control', "width": "auto"},
            {"width": "300px"},
            {"width": "5px"},
            {"width": "125px"},
            {"width": "120px"},
            {"width": "100px"},
            {"width": "100px"},
            {"width": "100px"},
            {"width": "100px"},
            {"width": "100px"},
            {"width": "125px"}
            ],
            "order": [[0, "desc"]],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "language": {
            "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "Memuat...",
                    "processing": "Memroses...",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                            "last": "<i class='fa fa-angle-double-right'></i>",
                            "next": "<i class='fa fa-angle-right'></i>",
                            "previous": "<i class='fa fa-angle-left'></i>"
                    },
                    "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                            "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
            }
    });

    $('#tlist_paket tbody').on('click', 'td.details-control img', function () {
    TableHtml = $("#tlist_detail").html();
    var tr = $(this).closest('tr');
    var row = tlist_paket.row(tr);
    var drow = row.data();

    if (row.child.isShown()) {
    // This row is already open - close it
    this.src = "<?php echo base_url();?>assets/img/details_open.png";
    row.child.hide();
    tr.removeClass('shown');
    } else {
    // Open this row

    this.src = "<?php echo base_url();?>assets/img/details_close.png";
    row.child(fnFormatDetails(iTableCounter, TableHtml)).show();
    alert(":ASdsa")
    oInnerTable = $("#tlist_detail_" + iTableCounter).DataTable({
        "createdRow": function ( row, data, index ) {
            $('td', row).eq(0).empty();

            var tags;
            if (data[0] == '')
            {
               tags = [];
            }
            else
            {
               tags = JSON.parse(data[0]);
            }
            if(tags==null)
            {
                tags=[];
            }
            var html = '';

            var i;
            for (i = 0; i < tags.length; i++) {
                 var vText = tags[i].text;
                 var vRujuk = vText.split("|")[0];

                  switch (vRujuk) {
                    case 'SIPRO':
                            html += '<span class="tag label label-primary">' + vText + '</span><br/>';
                    break;
                    case 'PEMDA':
                            html += '<span class="tag label label-default">' + vText + '</span><br/>';
                    break;
                    case 'DPR':
                            html += '<span class="tag label label-info">' + vText + '</span><br/>';
                    break;

                    case 'IRMS':
                            html += '<span class="tag label label-success">' + vText + '</span><br/>';
                    break;
                    case 'RAMS':
                            html += '<span class="tag label label-info">' + vText + '</span><br/>';
                    break;
                    case 'EPROG':
                            html += '<span class="tag label label-warning">' + vText + '</span><br/>';
                    break;
                    case 'RENSTRA':
                            html += '<span class="tag label label-danger">' + vText + '</span><br/>';
                    break;

                     }

            }

            //console.log(tags);
            $('td', row).eq(0).html(html);



    },
          "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "scrollCollapse": true,
            "searching": false
            "ajax": {
            url: "<?php echo base_url(); ?>pagu_tahapan/ssp_detail",
                    type: "POST",
                    data: function (d) {
                    d.kdsatker = kd_satker;
                    d.thang = drow[1];
                    d.kd_sub_komponen = drow[2].split(" - ")[0];
                    d.kd_komponen = drow[6].split(" - ")[0];
                    d.kd_giat = drow[3].split(" - ")[0];
                    d.kd_output = drow[4].split(" - ")[0];
                    d.kd_sub_output = drow[5].split(" - ")[0];
                    d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    d.konfig_tahapan = kd_tahapan
                    }
            },
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8]}],
            "aoColumnDefs": [

            {
            "aTargets": [1],
            "mRender": function (data, type, row) {
            return row[1]
                }
//                    "width": "20px",
//                    "visible": true
            },

            {
            "aTargets": [2],
//                    "width": "100px",
//                    "visible": true
            "mRender": function (data, type, row) {
            return row[4]
                }
            },
            {
            "aTargets": [3],
            "mRender": function (data, type, row) {
            return row[25]
                }
            },

            {
            "aTargets": [4],
            "mRender": function (data, type, row) {
            return row[15]
                }
            },
            {
            "aTargets": [5],
            "mRender": function (data, type, row) {
            return row[12]
                }
            },

            {
            "aTargets": [6],
//                    "width": "100px",
//                    "visible": true
            "mRender": function (data, type, row) {
            return row[13]
                }
            },
            {
            "aTargets": [7],
//                    "width": "100px",
//                    "visible": true
            "mRender": function (data, type, row) {
            return row[16]
                }
            },

            {
            "aTargets": [8],
                    "mRender": function (data, type, row) {
                if(kd_tahapan==kdtahap)
                               {
                                   var hasi='display:block';
                                   var hasied='display:block';
                               }else
                                   {
                                     if(kd_tahapan=='PAA')
                                     {
                                        var hasi='display:none';
                                       if(kdtahap=='KO')
                                       {
                                        var hasied='display:block';
                                       }else
                                       {
                                        var hasied='display:none';
                                       }
                                     }else{

                                       var hasi='display:none';
                                     }
                                   }
                    var id = row[1];
                    var html_button = [
                            "</button>",
                            "<button onclick= dtDeleteRow('detail','" + id + "') class='btn btn-danger btn-xs'   style='"+hasi+"'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtHistory('detail','" + id + "') class='btn btn-warning btn-xs' >History",
                            "</button>",
                            "<button onclick=editDetail('" + id + "') class='btn btn-dark btn-xs'   style='"+hasied+"'><i class='fa fa-bars'></i>",
                            "</button>"
                    ].join("\n");
                    return html_button;
                    }
            }
            ],
            "order": [[1, "asc"]],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "language": {
            "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "loadingRecords": "Memuat...",
                    "processing": "Memroses...",
                    // "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                            "last": "<i class='fa fa-angle-double-right'></i>",
                            "next": "<i class='fa fa-angle-right'></i>",
                            "previous": "<i class='fa fa-angle-left'></i>"
                    },
                    "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                            "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
            }
    });
    $("#tlist_detail_" + iTableCounter).on('xhr', function () {
    xhrdata = $("#tlist_detail_" + iTableCounter).ajax.json();
    });
    $("#tlist_detail_" + iTableCounter).addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
    // $('#tlist_detail_'+iTableCounter+'_length').empty()
    //
    // var se='<label>Cari:<input type="search" class="form-control" placeholder="" aria-controls="tlist_detail_'+iTableCounter+'"></label>'
    // $('#tlist_detail_'+iTableCounter+'_length').html(se)
    //   $('#tlist_detail_1_filter').empty()
    iTableCounter = iTableCounter + 1;

    tr.addClass('shown');

    }

    });
    tlist_paket.on('xhr', function () {
    xhrdata = tlist_paket.ajax.json();
    });

    }

    var addFormGroupEdit = function (event) {

    event.preventDefault();
    var $formGroup = $(this).closest('.form-group');
    var $multipleFormGroup = $formGroup.closest('.multiple-form-group.input-group.input-group-edit-detail');
    var $formGroupClone = $formGroup.clone();
    $(this).toggleClass('btn-success btn-add-sda btn-danger btn-remove-sda').html('â€“');
    $formGroupClone.find('input').val('');
    $formGroupClone.find('.concept').text('RM');
    $formGroupClone.insertAfter($formGroup);
    var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
    if ($multipleFormGroup.data('max') <= countFormGroup($multipleFormGroup)) {
    $lastFormGroupLast.find('.btn-add-sda').attr('disabled', true);
    }



    $(".mdl-edit-detail .valuesdana").each(function(index) {

    this.addEventListener('keyup', function(e){
    var rm_value = $('#wrm').val();
    if (this.value != ""){

    var current_value = this.value;
    this.value = numberWithCommasSDA(this.value);
    setTimeout(function(){

    var bulatrm = parseInt(rm_value.replace(/,/g, ''), 10);
    var bulatcurr = parseInt(current_value.replace(/,/g, ''));
    var result = bulatrm - bulatcurr;
    console.log(result);
    $("#wrm").val(numberWithCommasSDA(result.toString()));
    }, 3000);
    }

    if (this.id != "wrm"){
    console.log("limit money");
    var rm_value = $("#wrm").val();

    if (parseInt(this.value.replace(/,/g, '')) > parseInt(rm_value.replace(/,/g, ''))){
    alert("Nilai tidak boleh lebih dari RM");
    this.value = "";
    } else{

    }
    }

    });

    });
    };
    var removeFormGroupEdit = function (event) {
    event.preventDefault();
    var $formGroup = $(this).closest('.form-group');
    var $multipleFormGroup = $formGroup.closest('.multiple-form-group.input-group.input-group-edit-detail');
    var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
    if ($multipleFormGroup.data('max') >= countFormGroup($multipleFormGroup)) {
    $lastFormGroupLast.find('.btn-add-sda').attr('disabled', false);
    }

    $formGroup.remove();
    };
 var old_rm=0;
     var addFormGroup = function (event) {
         //alert("add form group tambah paket");
            event.preventDefault();
            var $formGroup = $(this).closest('.form-group');
            var $multipleFormGroup = $formGroup.closest('.multiple-form-group');
            var $formGroupClone = $formGroup.clone();
            $(this).toggleClass('btn-success btn-add btn-success btn-add').html('+');
            $formGroupClone.find('input').val('');
            $formGroupClone.find('.concept').text('RM');
            $formGroupClone.insertAfter($formGroup);
            var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
            if ($multipleFormGroup.data('max') <= countFormGroup($multipleFormGroup))
            {
                $lastFormGroupLast.find('.btn-add').attr('disabled', true);
            }

             $(".ctk-tb-detail .valuesdana").each(function( index ) {
                this.addEventListener('keyup',function(e){
                 console.clear();
                 console.log("keyCode");
                 console.log(e.which);
                 var rm_value = $('#wrm').val();
                 if(this.value != ""){
                    var current_value=this.value;
                    this.value=numberWithCommasSDA(this.value);

                    setTimeout(function(){

                        var bulatrm = parseInt(rm_value.replace(/,/g, ''), 10);

                        var bulatcurr = parseInt(current_value.replace(/,/g, ''));

                        var result = bulatrm - bulatcurr;
                        //console.log(result);
                        $("#wrm").val(numberWithCommasSDA(result.toString()));
                    }, 3000);

                    //alert("PPPPPPP");
                 }
                   var sdaItem=document.getElementById(this.id);
                   //console.log("index "+this.id+"="+$(".tb-detail").index(sdaItem));
                   if($(".tb-detail").index(sdaItem) !=0){

                        var rm_value=$("#yrm").val();
                        if(parseInt(this.value.replace(/,/g, '')) > parseInt(rm_value.replace(/,/g, ''))){
                            alert("Nilai tidak boleh lebih dari RM");
                            $("#yrm").val(this.value+$("#yrm").val());
                            this.value="";
                        }else{
                            console.log("werttdydrydyd");
                        }
                    }

                });



            });

            //modal tambah paket
             var xtemp=0;
             $.fn.getCursorPosition = function() {
                    var el = $(this).get(0);
                    var pos = 0;
                    var posEnd = 0;
                    if('selectionStart' in el) {
                        pos = el.selectionStart;
                        posEnd = el.selectionEnd;
                    } else if('selection' in document) {
                        el.focus();
                        var Sel = document.selection.createRange();
                        var SelLength = document.selection.createRange().text.length;
                        Sel.moveStart('character', -el.value.length);
                        pos = Sel.text.length - SelLength;
                        posEnd = Sel.text.length;
                    }
                    return [pos, posEnd];
                };

             $(".mdl-tbh-paket .valuesdana").each(function( index ) {
                this.addEventListener('keyup',function(e){

                var position = $(this).getCursorPosition();
                var deleted = '';
                var val = $(this).val();
                 console.log("a1");
                if(e.which !='8'){//disable autocount keyup backspace

//                   setTimeout(function(){
//                        var focus_val=$(":focus").val();
//                        var xrmz     =$("#xrm").val();
//
//                        $("#xrm").val(parseInt(xtemp) + parseInt(xrmz.replace(/,/g, '')));
//
//                   }, 3000);
//


                console.log("a2");
                 if(this.value != ""){
                    console.log("a3");
                    var current_value=this.value;
                    this.value=numberWithCommasSDA(this.value);
                    setTimeout(function(){

                        var bulatrm = parseInt(rm_value.replace(/,/g, ''), 10);

                        var bulatcurr = parseInt(current_value.replace(/,/g, ''));

                        var result = bulatrm - bulatcurr;
                        //console.log(result);
                        $("#xrm").val(numberWithCommasSDA(result.toString()));
                    }, 3000);
                    //alert("PPPPPPP");
                 }
                   var sdaItem=$(":focus");
                    console.log("a");
                   //console.log("index "+this.id+"="+$(".tb-master").index(sdaItem));
                   if($(".tb-master").index(sdaItem) !=0){
                       console.log("b");
                        var rm_value=$("#xrm").val();
                        if(parseInt(this.value.replace(/,/g, '')) > parseInt(rm_value.replace(/,/g, ''))){
                            $(":focus").val($(":focus").val().substring(0,$(":focus").val().length - 1));
                            $(":focus").val("");
                            $(":focus").blur()
                            old_rm=$("#xrm").val();

                            setTimeout(function(){
                            $("#xrm").val(old_rm);
                            old_rm=0;
                              },3000);
                              alert("Tidak boleh melebihi RM");

                        }

                        //if(this.value.replace(/,/g, ''))
                    }

                }else{
                   if (position[0] == position[1]) {
                    if (position[0] == 0)
                        deleted = '';
                    else
                        deleted = val.substr(position[0] - 1, 1);
                        }
                    else {
                        deleted = val.substring(position[0], position[1]);
                   }
                    output(deleted);
                    deleted_characters= deleted_characters+deleted.toString();

                    this.addEventListener('blur',function(e){
                         var remains=this.value;
                         //alert(deleted_characters);
                         var new_number=remains + deleted_characters;
                         //alert(new_number);
                    });
                }

                });



//                this.addEventListener('change',function(e){
//                     var sdaItem=$(":focus");
//
//                     if($(".tb-detail").index(sdaItem) !=0){
//                         var rm_value= $("#xrm").val();
//                         var current_value=this.value;
//                         var result= parseInt(rm_value.replace(/,/g, ''))-parseInt(current_value.replace(/,/g, ''));
//
//                          result=Math.ceil(result);
//                          console.log("Nilai RM MD tambah =>"+result);
//                          $("#xrm").val(numberWithCommasSDA(result.toString()));
//
//                     }
//
//                });
            });
//            $(".tb-master:eq(0)").attr("readonly",true)
//            $(".tb-detail:eq(0)").attr("readonly",true)
        };
    $(".tb-master:eq(0)").keyup(function(){

    var current_value = this.value;
    this.value = numberWithCommasSDA(this.value);
    });
    var removeFormGroup = function (event) {
    event.preventDefault();
    var $formGroup = $(this).closest('.form-group');
    var $multipleFormGroup = $formGroup.closest('.multiple-form-group');
    var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
    if ($multipleFormGroup.data('max') >= countFormGroup($multipleFormGroup)) {
    $lastFormGroupLast.find('.btn-add').attr('disabled', false);
    }

    $formGroup.remove();
    };
    var selectFormGroup = function (event) {
    event.preventDefault();
    var $selectGroup = $(this).closest('.input-group_select');
    var param = $(this).attr("href").replace("#", "");
    var concept = $(this).text();


    $selectGroup.find('.concept').text(concept);
    $selectGroup.find('.input-group_select-val').val(param);
    var lowParam = param.toLowerCase();
    var $selectEntryDana = $(this).closest('.input-group');
    $selectEntryDana.find('.valuesdana').attr("id", lowParam);
    $selectEntryDana.find('.valuesdana').attr("name", lowParam);
    };

    var countFormGroup = function ($form) {
    return $form.find('.form-group').length;
    };

    var selectFormGroups = function (event) {
    event.preventDefault();
    var $selectGroup = $(this).closest('.input-group_select');
    var param = $(this).attr("href").replace("#", "");
    var concept = $(this).text();
    if($("#xrm").val()=='' && $("#wrm").val()==''){
        var k='detail';
        var ii='2';
    }else if($("#yrm").val()=='' && $("#wrm").val()==''){
        var k='paket';
        var ii='1';
    }else if($("#yrm").val()=='' && $("#xrm").val()==''){
        var k='edit';
        var ii='3';
    }


    $selectGroup.find('.concept').text(concept);
    $selectGroup.find('.input-group_select-val').val(param);
    var lowParam = param.toLowerCase();
    var $selectEntryDana = $(this).closest('.input-group');
    $selectEntryDana.find('.valuesdanas').attr("id", lowParam+ii);
    $selectEntryDana.find('.valuesdanas').attr("name", lowParam);
    $selectEntryDana.find('.valuesdanas').attr("onkeyup", "hitung('"+lowParam+ii+"','"+k+"')");
    };

    var countFormGroup = function ($form) {
    return $form.find('.form-group').length;
    };

     function usulanpemda(idx){
    var tlist_usulanpemda = $("#tusulanpemda"+idx).DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_tahapan/ssp_usulanpemda"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        var statement_tag = 'PEMDA::' + id;

                        if(idx==='1')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='2')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='3')
                        {
                            var html_button = '<button onclick="dtTaggingTbhDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        else if(idx==='4')
                        {
                            var html_button = '<button onclick="dtTaggingEditDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        var html_buttons = '<button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModals" onclick="dtfollowup(' + "'" + id + "'" + ')">Tindak Lanjut</button>';
                        return html_button +""+html_buttons;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulanpemda.on('xhr', function () {
            xhrdatausulanpemda = tlist_usulanpemda.ajax.json();
        });
    }
    function usulandpr(idx){
      var tlist_usulandpr = $("#tusulandpr"+idx).DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_tahapan/ssp_usulandpr",
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        var statement_tag = 'DPR::' + id;

                        if(idx==='1')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='2')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='3')
                        {
                            var html_button = '<button onclick="dtTaggingTbhDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }

                    else if(idx==='4')
                        {
                            var html_button = '<button onclick="dtTaggingEditDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        var html_buttons = '<button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModals" onclick="dtfollowup(' + "'" + id + "'" + ')">Tindak Lanjut</button>';
                        return html_button +""+html_buttons;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulandpr.on('xhr', function () {
            xhrdatausulandpr = tlist_usulandpr.ajax.json();
        });
    }
    function renstra(idx){
     var tlist_renstra = $("#trenstra"+idx).DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_tahapan/ssp_renstra"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [1],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [6],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        var nilai = row[3];
                        var satuan = row[4];
                        var tahun = row[5];
                        var statement_tag = 'RENSTRA::' + id;


                    if(idx==='1')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='2')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='3')
                        {
                            var html_button = '<button onclick="dtTaggingTbhDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        else if(idx==='4')
                        {
                            var html_button = '<button onclick="dtTaggingEditDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        var html_buttons = '<button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModals" onclick="dtfollowup(' + "'" + id + "'" + ')">Tindak Lanjut</button>';
                        return html_button +""+html_buttons;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_renstra.on('xhr', function () {
            xhrdatarenstra = tlist_renstra.ajax.json();
        });
    }

    function irms(idx) {
        var r = '';
        var staax = '';
        var staex = '';
        if (idx == 2)
        {
            r = $('#xid_ruas').val()
            staax = $('#sta_awal').val()
            staex = $('#sta_akhir').val()
        } else if (idx == 3)
        {
            r = $('#id_ruas').val()
            staax = $('#sta_awal_sel').val()
            staex = $('#sta_akhir_sel').val()
        } else if (idx == 4)
        {
            r = $('#wid_ruas').val()
            staax = $('#wsta_awal').val()
            staex = $('#wsta_akhir').val()
        }
        var tirmsv3_wp = $("#tirmsv3_wp" + idx).DataTable({
            "draw": 0,
            "responsive": true,
            "searching": false,
            "ajax": {
              //  url: "<? php echo base_url(); ?>pagu_anggaran/pavlist",
                url: "<?php echo base_url(); ?>pagu_tahapan/get_irmsv3_rujukan", //irmsservice
                type: "POST",
                //async: false,
                data: function (d) {
                    d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    d.noruas = r;
                    d.staa = staax;
                    d.stae = staex;

                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [12],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var thn = row[1];
                        var kat = row[2];
                        var road = row[5];
                        var statement_tag = 'IRMS::' + id;
                        if (idx === '1')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        } else if (idx === '2')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        } else if (idx === '3')
                        {
                            var html_button = '<button onclick="dtTaggingTbhDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        } else if (idx === '4')
                        {
                            var html_button = '<button onclick="dtTaggingEditDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        var html_buttons = '<button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModals" onclick="dtfollowup(' + "'" + id + "'" + ')">Tindak Lanjut</button>';
                        return html_button +""+html_buttons;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {data: "unique_id", "width": "5px"},
                {data: "pms_treatment_name", "width": "120px"},
                {data: "treatment_cost", "width": "120px"},
                {data: "pms_budget_cat_id", "width": "120px"},
                {data: "scn_year_num", "width": "100px"},
                {data: "route_name", "width": "100px"},
                {data: "length", "width": "40px"},
                {data: "lane_dir_name", "width": "100px"},
                //{data: "lane_id_name", "width": "100px"},
                {data: "offset_from","width": "100px"},
                {data: "offset_to","width": "100px"},
                {data: "bm_region", "width": "100px"},
                {data: "bm_province", "width": "100px"},
                {"width": "5px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}],
            "pageLength": 10,
            "lengthMenu": [[10, 15, 20], [10, 15, 20]],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tirmsv3_wp.on('xhr', function () {
            xhrdatairms = tirmsv3_wp.ajax.json();
        });
    }

    function eprograms(idx){
     var teprogram = $("#teprogram"+idx).DataTable({
            "draw": 0,
            "responsive": true,
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_tahapan/get_eprogram",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                type: "POST"
            },
            "autoWidth": false,
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,17]}],
            "columns": [
                {data: "title", "width": "20px"},
                {data: "nama_skenario", "width": "100px"},
                {data: "prioritas", "width": "100px"},
                {data: "rp_1", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_2", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_3", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_4", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_5", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_6", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "length_1", "width": "100px"},
                {data: "length_2", "width": "100px"},
                {data: "length_3", "width": "100px"},
                {data: "length_4", "width": "100px"},
                {data: "length_5", "width": "100px"},
                {data: "length_6", "width": "100px"},
                {data: "benefit", "width": "100px"},
                {data: "id_skenario", "width": "100px"},
                {data: "gid", "width": "100px"},
                {"width": "100px"}
            ],
            "aoColumnDefs": [
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row.gid;
                        var scene = row.nama_skenario;
                        var judul = row.title;
                        var statement_tag = 'EPROG::' + id;

                        if(idx==='1')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='2')
                        {
                            var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        }
                        else if(idx==='3')
                        {
                            var html_button = '<button onclick="dtTaggingTbhDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        else if(idx==='4')
                        {
                            var html_button = '<button onclick="dtTaggingEditDetail(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';

                        }
                        var html_buttons = '<button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModals" onclick="dtfollowup(' + "'" + id + "'" + ')">Tindak Lanjut</button>';
                        return html_button +""+html_buttons;
                    }
                }
            ],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "language": {
                "decimal": "",
//                "thousands": ".",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        teprogram.on('xhr', function () {
            xhrdataeprogram = teprogram.ajax.json();
        });
    }

    function sipro(id)
    {
                var tsipro_wp = $("#tsipro_wp"+id).DataTable({
            'fnCreatedRow': function (nRow, aData, iDataIndex) {
                //alert("Row=> "+iDataIndex);
                $(nRow).attr('id', 'row-' + iDataIndex); // or whatever you choose to set as the id
                $('td', nRow).eq(-1).empty();
                $('td', nRow).eq(-1).append('<button class="btn btn-primary btn-xs btn-primary" id=cbx-' + iDataIndex + ' onclick="add_to_chart(' + "'" + aData[0] + "'" + "," + "'" + iDataIndex + "'" + ');">Paketkan</button><button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModals" onclick="dtfollowup(' + "'" + aData[38] + "'" + ')">Tindak Lanjut</button>');


            },
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "searching": true,
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_tahapan/siprolist_pagu"
            },
//            "aoColumnDefs": [
//                {
//                    "aTargets": [-1],
//                    "visible":true,
//                    "mRender": function (data, type, row) {
//
////                        var id = row[12];
////                        //alert(id);
////                        var judul = row[5];
////                        //var statement_tag = 'SIPRO::' + id + '::' + judul;
////                        var statement_tag = 'SIPRO::' + id;
////
////                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-primary btn-xs">Tandai</button>';
//        var html_button="";
//        return html_button;
//                    }
//                }
//            ],
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false}, //11
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "50px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "20px"},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tsipro_wp.on('xhr', function () {
            xhrdatasipro = tsipro_wp.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


    }


    //------------------RUJUKAN_------------------------------   //
    function tabsipro(id){
                var tab=$('#tsipro_wp'+id).DataTable();
            tab.destroy()
             sipro(id);
    }
        function tabirms(id){
                var tab=$('#tirmsv3_wp'+id).DataTable();
            tab.destroy()
             irms(id);
    }
    function tabprogram(id){
                var tab=$('#teprogram'+id).DataTable();
            tab.destroy()
             eprograms(id);
    }
    function tabrenstra(id){
                var tab=$('#trenstra'+id).DataTable();
            tab.destroy()
             renstra(id);
    }
    function tabdpr(id){
                var tab=$('#tusulandpr'+id).DataTable();
            tab.destroy()
             usulandpr(id);
    }
    function tabpemda(id){
                var tab=$('#tusulanpemda'+id).DataTable();
            tab.destroy()
             usulanpemda(id);
    }
    ////-----------------END RUJUKAN-----------------//


    function dtfollowup(id)
{


    var str_src = "<?php echo base_url('/paket_muasal?id='); ?>"+id;
    $("#iframefollow").attr("src", str_src);
    $("#modal-follow").modal("show");

}

  function testing(ii){
   if (isNaN(parseInt($('#pln'+ii).val()))) {
            var b = 0
        } else {
             $('#pln'+ii).val(formatRupiah($('#pln'+ii).val()))
            var b = parseInt($('#pln'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#rmp'+ii).val()))) {
            var c = 0
        } else {
             $('#rmp'+ii).val(formatRupiah($('#rmp'+ii).val()))
            var c = parseInt($('#rmp'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#pnbp'+ii).val()))) {
            var d = 0
        } else {
             $('#pnbp'+ii).val(formatRupiah($('#pnbp'+ii).val()))
            var d = parseInt($('#pnbp'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#pdn'+ii).val()))) {
            var e = 0
        } else {
             $('#pdn'+ii).val(formatRupiah($('#pdn').val()))
            var e = parseInt($('#pdn'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#blu'+ii).val()))) {
            var f = 0
        } else {
             $('#blu'+ii).val(formatRupiah($('#blu'+ii).val()))
            var f = parseInt($('#blu'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#sbsn'+ii).val()))) {
            var g = 0
        } else {
            $('#sbsn'+ii).val(formatRupiah($('#sbsn'+ii).val()))
            var g = parseInt($('#sbsn'+ii).val().replace(/\,/g, ""))
        }

    }

     function hitung(idd,key) {
//         alert($('#sbsn').val());

        if(key=='paket')
           {
               var ii='1';
           }else if(key=='detail')
           {
                 var ii='2';
           }else if(key=='edit')
           {
              var ii='3';
           }
if (isNaN(parseInt($('#pln'+ii).val()))) {
            var b = 0
        } else {
             $('#pln'+ii).val(formatRupiah($('#pln'+ii).val()))
            var b = parseInt($('#pln'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#rmp'+ii).val()))) {
            var c = 0
        } else {
             $('#rmp'+ii).val(formatRupiah($('#rmp'+ii).val()))
            var c = parseInt($('#rmp'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#pnbp'+ii).val()))) {
            var d = 0
        } else {
             $('#pnbp'+ii).val(formatRupiah($('#pnbp'+ii).val()))
            var d = parseInt($('#pnbp'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#pdn'+ii).val()))) {
            var e = 0
        } else {
             $('#pdn'+ii).val(formatRupiah($('#pdn'+ii).val()))
            var e = parseInt($('#pdn'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#blu'+ii).val()))) {
            var f = 0
        } else {
             $('#blu'+ii).val(formatRupiah($('#blu'+ii).val()))
            var f = parseInt($('#blu'+ii).val().replace(/\,/g, ""))
        }
        if (isNaN(parseInt($('#sbsn'+ii).val()))) {
            var g = 0
        } else {
            $('#sbsn'+ii).val(formatRupiah($('#sbsn'+ii).val()))
            var g = parseInt($('#sbsn'+ii).val().replace(/\,/g, ""))
        }


        if (isNaN(b)) {
            var k = 0;
        } else {
            var k = b;
        }
        if (isNaN(c)) {
            var l = 0;
        } else {
            var l = c;
        }
        if (isNaN(d)) {
            var m = 0;
        } else {
            var m = d;
        }
        if (isNaN(e)) {
            var n = 0;
        } else {
            var n = e;
        }
        if (isNaN(f)) {
            var o = 0;
        } else {
            var o = f;
        }
        if (isNaN(g)) {
            var p = 0;
        } else {
            var p = g;
        }
          var nilai = $('#'+idd).val();
        var pan = nilai.length - 1;
        var hasil =  k + l + m + n + o + p ;

           if(key=='paket')
           {
               var xx=  $("#xrms").val($("#xrm").val());
                var x = parseInt($('#xjumlah').val().replace(/\,/g, ""))
                var hasilfix=x-hasil;
                 var haz =$("#xrm").val(rubah(hasilfix));
           }else if(key=='detail')
           {
                var xx=  $("#yrms").val($("#yrm").val());
                  var x = parseInt($('#yjumlah').val().replace(/\,/g, ""))
                  var hasilfix=x-hasil;

                var haz =$("#yrm").val(rubah(hasilfix));
           }else if(key=='edit')
           {
                var xx=  $("#wrms").val($("#wrm").val());
               var x = parseInt($('#wjumlah').val().replace(/\,/g, ""))
               var hasilfalse = parseInt($('#wrm').val().replace(/\,/g, ""))
               var hasilfix=x-hasil;
               var xx=$("#wrm").val(formatRupiah($("#wrm").val()));
                var haz =$("#wrm").val(rubah(hasilfix));
           }

        if(hasil > x)
        {
            if(key=='paket')
            {
                $("#xrm").val($("#xrms").val())
            }else if(key=='detail')
            {
                $("#yrm").val($("#yrms").val())
            }else if(key=='edit')
            {
                $("#wrm").val($("#wrms").val())
                   alert($("#wrms").val())
            }

              $('#'+idd).val(formatRupiah(nilai.substring(0, pan)))

            alert('Tidak boleh melebihi RM');


        }else
        {
        haz
        }

//        alert(rubah(hasil))


//        hitung()

    }
         function rubah(angka) {
        var reverse = angka.toString().split('').reverse().join(''),
                ribuan = reverse.match(/\d{1,3}/g);
        ribuan = ribuan.join(',').split('').reverse().join('');
        return ribuan;
    }

     function droppak(){
      multipleSelects('kdsdana', 38);

    }















    $(document).ready(function () {

         $('#submit').submit(function (e) {
            e.preventDefault();
            var file = new FormData;
//            var id = $("#id_usulan").val();
           // console.log(id);
            $.ajax({
                url: '<?php echo base_url(); ?>pagu_tahapan/up',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    $("#judul").val('')
                    $("#nmfile").val('')
                    $("#kate").val("#");
                 var tab = $('#table_id2').DataTable();
                tab.ajax.reload();

                }
            });
        });


         bs_input_file();
                    $("#id_ruasss").change(function(){
             var x = $("#id_ruasss").val();
       var kd_ruas=x.split("::")[1];
       console.log(kd_ruas);
       var len=kd_ruas.length;
       if(len==5)
       {
           var v="0";
       }
       else if(len==6)
       {
           var v=kd_ruas.slice(5,6);
       }
       else if(len==7)
       {
           var v=kd_ruas.slice(5,7);
       }
       else if(len==8)
       {
            var v=kd_ruas.slice(6,8);
       }
       $("#suffix").val(v);

    })


//        		    $('#title').autocomplete({
//
////                        alert("asdasd");
//                source: "<?php echo base_url('pagu_tahapan/get_autocomplete');?>",
//
//                select: function (event, ui) {
//                    $('[name="title"]').val(ui.item.label);
//                 $( ".addresspicker" ).autocomplete( "option", "appendTo", ".eventInsForm" );
//                }
//
//            });


//        alert(prrr)
    refreshCombobox('kd_prov', 63, 'kdinduk', kd_satker);
    initCombobox('outs', 62);
    if (typeof (kd_satker) == 'undefined' || kd_satker == '' || kd_satker == null)
    {
    //do nothing

    } else
    {
    refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
    refreshCombobox('kdkppn_sel', 46, 'kdsatker', kd_satker);
    }

    var xkdprov = $("#prov").val();
    var id_rprov = $("#id_rprov").val();
    initCombobox('jnskontrak', 23);

    refreshCombobox('kabkot', 61, 'kd_prov', id_rprov);
    bind_wps_by_province();
    $(document).on('show.bs.modal', '.modal', function (event) {
    var zIndex = 1040 + (10 * $('.modal:visible').length);
    $(this).css('z-index', zIndex);
    setTimeout(function () {
    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
    }, 0);
    });
    $(document).on('hidden.bs.modal', '.modal', function () {
    $('.modal:visible').length && $(document.body).addClass('modal-open');
    });
    $('.rujuk').show();
    $(".content-sumber-dana").hide();
//    $(document).on('click', '.btn-add', addFormGroup);
//    $(document).on('click', '.btn-remove', removeFormGroup);
//    $(document).on('click', '.dropdown-menu a', selectFormGroups);
//    $(document).on('click', '.btn-add-sda', addFormGroupEdit);
//    $(document).on('click', '.btn-remove-sda', removeFormGroupEdit);
    initCombobox('kd_isu', 3);
    initCombobox('thang', 28);
    initCombobox('thang_sel', 28);
    initCombobox('kd_kegiatan', 5);
    if (typeof (kd_satker) == 'undefined' || kd_satker == '' || kd_satker == null)
    {
    //do nothing

    } else
    {
    refreshCombobox('id_ppk', 47, 'kdsatker', kd_satker);
    refreshCombobox('id_ppk_sel', 47, 'kdsatker', kd_satker);
    refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
    refreshCombobox('kdkppn_sel', 46, 'kdsatker', kd_satker);
    }

//    $("#thang").change(function () {
//    var thang = $('#thang').val();
//    $('#kd_output').empty();
//    $('#kd_output').append(new Option("--Pilih--",""));
//    $('#kd_sub_output').empty();
//    $('#kd_sub_output').append(new Option("--Pilih--",""));
//    $('#kd_komponen').empty();
//    $('#kd_komponen').append(new Option("--Pilih--",""));
//    initCombobox('kd_kegiatan', 5);
//    });
    if (id_user_group != 60)
            {

            $('#sta_awal_sel, #sta_akhir_sel').on('keyup', function () {
            var tab=$('#tirmsv3_wp').DataTable();
            tab.destroy();
            irms();

            $("#yhargasat").val("");
            $("#yjumlah").val("");
            $(".valuesdana").val("");
            var staa = $('#sta_awal_sel').val();
            var stae = $('#sta_akhir_sel').val();
            var volume = parseFloat((stae - staa) / 1000);
            $('#volume').val(Math.abs(volume));
            });
            //new codes
            $('#ylatitude, #ylongitude, #ylatitude2, #ylongitude2').on('keyup', function () {
                $("#yhargasat").val("");
                $("#yjumlah").val("");
                $(".valuesdana").val("");
                var staa = $('#sta_awal_sel').val();
                var stae = $('#sta_akhir_sel').val();
                var volume = parseFloat((stae - staa) / 1000);
                $('#volume').val(Math.abs(volume));
            });
            //modal tambah paket
            $('#sta_awal, #sta_akhir').on('keyup', function () {
//                     alert($("#xid_ruas").val())
//        alert($("#sta_awal").val())
//        alert($("#sta_akhir").val())

    var urutans=$("#urutans").val()
    var tab=$('#tirmsv3_wp'+urutans).DataTable();
    tab.destroy()
    irms(urutans);
                    var itirmsv3_wp = $('#tirmsv3_wp'+urutans).DataTable();
                itirmsv3_wp.ajax.reload();

            $("#xhargasat").val("");
            $("#xjumlah").val("");
            $(".tb-master").val("");
            var staa = $('#sta_awal').val();
            var stae = $('#sta_akhir').val();
            var volume = parseFloat((stae - staa) / 1000);
            var harga_satuan = $("#hargasat").val();
            $('#xvolume').val(Math.abs(volume));
            });
            //new codes
            $('#latitude, #longitude, #latitude2, #longitude2').on('keyup', function () {
                $("#xhargasat").val("");
                $("#xjumlah").val("");
                $(".tb-master").val("");
                var staa = $('#sta_awal').val();
                var stae = $('#sta_akhir').val();
                var volume = parseFloat((stae - staa) / 1000);
                var harga_satuan = $("#hargasat").val();
                $('#xvolume').val(Math.abs(volume));
            });
            //modal edit detail
            $('#wsta_awal, #wsta_akhir').on('keyup', function () {
    var urutans=$("#urutans").val()
    var tab=$('#tirmsv3_wp'+urutans).DataTable();
    tab.destroy()
    irms(urutans);
                    var itirmsv3_wp = $('#tirmsv3_wp'+urutans).DataTable();
    itirmsv3_wp.ajax.reload();
            $("#whargasat").val("");
            $("#wjumlah").val("");
            $(".valuesdana").val("");
            var staa = $('#wsta_awal').val();
            var stae = $('#wsta_akhir').val();
            var volume = parseFloat((stae - staa) / 1000);
            $('#wvolume').val(Math.abs(volume));
            });
            //new codes
            $('#wlatitude, #wlongitude, #wlatitude2, #wlongitude2').on('keyup', function () {
                $("#whargasat").val("");
                $("#wjumlah").val("");
                $(".valuesdana").val("");
                var staa = $('#wsta_awal').val();
                var stae = $('#wsta_akhir').val();
                var volume = parseFloat((stae - staa) / 1000);
                $('#wvolume').val(Math.abs(volume));
            });

            dataTablepaket();
            }
    else
    {
    dataTablepaket();
    }

    $("#tlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");

    $('#btnSetuju').click(function () {
    console.log('stuju');
    var wdata = way.get('formData');
    wdata.status = 1;
    console.log(wdata);
    var mode = wdata.modeform;
    var url;
    url = "<?php echo base_url(); ?>/pagu_tahapan/addform";
    var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
            .done(function (data) {
            tlist_paket.ajax.reload();
            $("#alert-content").append(" <p>Insert data suksess");
            $("#alert_information").css({display: "block"});
            })
            .fail(function () {
            alert("error");
            })
            .always(function () {
            });
    $("#modal-edit").modal("hide");
    });
    $('#btnTolak').click(function () {
    console.log('tolak');
    var wdata = way.get('formData');
    wdata.status = 2;
    var mode = wdata.modeform;
    var url;
    url = "<?php echo base_url(); ?>/pagu_tahapan/addform";
    var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
            .done(function (data) {
            tlist_paket.ajax.reload();
            $("#alert-content").append(" <p>Insert data suksess");
            $("#alert_information").css({display: "block"});
            })
            .fail(function () {
            alert("error");
            })
            .always(function () {
            });
    $("#modal-edit").modal("hide");
    });
    $('#btnTutupView').click(function () {
    $('#modal-view').modal('hide');
    });
    $('#btnHold').click(function () {
    console.log('hold');
    var wdata = way.get('formData');
    wdata.status = 3;
    console.log(wdata);
    var mode = wdata.modeform;
    var url;
    url = "<?php echo base_url(); ?>/pagu_tahapan/addform";
    var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
            .done(function (data) {
            tlist_paket.ajax.reload();
            $("#alert-content").append(" <p>Insert data suksess");
            $("#alert_information").css({display: "block"});
            })
            .fail(function () {
            alert("error");
            })
            .always(function () {
            });
    $("#modal-edit").modal("hide");
    });
    $('#btnStock').click(function () {
    console.log('stock');
    var wdata = way.get('formData');
    wdata.status = 4;
    console.log(wdata);
    var mode = wdata.modeform;
    var url;
    url = "<?php echo base_url(); ?>/pagu_tahapan/addform";
    var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
            .done(function (data) {
            tlist_paket.ajax.reload();
            $("#alert-content").append(" <p>Insert data suksess");
            $("#alert_information").css({display: "block"});
            })
            .fail(function () {
            alert("error");
            })
            .always(function () {
            });
    $("#modal-edit").modal("hide");
    });


     //jquery validate
     $( "#frm-tambah" ).validate({
          rules: {
            thang: {
              required: true

            },
            kd_kegiatan: {
              required: true

            }
            ,
            kd_output: {
              required: true

            }
            ,
            kd_sub_output: {
              required: true,
              maxlength: 2

            },
            kd_komponen: {
              required: true,


            },
            kd_sub_komponen: {
              required: true,
              maxlength: 2
            }
            ,
            xnama_sub_komponen: {
              required: true
            },
            jnskontrak: {
              required: true
            },
            kdkppn: {
              required: true
            },
            id_ppk: {
              required: true
            },
            rc_ded_status: {
              required: true
            },
            rc_fs_status: {
              required: true
            },
            rc_lahan_status: {
              required: true
            },
            rc_doklin_status: {
              required: true
            },
            kak: {
              required: true
            },
            rab: {
              required: true
            },
            prov: {
              required: true
            },
            kabkot: {
              required: true
            },
            kdgbkpk: {
              required: true
            },
            kdakun: {
              required: true
            },
            xdetail: {
              required: true
            },
            xid_ruas: {
              required: true
            },
            rab: {
              required: true
            }
            ,
            sta_awal: {
              required: true
            },
            sta_akhir: {
              required: true
            },
            xid_jembatan: {
              required: true
            },
            longitude: {
              required: true
            },
            latitude: {
              required: true
            },
            longitude2: {
              required: true
            },
            latitude2: {
              required: true
            }
            ,
            xvolume: {
              required: true,
              number  : true
            }
            ,
            xsatuan: {
              required: true
            }
            ,
            xhargasat: {
              required: true
            }
            ,
            xjumlah: {
              required: true
            },
            xsumber: {
              required: true
            }

          }
        });


        //jquery validate
     $( "#frm-detail").validate({
          rules: {
            ythang_sel: {
              required: true

            },
            ykd_kegiatan_sel: {
              required: true

            }
            ,
            ykd_output_sel: {
              required: true

            }
            ,
            ykd_sub_output_sel: {
              required: true

            },
            ykd_komponen_sel: {
              required: true,

            },
            ykd_sub_komponen_sel: {
              required: true,
              maxlength: 2
            }
            ,
            ynama_sub_komponen: {
              required: true
            }
            ,yprov: {
              required: true
            }
            ,
            ykabkot: {
              required: true
            },
            kdgbkpk: {
              required: true
            },
            ykdakun: {
              required: true
            },
            detail: {
              required: true
            },
            id_ruas: {
              required: true
            },
            sta_awal_sel: {
              required: true
            },
            sta_akhir_sel: {
              required: true
            },
            treatment: {
              required: true
            },
            yid_jembatan: {
              required: true
            },
            ylongitude: {
              required: true
            },
            ylatitude: {
              required: true
            },
            ylongitude2: {
              required: true
            },
            ylatitude2: {
              required: true
            },
            volume: {
              required: true
            },
            ysatuan: {
              required: true
            },
            yhargasat: {
              required: true
            }
            ,
            yjumlah: {
              required: true
            },
            ysumber: {
              required: true
            }
          }
        });


     //modal edit detail
     $( "#frm-edit-detail").validate({
          rules: {
            wkdakun: {
              required: true

            },
            wid_ruas: {
              required: true

            }
            ,
            wsta_awal: {
              required: true

            }
            ,
            wsta_akhir: {
              required: true

            },
            wid_jembatan: {
              required: true,

            },
            wlongitude: {
              required: true,
            }
            ,
            wlatitude: {
              required: true
            }
            ,wlongitude2: {
              required: true
            }
            ,
            wlatitude2: {
              required: true
            },
            wvolume: {
              required: true
            },
            wsatuan: {
              required: true
            },
            whargasat: {
              required: true
            },
            wjumlah: {
              required: true
            },
            wsumber: {
              required: true
            },
            wdetail: {
              required: true
            }
          }
        });


        //modal edit detail
     $( "#frm-edit-paket").validate({
          rules: {
            zthang: {
              required: true

            },
            zkd_kegiatan: {
              required: true

            }
            ,
            zkd_output: {
              required: true

            }
            ,
            zkd_sub_output: {
              required: true

            },
            zkd_komponen: {
              required: true,

            },
            zkd_sub_komponen: {
              required: true,
            }
            ,
            znama_sub_komponen: {
              required: true
            }
            ,zjnskontrak: {
              required: true
            }
            ,
            zkdkppn: {
              required: true
            },
            zid_ppk: {
              required: true
            },
            zrc_ded_status: {
              required: true
            },
            zrc_fs_status: {
              required: true
            },
            zrc_lahan_status: {
              required: true
            },
            zrc_doklin_status: {
              required: true
            },
            wdetail: {
              required: true
            }
            ,
            zkak: {
              required: true
            }
            ,
            zrab: {
              required: true
            }
            ,
            zprov: {
              required: true
            }
            ,
            zinputHal4: {
              required: true
            }
            ,
            zinputDipa: {
              required: true
            }
          }
        });


    });
    //*/--------------------------END DOC.READY-----------------------------------------


    function tbruas(){
                var tab=$('#table_iid').DataTable();
            tab.destroy()

        listingruas()
                $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");


    }
    function tbjembatan(){
           var tab=$('#table_idjem').DataTable();
            tab.destroy()
        listingjembatan()
                $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");


    }




        function dtTambahRowruass() {
        refreshCombobox('ppk', 54, 'kdsatker',"<?php echo $this->session->users['kode_satker']; ?>");
        $("#btn-simpan").show();
//        console.log('nambah row');
//        enable_input();
//        $("input").val("");



        // initCombobox('linkid', 48)
        //   bind_satker()


        $('#modalTitle').text('Tambah Data');
        $('#modeform').val('tambah');
        $('#modal-tambahr').modal('show');
    }
      function simpanFormruas() {
        var mode = $('#modeforms').val();
        var kdsat= "04" + user_satker;
        var url;
        if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/reff_ruas_nosk/addform";
            var data = {
                "kode_satker": kdsat,
                "linkname": $("#linkname").val(),
                "kd_ruas": $("#kd_ruas").val(),
                "no_sk": $("#nop").val(),
                "id_ppk": $("#ppk").val()
            }
        } else if (mode == 'edit') {
            url = "<?php echo base_url(); ?>/satker_ppk/editform";
            var data = way.get('formData');
        }
        ;

        //console.log(url);

        //  var data = way.get('formData');
        var params = {"formData": data, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        //console.log(params);
        // alert($(".webgis-required").val());
        console.log(params);
        if ($("#kd_dapil").val() == "" || $("#kd_prov_irmsv3").val() == "" || $("#nama_dapil").val() == "") {
            //alert(111);
            alert("Gagal menyimpan, Harap lengkapi Form isian yang kosong");
        } else {

            $.post(url, params)
                    .done(function (data) {
                        //console.log(data);
                        //alert("Data saved " + data);

                        tablex.ajax.reload();
                        $("#alert-content").empty();
                        $("#alert-content").append(" <p>simpan data suksess");
                        $("#alert_information").css({display: "block"});
                        setTimeout(close_alert, 3000);


                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }
     function dtDeleteRowruas(id) {

        url = "<?php echo base_url(); ?>/reff_ruas_nosk/deleteform";
//        alert(id);

        $.post(url, {id: id, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"}).done(function (data) {

            tablex.ajax.reload();
            $("#alert-content").empty();
            $("#alert-content").append(" <p>Delete data suksess");
            $("#alert_information").css({display: "block"});
            setTimeout(close_alert, 3000);

        })
//                .fail(function () {
//                    alert("error");
//                })
//                .always(function () {
//                    alert("finished");
//                });
    }

    function listingruas() {

        tablex = $('#table_iid').DataTable({

            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2]}],
            "order": [2, "desc"],
            "processing": true,
            "serverSide": true,
            "ajax": {type: "POST", url: "<?php echo base_url(); ?>/reff_ruas_nosk/ssp","data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},},
            "aoColumnDefs": [
                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {

                        var html_button = [
//                            "<button onclick= dtViewRow('" + data + "') class='btn btn-primary btn-xs btn-warning' >",
//                            "<i class='fa fa-search'></i>",
//                            "</button>",
//                            "<button onclick= dtEditRow('" + data + "') class='btn btn-primary btn-xs'>",
//                            "<i class='fa fa-pencil'>",
//                            "</i>",
//                            "</button>",
                            "<button onclick= dtDeleteRowruas('" + data + "') class='btn btn-danger btn-xs'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>",
                        ].join("\n");
                        return html_button;


                        return  '<div><button title="Edit" onClick="dtEditRow(' + data + ');" >E</button><button title="Delete" onClick="dtDeleteRow(' + data + ');">X</button></div>';
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        tablex.on('xhr', function () {
            xhrdata = tablex.ajax.json();
            console.log(xhrdata);
        });
    }
    //jembartan


     var tablej = null;
    function listingjembatan() {
        tablej = $('#table_idjem').DataTable({

            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": {type: "POST", url: "<?php echo base_url(); ?>/reff_jembatan/ssp",
				"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},},
            "aoColumnDefs": [
                {
                    "aTargets": [3],
                    "mRender": function (data, type, full) {

                        var html_button = [
//                            "<button onclick= dtViewRow('" + data + "') class='btn btn-primary btn-xs btn-warning' >",
//                            "<i class='fa fa-search'></i>",
//                            "</button>",
                            "<button onclick= dtEditRowjembatan('" + data + "') class='btn btn-primary btn-xs'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick= dtDeleteRowjembatan('" + data + "') class='btn btn-danger btn-xs'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>",
                        ].join("\n");
                        return html_button;


                        return  '<div><button title="Edit" onClick="dtEditRow(' + data + ');" >E</button><button title="Delete" onClick="dtDeleteRow(' + data + ');">X</button></div>';
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        tablej.on('xhr', function () {
            xhrdata = tablej.ajax.json();
            console.log(xhrdata);
        });
    }

    function dtTambahRowjembatan() {
    refreshCombobox('id_ruasss', 33, 'kode_satker', user_satker);


//        enable_input();
        $("input").val("");



        // initCombobox('linkid', 48)
        //   bind_satker()


        $('#modalTitle').text('Tambah Data');
        $('#modeform').val('tambah');
        $('#modal-tambahjembatan').modal('show');
    }


    function dtDeleteRowjembatan(id) {

        url = "<?php echo base_url(); ?>/reff_jembatan/deleteform";


        $.post(url, {id: id}).done(function (data) {

            table.ajax.reload();
            $("#alert-content").empty();
            $("#alert-content").append(" <p>Delete data suksess");
            $("#alert_information").css({display: "block"});
            setTimeout(close_alert, 3000);

        })
//                .fail(function () {
//                    alert("error");
//                })
//                .always(function () {
//                    alert("finished");
//                });
    }


    function simpanFormjembatan() {
        var mode = $('#modeforms').val();
        var x = $("#id_ruas").val();
        var noj = $("#no_jembatan").val();
        var not = $("#no_tambahan").val();
        var noje = $("#noje").val();
        var suf = $("#suffix").val();
        var id = $('#id').val();
       // alert(id);
        if (x == "-1")
        {

         var has = noje.slice(5, 20);
         var u = has+ "." + noj + "." + not + "." + suf;
        // alert("gagal");
        }
        else
        {
            var satu = x.slice(2,3);
//            alert (satu)
            var dua = x.slice(3,4);
            var tiga = x.slice(4,5);
            if(satu==":")
            {
            var has = x.slice(4, 20);
            console.log("ssssssssssssssss")
            }else if(dua==":")
            {
                var has = x.slice(5, 20);
                console.log("zzzzzzzzzzzzzzz")
            }else if(tiga==":")
            {
                var has = x.slice(6, 20);
                console.log("ttttttttttttttttt")
            }
            //alert(has);
        var t = has.slice(0, 5);
       // alert(t);
        var y = t.slice(0, 2);
        var z = t.slice(2, 5);
        var u = y + "." + z + "." + noj + "." + not + "." + suf;

        }

        console.log(u);
//        if (has.slice(5, 6) == ":")
//        {
//            var t = x.slice(6, 11);
//            var y = t.slice(0, 2);
//            var z = t.slice(2, 5);
//            var u = y + "." + z +".";
//            console.log('lima');
//             console.log(u);
//        } else if (has.slice(6, 7) == ":")
//        {
//            var t = x.slice(6, 12);
//            var y = t.slice(0, 2);
//            var z = t.slice(2, 6);
//            var u = y + "." + z+".";
//            console.log('enam');
//             console.log(u);
//        } else if (has.slice(7, 8) == ":")
//        {
//            var t = x.slice(6, 13);
//            var y = t.slice(0, 2);
//            var z = t.slice(2, 5);
//            var l = t.slice(5, 7);
//            var u = y + "." + z + ".0" + l+".";
//            console.log('tujuh');
//             console.log(u);
//        } else
//        {
//            var t = x.slice(6, 14);
//            var y = t.slice(0, 2);
//            var z = t.slice(2, 5);
//            var l = t.slice(5, 7);
//            var u = y + "." + z + "." + l +".";
//            console.log('delapan');
//             console.log(u);
//        }
      //  alert(u);
        var url;
        if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/reff_jembatan/addform";
            var data = {
                "nama_jembatan": $("#nama_jembatan").val(),
                "nomor_jembatan": u
            }
        } else if (mode == 'edit') {
            url = "<?php echo base_url(); ?>/reff_jembatan/editform";
            //  var data = way.get('formData');
            var data = {
                "nama_jembatan": $("#nama_jembatan").val(),
                "nomor_jembatan": u,
                "id_jembatan":id
            }
        }
        ;

        //console.log(url);

        //  var data = way.get('formData');
        var params = {"formData": data, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        //console.log(params);
        // alert($(".webgis-required").val());
        console.log(params);
        if ($("#kd_dapil").val() == "" || $("#kd_prov_irmsv3").val() == "" || $("#nama_dapil").val() == "") {
            //alert(111);
            alert("Gagal menyimpan, Harap lengkapi Form isian yang kosong");
        } else {

            $.post(url, params)
                    .done(function (data) {
                        //console.log(data);
                        //alert("Data saved " + data);

                        table.ajax.reload();
                        $("#alert-content").empty();
                        $("#alert-content").append(data);
                        $("#alert_information").css({display: "block"});
                        setTimeout(close_alert, 3000);


                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }


    //------------aday
    function get_wps() {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_wps') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            console.log(data);
                    x = data;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }



    function get_detail_usulan(id_detail) {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_detail_usulan') ?>" + "/" + id_detail+'/'+kd_tahapan,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            x = data;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }
function initComboboxJembatans(divname, selvalue,selvalue2) {
    url3 = "exercise_wp/get_jembatan/"+selvalue;

    $.get(url3).done(function (data3) {
        jdata3 = JSON.parse(data3);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));


        $.each(jdata3, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });
        $('#' + divname).val(selvalue2);

    }).fail(function () {
        alert("error");
    }).always(function () {
        // alert("finished");
    });
}

    var htmldropdownsumberdana = "";
    function editDetail(id_detail){
        $('#frm-edit-detail')[0].reset();
         $("#xrm").val('')
        $("#yrm").val('')
            var tab=$('#tirmsv3_wp4').DataTable();
            tab.destroy()
        irms('4')
    $(".content-sumber-dana").hide();
    $("#alert-tunggu").show();
    //new code
     // initCombobox('wregister', 73);
     // initCombobox('wregisterpdp', 73);
     initCombobox('wkd_blokir', 76);

    //mengosongkan semua nilai variabel global
//             $("#wkdakun").prop("disabled",true)
       $("#wsta_awal").prop("disabled",false)
       $("#wsta_akhir").prop("disabled",false)
//    $("#wid_jembatan").prop("disabled",true)
//    $("#wlongitude").prop("disabled",true)
//    $("#wlatitude").prop("disabled",true)
        $('#thang_sel').prop('disabled', true);
    $('#kd_kegiatan_sel').prop('disabled', true);
    $('#kd_output_sel').prop('disabled', false);
    $('#kd_sub_output_sel').prop('disabled', false);
    $('#kd_komponen_sel').prop('disabled', false);
    $('#kd_sub_komponen_sel').prop('disabled', true);
    $('#nama_sub_komponen_sel').prop('disabled', true);
    //$('#id_ppk_sel').prop('disabled', false);
    //$('#kdkppn_sel').prop('disabled', false);
    rujukan_all = [];
    obj_wsumberdana = {};
    objupdateedittags = {};
    $("#xrm").val("");
    var roledesc = "<?php echo $this->session->users['roledesc']; ?>";

    var obj_usulan = get_detail_usulan(id_detail);
    if (roledesc == "Satker Fisik"){
    $(".divNonFisik").css("display", "none");
    } else{
    $(".divRuas").css("display", "none");
    }

    //new code
    var satuans = obj_usulan.satuan;
    var satuan = satuans.toUpperCase();
    console.log(kdtahap);
    if (satuan === 'KM'){
        $(".id_radioRuas").show();
        $(".id_radioJembatan").hide();
        $(".id_radioHonor").hide();
        $("input[name='wradioVolume']").val([1]);
        setTimeout(function(){ handleRadioVolumeEdit('w'); }, 3000);
        if (kdtahap === 'KO'){
            $("#wkabkot").prop('disabled', true);
            $("#wkdgbkpk").prop('disabled', true);
            $("#wkdakun").prop('disabled', true);
            $("#wsumber").prop('disabled', true);
            $("#wregister").prop('disabled', true);
            $("#wpanelHitung").prop('disabled', true);
            $("#winputHal4").prop('disabled', true);
            $("#winputDipa").prop('disabled', true);
            $("#wid_ruas").prop('disabled', true);
            $('#wkdkppn').prop('disabled', true);
            $('#wid_ppk').prop('disabled', true);
            $(".longs").addClass('hidden');
            $(".longsa").addClass('hidden');
            $(".longss").show();
            $(".longsaa").show();
        } else {
            $(".longs").show();
            $(".longsa").show();
            $(".longss").hide();
            $(".longsaa").hide();
        }
    } else if (satuan === 'M') {
        $(".id_radioRuas").hide();
        $(".id_radioJembatan").show();
        $(".id_radioHonor").hide();
        $("input[name='wradioVolume']").val([2]);
        setTimeout(function(){ handleRadioVolumeEdit('w'); }, 3000);
        if (kdtahap === 'KO'){
            $("#wkabkot").prop('disabled', true);
            $("#wkdgbkpk").prop('disabled', true);
            $("#wkdakun").prop('disabled', true);
            $("#wsumber").prop('disabled', true);
            $("#wregister").prop('disabled', true);
            $("#wpanelHitung").prop('disabled', true);
            $("#winputHal4").prop('disabled', true);
            $("#winputDipa").prop('disabled', true);
            $("#wid_ruas").prop('disabled', true);
            $("#wid_jembatan").prop('disabled', true);
            $(".longs").addClass('hidden');
            $(".longsa").addClass('hidden');
            $(".longss").show();
            $(".longsaa").hide();
        } else {
            $(".longs").show();
            $(".longsa").show();
            $(".longss").hide();
            $(".longsaa").hide();
        }
    } else {
        $(".id_radioRuas").hide();
        $(".id_radioJembatan").hide();
        $(".id_radioHonor").show();
        $("input[name='wradioVolume']").val([3]);
        setTimeout(function(){ handleRadioVolumeEdit('w'); }, 3000);
    }

    //menyembunyikan field field khusus bagi satker nonfisik
    var kdgi= obj_usulan.kd_kegiatan;
    var kdou=obj_usulan.kd_output;

   if(kdgi=='2409' && (kdou=='014' || kdou=='015' || kdou=='016' || kdou=='017' || kdou=='018' || kdou=='951' || kdou=='970' || kdou=='994' || kdou=='999'))
               {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                   $(".uraians").show();
                   //$("#fsk").hide();
                  // alert("ilang");
               }
               else if(kdgi=='2409' && (kdou=='001' || kdou=='002' || kdou=='003' || kdou=='004' || kdou=='005' || kdou=='006' || kdou=='007' || kdou=='008' || kdou=='009' || kdou=='010' || kdou=='011' || kdou=='012' || kdou=='013'))
                {
                    if(kdou=='004' || kdou=='005' || kdou=='006' || kdou=='010')
                    {
                     $(".ruasjalan").show();
                     $(".ruasjalan").removeClass('col-md-6');
                     $(".ruasjalan").addClass('col-md-12');
                    $(".staw").hide();
                    $("#sta_awal_sel").empty();
                    $("#sta_akhir_sel").empty();
                    $(".jembatans").show();
                    $("#19").text("Longitude (X1)")
                    $("#20").text("Latitude  (Y1)")
//                    $("#ylongitudes").empty()
//                    $("#ylatitudes").empty()
//                    $("#ylongitude").empty()
//                    $("#ylatitude").empty()
                    $(".longs").show();
                    $(".longsa").hide();
                    }else
                    {
                    $(".ruasjalan").show();
                     $(".ruasjalan").removeClass('col-md-12');
                     $(".ruasjalan").addClass('col-md-6');
                    $(".staw").show();
                    $(".jembatans").hide();
                    $("#yid_jembatan").empty()
                    $("#19").text("Longitude (X1)")
                    $("#20").text("Latitude (Y1)")
                     $("#21").text("Longitude (X2)")
                    $("#22").text("Latitude (Y2)")
                    $(".longs").show();
                    $(".longsa").show();
                    }

                }
                else if(kdgi !='2409')
                {
                    console.log("disini");
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                    $(".uraians").show();
                }

    refreshComboboxOutput('wthang', 21, 'thang', obj_usulan.thang, obj_usulan.thang);
    refreshComboboxOutput('wkd_kegiatan', 5, 'kdgiat', obj_usulan.kd_kegiatan, obj_usulan.kd_kegiatan);
    setTimeout(set_kegiatan, 8000); //set kegiatan berdasarkan kode
    //var wsum=obj_usulan.kdbeban+"_"+obj_usulan.kdjnsban+"_"+obj_usulan.kdctarik;
    //updateComboboxAndSelected('wsumber', 65, wsum);
    $("#wkd_sub_komponen").val(obj_usulan.kd_sub_komponen);
    $("#wnama_sub_komponen").val(obj_usulan.nama_sub_komponen);
        $("#wdetail").val(obj_usulan.detail);
    $("#wid_paket").val(obj_usulan.id_paket);
    $("#wid_usulan").val(obj_usulan.id_usulan);
    //alert(obj_usulan.flow1)
    $("#wflow1").val(obj_usulan.flow1);
    // alert(obj_usulan.flow1)
    $("#wflow2").val(obj_usulan.flow2);
    $("#wflow3").val(obj_usulan.flow3);
      var wrm = obj_usulan.rm;
      var pln = obj_usulan.phln;
      var rmp = obj_usulan.rmp;
      var pnbp = obj_usulan.pnbp;
      var blu = obj_usulan.blu;
      var sbsn = obj_usulan.sbsn;
      var pdn = obj_usulan.pdn;
      if(wrm == '.000' || wrm == null || wrm==''){$("#wrm").val('0');} else {$("#wrm").val(numberWithCommas(wrm).split('.')[0]);}
      if(pln == '.000' || pln == null || pln ==''){$("#pln3").val('0');} else {$("#pln3").val(numberWithCommas(pln).split('.')[0]);}
      if(rmp == '.000' || rmp == null || rmp ==''){$("#rmp3").val('0');} else{     $("#rmp3").val(numberWithCommas(rmp).split('.')[0]);}
      if(pnbp == '.000' ||pnbp == null || pnbp ==''){$("#pnbp3").val('0');}else {$("#pnbp3").val(numberWithCommas(pnbp).split('.')[0]);}
      if(blu == '.000' || blu== null || blu==''){$("#blu3").val('0');} else{$("#blu3").val(numberWithCommas(blu).split('.')[0]);}
      if(sbsn == '.000'  ||sbsn == null || sbsn==''){$("#sbsn3").val('0');}else {$("#sbsn3").val(numberWithCommas(sbsn).split('.')[0]);}
      if(pdn == '.000' || pdn == null || pdn==''){$("#pdn3").val('0');}else{$("#pdn3").val(numberWithCommas(pdn).split('.')[0]);}
//    $("#pln3").val(numberWithCommas(pln));
//    $("#rmp3").val(numberWithCommas(rmp));
//    $("#pnbp3").val(numberWithCommas(pnbp));
//    $("#blu3").val(numberWithCommas(blu));
//    $("#sbsn3").val(numberWithCommas(sbsn));
//    $("#pdn3").val(numberWithCommas(pdn));
    var id_rprov = $("#id_rprov").val();
        refreshComboboxOutput('wprov', 64, 'kdsatker', kd_satker,prrr);
    //refreshComboboxOutput('wprov', 60, 'kd_prov', id_rprov, id_rprov);
    refreshComboboxOutput('wkd_output', 30, 'kdgiat', obj_usulan.kd_kegiatan, obj_usulan.kd_output);
    refreshComboboxOutput('wkd_sub_output', 44, 'kdoutput', obj_usulan.kd_output, obj_usulan.kd_sub_output);
    refreshComboboxOutput('wkd_komponen', 18, 'kdsoutput', obj_usulan.kd_sub_output, obj_usulan.kd_komponen);
    updateComboboxAndSelected('wjnskontrak', 23, obj_usulan.jnskontrak);
    //updateComboboxAndSelected('wkdkppn', 9, obj_usulan.kdkppn);
    //refreshComboboxOutput('wid_ppk',47,'kdsatker', kd_satker, obj_usulan.id_ppk);
    //updateComboboxAndSelected('wid_ppk', 47, obj_usulan.id_ppk);
    refreshComboboxOutput('wid_ppk',47,'kdsatker', kd_satker, obj_usulan.id_ppk);
    updateComboboxAndSelected('wwps_kode', 57, obj_usulan.wps_kode);
    //updateComboboxAndSelected('wkdgbkpk', 36, obj_usulan.kdgbkpk);
    //console.log('ini');
    //console.log(obj_usulan.id_ruas);
    //refreshComboboxRuasSatker("wid_ruas", kd_satker, obj_usulan.id_ruas);
    //refreshComboboxOutput('wkabkot', 71, 'KD_SATKER',kd_satker,obj_usulan.kdkabkota);
    if (obj_usulan.kdkabkota2 === null || obj_usulan.kdkabkota2 === ''){
        refreshComboboxOutput('wkabkot', 71, 'kd_prov_rkakl', prrr);
    } else {
        var kabkot = get_kabkota(obj_usulan.kdlokasi, obj_usulan.kdkabkota2);
        console.log(kabkot);
        refreshComboboxOutput('wkabkot', 71, 'kd_prov_rkakl', prrr, kabkot.kd_kab_ruas);
        //alert(obj_usulan.kdkabkota);
        if (obj_usulan.id_ruas !== '' || obj_usulan.id_ruas !== null){
            refreshComboboxOutput('wid_ruas', 70, 'KODE_KABKOTA', kabkot.kd_kab_bps, obj_usulan.id_ruas);
            //refreshComboboxSTASatker("wsta_awal", obj_usulan.id_ruas, obj_usulan.sta_awal);
            //refreshComboboxSTASatker("wsta_akhir", obj_usulan.id_ruas, obj_usulan.sta_akhir);
        }
    }
     //refreshComboboxSearch('wkdakun', 37, 'kdgbkpk', obj_usulan.kdgbkpk, obj_usulan.kdakun);
     //refreshComboboxSearch('wkabkot', 61, 'kdkabkota', obj_usulan.kdkabkota, obj_usulan.kdkabkota);
     //updateComboboxAndSelected('wkabkot', 61, obj_usulan.kdkabkota);


    if (obj_usulan.volume < 1){
  //  $("#wvolume").val("0" + parseFloat(obj_usulan.volume));
    } else{
  //  $("#wvolume").val(parseFloat(obj_usulan.volume));
    }
      $("#wvolume").val(parseFloat(obj_usulan.volume));

    if (typeof obj_usulan.id_jembatan != "object") {
//        alert(obj_usulan.id_jembatan);
    refreshComboboxJembatan('wid_jembatan', obj_usulan.id_ruas, obj_usulan.id_jembatan);
    $("#wid_jembatan").val(obj_usulan.id_jembatan)

    $("#wlongitude").val(obj_usulan.longitude);
    $("#wlatitude").val(obj_usulan.latitude);
        if (kdtahap === 'KO'){
            $("#wlongitude3").val(obj_usulan.longitude);
            $("#wlatitude3").val(obj_usulan.latitude);
        }
    }
    $("#wlongitude").val(obj_usulan.longitude);
    $("#wlatitude").val(obj_usulan.latitude);
    $("#wlongitude2").val(obj_usulan.longitude2);
    $("#wlatitude2").val(obj_usulan.latitude2);

    if (kdtahap === 'KO'){
        $("#wlongitude3").val(obj_usulan.longitude);
        $("#wlatitude3").val(obj_usulan.latitude);
        $("#wlongitude4").val(obj_usulan.longitude2);
        $("#wlatitude4").val(obj_usulan.latitude2);
    }

    $("#wsatuan").val(obj_usulan.satuan);
    var wjumlah = obj_usulan.jumlah;
    var whargasat = obj_usulan.hargasat;
    if (typeof wjumlah != 'object'){
    $("#wjumlah").val(numberWithCommas(wjumlah));
    }

    if (typeof whargasat != 'object'){
    $("#whargasat").val(numberWithCommas(whargasat));
    }

//    initCombobox("wkdakun", 37);
    $("#wrc_ded_status").append("<option selected value=" + obj_usulan.rc_ded_status + ">" + obj_usulan.rc_ded_status + "</option>");
    $("#wrc_fs_status").append("<option selected " + obj_usulan.rc_fs_status + ">" + obj_usulan.rc_fs_status + "</option>");
    $("#wrc_lahan_status").append("<option selected " + obj_usulan.rc_lahan_status + ">" + obj_usulan.rc_lahan_status + "</option>");
    $("#wrc_doklin_status").append("<option selected " + obj_usulan.rc_doklin_status + ">" + obj_usulan.rc_doklin_status + "</option>");
        $("#wkak").append("<option selected " + obj_usulan.kak_status + ">" + obj_usulan.kak_status + "</option>");
    $("#wrab").append("<option selected " + obj_usulan.rab_status + ">" + obj_usulan.rab_status + "</option>");

    setTimeout(set_kegiatan, 8000); //
    if (obj_usulan.rj_rams != "{}"){
    $("#rj_rams").val(obj_usulan.rj_rams);
    }
    if (obj_usulan.rj_irms != "{}"){
    $("#rj_irms").val(obj_usulan.rj_irms);
    }
    if (obj_usulan.rj_renstra != "{}"){
    $("#rj_renstra").val(obj_usulan.rj_renstra);
    }
    if (obj_usulan.rj_eprog != "{}"){
    $("#rj_eprog").val(obj_usulan.rj_eprog);
    }
    if (obj_usulan.rj_dpr != "{}"){
    $("#rj_dpr").val(obj_usulan.rj_dpr);
    }
    if (obj_usulan.rj_pemda != "{}"){
    $("#rj_pemda").val(obj_usulan.rj_pemda);
    }
    if (obj_usulan.rj_sipro != "{}"){
    $("#rj_sipro").val(obj_usulan.rj_sipro);
    }

    var tagram = set_rams();
    var tagirms = set_irms();
    var tagrenstra = set_renstra();
    var tageprog = set_eprog();
    var tagdpr = set_dpr();
    var tagsipro = set_sipro();
    var tagpemda = set_pemda();
    var objtag = [
    {"rams":tagram},
    {"irms":tagirms},
    {"renstra":tagrenstra},
    {"eprog":tageprog},
    {"dpr":tagdpr},
    {"pemda":tagpemda},
    {"sipro":tagsipro}
    ];
    var string_tag = "";
    var append_tag = "";
    $('#wtag').tagsinput('removeAll');
    $('#wtags').tagsinput({
    allowDuplicates: false,
            itemValue: 'id', // this will be used to set id of tag
            itemText: 'label', // this will be used to set text of tag
            tagClass: function(item) {
            switch (item.tipe_rujukan) {
            case 'DPR':
                    return 'label label-info';
            case 'PEMDA':
                    return 'label label-default';
            case 'SIPRO':
                    return 'label label-primary';
            case 'IRMS':
                    return 'label label-success';
            case 'RAMS':
                    return 'label label-info';
            case 'EPROG':
                    return 'label label-warning';
            case 'RENSTRA':
                    return 'label label-danger';
            }
            }
    });
    console.log("object tag renstra");

    $('#wtags').tagsinput('removeAll');
    if ($("#modal-edit-detail #rj_rams").val() == "" && $("#modal-edit-detail #rj_irms").val() == "" && $("#modal-edit-detail #rj_eprog").val() == "" && $("#modal-edit-detail #rj_renstra").val() == "" && $("#modal-edit-detail #rj_dpr").val() == "" && $("#modal-edit-detail #rj_pemda").val() == "" && $("#modal-edit-detail #rj_sipro").val() == ""){

    } else{

    var arrstring_tag = [];
    for (var i = 0; i <= objtag.length - 1; i++){

    var strtag = JSON.stringify(Object.values(objtag[i]));
    var arr_tag = strtag.split(",");
    for (var y = 0; y <= arr_tag.length - 2; y++){

    var string_key = JSON.stringify(Object.keys(objtag[i])).replace('[', '');
    string_key = string_key.replace(']', '');
    string_key = string_key.replace('"', '');
    string_key = string_key.replace('"', '');
    var tag_value = arr_tag[y];
    tag_value = tag_value.replace('[', '');
    tag_value = tag_value.replace(']', '');
    tag_value = tag_value.replace('"', '');
    tag_value = tag_value.replace('"', '');
    var xkey = string_key;
    var append_tag = append_tag + xkey.toUpperCase() + "|" + tag_value + "#";
    arrstring_tag[i] = append_tag;
    }

    }

    var filtered = arrstring_tag.filter(function (el) {
    return el != null;
    });
    var numofdata = filtered.length;
    for (var a = 0; a <= filtered[numofdata - 1].split("#").length - 1; a++){

    var key = filtered[numofdata - 1].split("#")[a].split("|")[0];
    $('#wtags').tagsinput('add', { id: a, label: filtered[numofdata - 1].split("#")[a], tipe_rujukan:key});
    }
    }

    var objsumberdana = {};
    if (obj_usulan.rm != "" && typeof obj_usulan.rm != 'object' && obj_usulan.rm != '.000'){
    objsumberdana["rm"] = obj_usulan.rm;
    console.log("nilai RM");
            console.log(obj_usulan.rm);
    }

    if (obj_usulan.phln != "" && typeof obj_usulan.phln != 'object' && obj_usulan.phln != '.000'){
    objsumberdana["pln"] = obj_usulan.phln;
    }

    if (obj_usulan.rmp != "" && typeof obj_usulan.rmp != 'object' && obj_usulan.rmp != '.000'){
    objsumberdana["rmp"] = obj_usulan.rmp;
    }

    if (obj_usulan.pnbp != "" && typeof obj_usulan.pnbp != 'object' && obj_usulan.pnbp != '.000'){
    objsumberdana["pnbp"] = obj_usulan.pnbp;
    }

    if (obj_usulan.blu != "" && typeof obj_usulan.blu != 'object' && obj_usulan.blu != '.000'){
    objsumberdana["blu"] = obj_usulan.blu;
    }

    if (obj_usulan.sbsn != "" && typeof obj_usulan.sbsn != 'object' && obj_usulan.sbsn != '.000'){
    objsumberdana["sbsn"] = obj_usulan.sbsn;
    }

    if (obj_usulan.opr != "" && typeof obj_usulan.opr != 'object' && obj_usulan.opr != '.000'){
    objsumberdana["opr"] = obj_usulan.opr;
    }

    if (obj_usulan.pdp != "" && typeof obj_usulan.pdp != 'object' && obj_usulan.pdp != '.000'){
    objsumberdana["pdp"] = obj_usulan.pdp;
    }

    if (obj_usulan.pdn != "" && typeof obj_usulan.pdn != 'object' && obj_usulan.pdn != '.000'){
    objsumberdana["pdn"] = obj_usulan.pdn;
    }

    multipleSelect('wkdsdana', 38);
    $("#htmldropdownsdana").val($("#wkdsdana").html());

    $("#objsumberdana").val("");
    $("#objsumberdana").val(JSON.stringify(objsumberdana));

    setTimeout(setSumberDana, 8000);
    if ($(".input-edit-detail").length > 1){
    $(".input-edit-detail :first").remove();
    }


    var whargasat = obj_usulan.hargasat;
    if (typeof whargasat != 'object'){
    $("#whargasat").val(numberWithCommas(parseInt(whargasat)));
    }

    var wjumlah = obj_usulan.jumlah;
    if (typeof whargasat != 'object'){
    $("#wjumlah").val(numberWithCommas(parseInt(wjumlah)));
    }
    document.getElementById("wthang").disabled = true;
//    document.getElementById("wkd_isu").disabled = true;
    document.getElementById("wkd_kegiatan").disabled = true;
    //document.getElementById("wjns_giat").disabled = true;
    document.getElementById("wkd_output").disabled = true;
    document.getElementById("wkd_sub_output").disabled = true;
    document.getElementById("wkd_komponen").disabled = true;
    document.getElementById("wkd_sub_komponen").disabled = true;
    document.getElementById("wnama_sub_komponen").disabled = true;
    //document.getElementById("wkdkppn").disabled = true;
    //document.getElementById("wid_ppk").disabled = true;
    document.getElementById("wrc_ded_status").disabled = true;
    document.getElementById("wrc_fs_status").disabled = true;
    document.getElementById("wrc_lahan_status").disabled = true;
    document.getElementById("wrc_doklin_status").disabled = true;
    document.getElementById("wkdgbkpk").disabled = false;
    document.getElementById("wprov").disabled = true;

        $("#urutans").empty();
        $("#urutans").val("4");
    $("#modal-edit-detail").modal("show");


    //new code
    //$("#wid_ruas").val(obj_usulan.id_ruas);
    $("#wsta_awal").val(obj_usulan.sta_awal);
    $("#wsta_akhir").val(obj_usulan.sta_akhir);
    var akun = get_akun_all(id_detail);

    //$('#wkdgbkpk').prop('disabled', true);
    //$('#wkdakun').prop('disabled', true);
    refreshComboboxKPPN('wkdkppn', 46, 'kdsatker', kd_satker, obj_usulan.kdkppn);
    var a = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        updateComboboxAndSelected('wkdgbkpk', 36, akun.kdgbkpk);
        $('#wkdakun').val('');
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var b = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        updateComboboxAndSelected('wkdakun', 37, akun.kdakun);
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var aaa = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        $("input[name='witemradiolokasi']").val([2]);
        //console.log("fungsi ini jalan");
        $('#wkdsbu').val(obj_usulan.kdsbu);
        $('#winputVol1').val(parseInt(obj_usulan.vol1));
        $('#winputVol2').val(parseInt(obj_usulan.vol2));
        $('#winputVol3').val(parseInt(obj_usulan.vol3));
        $('#winputVol4').val(parseInt(obj_usulan.vol4));
        $('#winputSat1').val(obj_usulan.sat1);
        $('#winputSat2').val(obj_usulan.sat2);
        $('#winputSat3').val(obj_usulan.sat3);
        $('#winputSat4').val(obj_usulan.sat4);
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var aa = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        var wsum = obj_usulan.kdbeban+"_"+obj_usulan.kdjnsban+"_"+obj_usulan.kdctarik;
        //console.log(wsum);
        updateComboboxAndSelectedSumber('wsumber', 65, wsum);
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var ab = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        var sumber = $('#wsumber').val();
        handleSumberEdit(sumber, 'w');
        setTimeout(function(){ ChangeSumberList(null, 'w'); }, 1000);
        //updateComboboxAndSelected('wregister', 73, obj_usulan.register);
        //console.log(obj_usulan.register);
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var ac = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        console.log(obj_usulan.kdjnsban);
        console.log(obj_usulan.kdctarik);
        $("input[name='wcaraHitung']").val([obj_usulan.carahitung]);
        setTimeout(function(){ handleRadioHitungEdit('w'); }, 1000);
//        var sumber = $('#wsumber').val();
//        //console.log(sumber);
//        handleSumber(null, sumber+'::w');
//        setTimeout(function(){ ChangeSumberList('w'); }, 3000);
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var ad = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        console.log('sebelum');
        if (obj_usulan.register !== ''){
            var regis = get_nmregister(obj_usulan.register);
            refreshSelectCombobox('wregister', 73, obj_usulan.register, regis.nmdonor);
        } else {
            refreshSelectCombobox('wregister', 73, '', '');
        }
        console.log('sudah');
        //console.log("set ini");
//        $("#winputPHLN").val(akun.prosenphln);
//        $("#winputRMPdp").val(akun.prosenrmp);
//        $("#winputRPLNPdp").val(akun.prosenrkp);
        if (akun.carahitung === '4'){
            $("#wpanelNonSharing").show();
            $("#winputNSPHLN2").val(addCommas(obj_usulan.paguphln).split('.')[0]);
            $("#winputNSRMPdp2").val(addCommas(obj_usulan.pagurmp).split('.')[0]);
            $("#winputNSRPLNPdp2").val(addCommas(obj_usulan.pagurkp).split('.')[0]);
            $("#wTotal").val(addCommas(obj_usulan.jumlah).split('.')[0]);

            //$("#winputRPLNPdp2").val(addCommas(akun.pagurkp));
        }
//        $('#winputKPPNph').val(akun.kppnphln);
//        $('#winputKPPNrm').val(akun.kppnrmp);
//        $('#winputKPPNrp').val(akun.kppnrkp);
//        $('#wregisterpdp').val(akun.regdam);
//        refreshComboboxKPPN('winputKPPNph', 46, 'kdsatker', kd_satker, akun.kppnphln);
//        refreshComboboxKPPN('winputKPPNrm', 46, 'kdsatker', kd_satker, akun.kppnrmp);
//        refreshComboboxKPPN('winputKPPNrp', 46, 'kdsatker', kd_satker, akun.kppnrkp);
        //new
        get_akun_update(obj_usulan.id_paket, obj_usulan.thang, kd_satker, obj_usulan.kd_kegiatan,
            obj_usulan.kd_output, obj_usulan.kd_sub_output, obj_usulan.kd_komponen, obj_usulan.kd_sub_komponen,
            obj_usulan.kdakun, obj_usulan.kdbeban, obj_usulan.kdjnsban, obj_usulan.kdctarik, 'w');
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var ae = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        if (obj_usulan.kdblokir === '*'){
            $("input[name='witemblokir']").val([1]);
            $("input[name='witemblokir']").prop('disabled', false);
            setTimeout(function(){ handleBlokir(null, 'w'); }, 1000);
            $('#winputBlokirPHLN').val(addCommas(obj_usulan.blokirphln).split('.')[0]);
            $('#winputBlokirRMPdp').val(addCommas(obj_usulan.blokirrmp).split('.')[0]);
            $('#winputBlokirRPLNPdp').val(addCommas(obj_usulan.blokirrkp).split('.')[0]);
            $('#winputRphBlokir').val(addCommas(obj_usulan.rphblokir).split('.')[0]);
        } else {
            setTimeout(function(){ handleBlokir('kosong', 'w'); }, 1000);
        }
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var af = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        //console.log(obj_usulan.id_paket);
        //console.log(obj_usulan.id_ppk);
        //console.log(obj_usulan.header2);
        if (obj_usulan.header2 !== null || obj_usulan.header2 !== ''){
            var header2 = get_header2s(obj_usulan.id_paket, obj_usulan.kdakun, obj_usulan.id_ppk, obj_usulan.header2);
            if (header2 !== null){
                $('#wheader2').val(header2.detail);
            }
        }
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var ag = function () {
    var defer = $.Deferred();
    setTimeout(function () {
        var sumber = $('#wsumber').val();
        setTimeout(function(){ handleSumber(null, sumber+'::w'); }, 1000);
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    a().then(b).then(aaa).then(aa).then(ab).then(ac).then(ad).then(ae).then(af);
//    $('#winputHal4').val(akun.ket);
//    $('#winputDipa').val(akun.ket2);
    }

    function setDropdown(){
    $("#htmldropdownsdana").val($("#wkdsdana").val());
    }


    $(".input-group_select:input").change(function(){
    alert(11111);
    });
    function extract_w_sumberdana(){


    $(".mdl-edit-detail").find('input').each(function(index, data) {
    var obj_wsumberdana = {};
    var value = $(this).val();
    var sdanakey = $(this).prev().text();
    var xkey = sdanakey.replace(/\n/ig, '');
    xkey = xkey.replace(/\s/g, "");
    var xkeyrev = $(".mdl-edit-detail .concept:eq(" + index + ")").text();
    var sdavalue = value.split('.')[0];
    if (xkey != ""){
    arr_wsumberdana[xkeyrev.toLowerCase()] = parseInt(sdavalue.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"));
    }
    });
    arr_wsumberdana["rm"] = parseInt($("#wrm").val().replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"));

    }

    function update_form_pagu_detail(){
        $("#wkdakun").prop("disabled",false);
        $("#frm-edit-detail").validate({
           submitHandler: function(form) {

           }
        });

        if($( "#frm-edit-detail" ).valid()==true){
            var objupdateedittags = extract_tags();
            var obj_wsumberdana = {};
            var tags_val = JSON.stringify(rujukan_all).replace(/\\/g, '');

            tags_val = tags_val.replace(/\"{/g, '{');
            tags_val = tags_val.replace(/\}"/g, '}');
            var hargasat = $("#whargasat").val().split(".")[0];
            var jumlah = $("#wjumlah").val().split(".")[0];

            var id_ruas=$("#wid_ruas").val();
            var id_jembatan=$("#wid_jembatan").val();
            var isidetail=null;
            if(id_ruas=='' || id_ruas == null && id_jembatan=='' || id_jembatan == null)
            {
                isidetail=$("#wdetail").val();
            }else if(id_ruas != '' && id_jembatan == '')
            {
                isidetail=$("#wid_ruas option:selected").text();

            }else
            {
                isidetail=$("#wid_jembatan option:selected").text();

            }
            //alert(isidetail)
            for (var key in arr_wsumberdana) {
                if (arr_wsumberdana.hasOwnProperty(key)) {
                    console.log("keyx =>" + key);
                    if (key == "") {
                    delete arr_wsumberdana[key];
                    }
                }
            }
            console.log("last result");
            console.log(arr_wsumberdana);
            var wsumber=$("#wsumber").val();
            var kdbeban=wsumber.split('_')[0];
            var kdjnsban=wsumber.split('_')[1];
            var kdctarik=wsumber.split('_')[2];
            var idus = $("#wid_usulan").val()
            var objd = get_detail_usulan(idus);
            if (objd.volume < 1) {
                var rrr = "0" + objd.volume;

            } else {
                var rrr = objd.volume;
            }

            var kdgi = $("#wkd_kegiatan").val();
            var kdou = $("#wkd_output").val();
            var a = $("#wid_ruas").val()
            var b = $("#wkdgbkpk").val()
            var c = $("#wkdakun").val()
            var d = $("#wsta_awal").val()
            var e = $("#wsta_akhir").val()
            var f = $("#wid_jembatan").val()
            var g = $("#wlongitude").val()
            var h = $("#wlatitude").val()
            var i = $("#wlongitude2").val()
            var j = $("#wlatitude2").val()
            var k = $("#wvolume").val()
            var l = $("#wsatuan").val()
            var m = parseInt(hargasat.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"))
            var n = parseInt(jumlah.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"))
            var o = $("#wid_paket").val()
            var p = idus
            var q = tags_val
            var r = isidetail
            var s = kdbeban
            var t = kdjnsban
            var u = kdctarik
            var v = $("#wdetail").val()
            var sp = null;
            var flow1 = $("#wflow1").val()
            var flow2 = $("#wflow2").val()
            var flow3 = $("#wflow3").val()
        if(flow3=='') {
            var gbung = flow1+','+flow2;

        }else{
            var gbung = flow1+','+flow2+','+flow3;
        }
            if (kdgi == '2409' && (kdou == '014' || kdou == '015' || kdou == '016' || kdou == '017' || kdou == '018' || kdou == '951' || kdou == '970' || kdou == '994' || kdou == '999'))
            {
                if (b == objd.kdgbkpk && c == objd.kdakun && v == objd.detail && k == rrr && l == objd.satuan && m == objd.hargasat.split('.')[0] && n == objd.jumlah.split('.')[0] && r == objd.detail && wsumber == objd.kdbeban + "_" + objd.kdjnsban + "_" + objd.kdctarik)
                {
                    sp = 'tidakberubah'
                } else
                {
                    sp = 'berubah'
                }
            } else if (kdgi == '2409' && (kdou == '001' || kdou == '002' || kdou == '003' || kdou == '004' || kdou == '005' || kdou == '006' || kdou == '007' || kdou == '008' || kdou == '009' || kdou == '010' || kdou == '011' || kdou == '012' || kdou == '013'))
            {
                if (kdou == '004' || kdou == '005' || kdou == '006' || kdou == '010')
                {
                    if (b == objd.kdgbkpk && c == objd.kdakun && a == objd.id_ruas && f == objd.id_jembatan && g == objd.longitude && h == objd.latitude && k == rrr && l == objd.satuan && m == objd.hargasat.split('.')[0] && n == objd.jumlah.split('.')[0] && r == objd.detail && wsumber == objd.kdbeban + "_" + objd.kdjnsban + "_" + objd.kdctarik)
                    {
                        sp = 'tidakberubah'
                    } else
                    {
                        sp = 'berubah'
                    }
                } else
                {
                    if (b == objd.kdgbkpk && c == objd.kdakun && a == objd.id_ruas && d == objd.sta_awal && e == objd.sta_akhir && g == objd.longitude && h == objd.latitude && i == objd.longitude2 && j == objd.latitude2 && k == rrr && l == objd.satuan && m == objd.hargasat.split('.')[0] && n == objd.jumlah.split('.')[0] && r == objd.detail && wsumber == objd.kdbeban + "_" + objd.kdjnsban + "_" + objd.kdctarik)
                    {
                        sp = 'tidakberubah'
                    } else
                    {
                        sp = 'berubah'
                    }
                }

            } else if (kdgi != '2409')
            {
                if (b == objd.kdgbkpk && c == objd.kdakun && v == objd.detail && k == rrr && l == objd.satuan && m == objd.hargasat.split('.')[0] && n == objd.jumlah.split('.')[0] && r == objd.detail && wsumber == objd.kdbeban + "_" + objd.kdjnsban + "_" + objd.kdctarik)
                {
                    sp = 'tidakberubah'
                } else
                {
                    sp = 'berubah'
                }
            }
                    //new code
                    var register = $("#wregister").val();
                    var caraHitung = $("input[name='wcaraHitung']:checked").val();
                    var inputPHLN = $("#winputPHLN").val(); //prosen
                    var inputRMPdp = $("#winputRMPdp").val(); //prosen
                    var inputRPLNPdp = $("#winputRPLNPdp").val(); //prosen
                    var rinputPHLNs = $("#winputNSPHLN2").val(); //Rp
                    var rinputRMPdps = $("#winputNSRMPdp2").val(); //Rp
                    //var rinputRPLNPdps = $("#winputNSRPLNPdp2").val(); //Rp
                    var jumlah = jumlah.split(',').join('');
                    var rinputPHLN = rinputPHLNs.split(',').join('');
                    var rinputRMPdp = rinputRMPdps.split(',').join('');
                    //var rinputRPLNPdp = rinputRPLNPdps.split(',').join('');
                    var paguPHLN = 0;
                    var paguRMPdp = 0;
                    var paguRPLNPdp = 0;
                    if(caraHitung === '4'){
                        paguPHLN = rinputPHLN;
                        paguRMPdp = rinputRMPdp;
                        //paguRPLNPdp = rinputRPLNPdp;
                    } else if (caraHitung === '1' || caraHitung === '2'){
                        paguPHLN = (jumlah * inputPHLN)/100;
                        paguRMPdp = (jumlah * inputRMPdp)/100;
                        paguRPLNPdp = (jumlah * inputRPLNPdp)/100;
                    }

                    var jumlah2 = parseInt(jumlah.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"));
                    console.log(jumlah);
                    console.log(jumlah2);
                    console.log(caraHitung);
                    console.log(inputPHLN);
                    console.log(inputRMPdp);
                    console.log(inputRPLNPdp);
                    console.log(paguPHLN);
                    console.log(paguRMPdp);
                    console.log(paguRPLNPdp);

                    var inputKPPNph = $("#winputKPPNph").val();
                    var inputKPPNrm = $("#winputKPPNrm").val();
                    var inputKPPNrp = $("#winputKPPNrp").val();
                    var registerpdp = $("#wregisterpdp").val();
                    var vol1 = $("#winputVol1").val();
                    var vol2 = $("#winputVol2").val();
                    var vol3 = $("#winputVol3").val();
                    var vol4 = $("#winputVol4").val();
                    var sat1 = $("#winputSat1").val();
                    var sat2 = $("#winputSat2").val();
                    var sat3 = $("#winputSat3").val();
                    var sat4 = $("#winputSat4").val();
                    var inputHal4 = $("#winputHal4").val();
                    var inputDipa = $("#winputDipa").val();

                    var block = $("input[name='witemblokir']:checked").val();
                    var kdblokir = null;
                    if (block === '1'){
                        kdblokir = '*';
                    } else {
                        kdblokir = null;
                    }
                    var blokirphlns = $('#winputBlokirPHLN').val();
                    var blokirphln = blokirphlns.split(',').join('');
                    var blokirrmps = $('#winputBlokirRMPdp').val();
                    var blokirrmp = blokirrmps.split(',').join('');
                    var blokirrkps =  $('#winputBlokirRPLNPdp').val();
                    var blokirrkp = blokirrkps.split(',').join('');
                    var RphBlokirs =  $('#winputRphBlokir').val();
                    var RphBlokir = RphBlokirs.split(',').join('');

                    var kab = $('#wkabkot').val();
                    var kabkota = kab.split('-')[0];

            var obj_usulan = {
            "id_ruas": $("#wid_ruas").val(),
            "kdgbkpk": $("#wkdgbkpk").val(),
             "kdakun": $("#wkdakun").val(),
            "sta_awal": $("#wsta_awal").val(),
            "sta_akhir": $("#wsta_akhir").val(),
            "id_jembatan": $("#wid_jembatan").val(), //not exists in form exists in table
            "longitude": $("#wlongitude").val(), //not exists in form exists in table
            "latitude": $("#wlatitude").val(), //not exists in form exists in table
            "longitude2": $("#wlongitude2").val(), //not exists in form exists in table
            "latitude2": $("#wlatitude2").val(), //not exists in form exists in table
            "volume": $("#wvolume").val(), //not exists in form exists in table
            "satuan": $("#wsatuan").val(), //not exists in form exists in table
            "hargasat": parseInt(hargasat.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1")),
            "jumlah": jumlah,//parseInt(jumlah.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1")),
            "id_paket": $("#wid_paket").val(),
            "id_usulan": $("#wid_usulan").val(),
            "data_tag" : JSON.stringify(objupdateedittags),
            "tags_val":tags_val,
            "detail":isidetail,
            "kdbeban":kdbeban,
            "kdjnsban":kdjnsban,
            "kdctarik":kdctarik,

            //new code
            "id_ppk": $('#wid_ppk').val(),
            "kdkppn": $('#wkdkppn').val(),
            "thang" : $('#wthang').val(),
            "kd_kegiatan" : $('#wkd_kegiatan').val(),
            "kd_output" : $('#wkd_output').val(),
            "kd_sub_output" : $('#wkd_sub_output').val(),
            "kd_komponen" : $('#wkd_komponen').val(),
            "kd_sub_komponen" : $('#wkd_sub_komponen').val(),
            "register": register,
            "carahitung": caraHitung,
            "prosenphln": inputPHLN,
            "prosenrmp": inputRMPdp,
            "prosenrkp": inputRPLNPdp,
            "kppnphln": inputKPPNph,
            "kppnrmp": inputKPPNrm,
            "kppnrkp": inputKPPNrp,
            "paguphln": paguPHLN,
            "pagurmp": paguRMPdp,
            //"pagurkp": paguRPLNPdp,
            "regdam": registerpdp,
            "ket": inputHal4,
            "ket2": inputDipa,
            "vol1": vol1,
            "vol2": vol2,
            "vol3": vol3,
            "vol4": vol4,
            "sat1": sat1,
            "sat2": sat2,
            "sat3": sat3,
            "sat4": sat4,
            "geom": $('#wgeom').val(),
            "kdkabkota": kabkota, //$("#wkabkot").val(),
            "kdlokasi": $("#wprov").val(),
            "status_perubahan": sp,
            "perubahan":gbung,
            "kd_tahapan":kdtahap,
            "kdsbu": $('#wkdsbu').val(),
            "kdblokirs" :kdblokir,
            "kdblokir" :$('#wkd_blokir').val(),
            "urblokir" :$('#wurblokir').val(),
            "blokirphln": blokirphln,
            "blokirrmp": blokirrmp,
            "blokirrkp": blokirrkp,
            "rphblokir": RphBlokir,
            "header2": $('#wheader2').val(),
            "longitude3": $("#wlongitude3").val(),
            "latitude3": $("#wlatitude3").val(),
            "longitude4": $("#wlongitude4").val(),
            "latitude4": $("#wlatitude4").val()
    };
    console.log(obj_usulan);

            var url = "<?php echo base_url("pagu_tahapan/update_detail_usulan"); ?>";
            var params = {"formData": obj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            if ($("#wid_ruas").val() != "#" && $("#wsta_awal").val() != "#" && $("#wsta_akhir").val() != "#" && $("#whargasat").val() != "#"){
            $.post(url, params)
                    .done(function (data) {
                        if(data === '1'){
                            alert('penanganan pada ruas ini sudah ada di paket lain');
                        } else if (data === '2'){
                            alert('penanganan pada jembatan ini sudah ada di paket lain');
                        } else if (data === '3'){
                            alert('Akun sudah ada');
                        }else {
                            var tlist_paket = $('#tlist_paket').DataTable();
                            tlist_paket.ajax.reload();
                            $('#modal-edit-detail').modal('hide');
                        }
                    //var tlist_paket = $('#tlist_paket').DataTable();
                    //tlist_paket.ajax.reload();
                    //$('#modal-edit-detail').modal('hide');
                    })
                    .fail(function () {
                    alert("error");
                    })
            } else{
            alert("harap lengkap formulir isian field mandatory")
            }
      }else{
          alert("Simpan data gagal, silahkan lihat informasi di setiap field untuk memperbaiki formulir isian")
      }


    }

    function bind_combo_wps() {
    $("#wps_kode").empty();
    var data = get_wps();
    //alert(data);
    for (var i = 0; i <= data.length - 1; i++) {
    $("#wps_kode").append("<option value=" + data[i].wps_kode + ">" + data[i].wps_nama + "</option>");
            console.log(data[i].wps_kode);
    }
    }

    function wps()
    {
    var x = $("#wps_kode").val();
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_kws/') ?>" + x,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            console.log(data);
                    x = data;
            lookkws(x);

            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    }

    function lookkws(x)
    {
    $("#kws_kode").empty();
    var dataa = x;
    var html_looku = "<option value=''>--Pilih KWS----</option>";
    $("#kws_kode").append(html_looku);
    for (var i = 0; i <= dataa.length - 1; i++) {

    var html_lookup = ["<option value=" + dataa[i].kws_kode + " >",
            dataa[i].kws_nama,
            "</option>"
    ].join("\n");
    $("#kws_kode").append(html_lookup);
    }
    }
    function kws()
    {
    var x = $("#kws_kode").val();;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_sub/') ?>" + x,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            console.log(data);

                    x = data;
            looksub(x);

            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    }

    function looksub(x)
    {
    $("#subkw").empty();
    var dataa = x;
    var html_looku = "<option value=''>--Pilih Sub Kawasan----</option>";
    $("#subkw").append(html_looku);
    for (var i = 0; i <= dataa.length - 1; i++) {
    console.log(dataa[i].subkawasan_nama);
    var nm = dataa[i].subkawasan_nama;
    var html_lookup = ['<option value="' + nm + '" >',
            dataa[i].subkawasan_nama,
            '</option>'
    ].join("\n");
    $("#subkw").append(html_lookup);
    }
    }

    function btnProsesVerifikasiIndi(data) {

    var adata = data.split('~');
    var id_paket = adata[1];
    var idx = parseInt(adata[2], 10);
    console.log('idx ' + idx);
    var sstat = adata[0].split('|')[idx];
    var astat = sstat.split('_');
    var roleberhak = astat[0];

    var thedata = xhrdata.data.filter(x => x[1] == id_paket)[0]; //kolom 18 (id paket) sama dengan id
    console.log(thedata);
    var waydata = {
    status_idx: idx,
            id_paket: id_paket,
            nama_sub_komponen: (thedata[3] ? thedata[3] : ''),
            nmgiat: (thedata[4] ? thedata[4] : ''),
            nmoutput: (thedata[5] ? thedata[5] : ''),
            nmsoutput: (thedata[6] ? thedata[6] : ''),
            nmkmpnen: (thedata[7] ? thedata[7] : ''),
            kode_satker: (thedata[23] ? thedata[23] : ''),
            modeform: 'tambah',
            tahun_anggaran: (thedata[8] ? thedata[8] : ''),
            volume: (thedata[7] ? thedata[7] : ''),
            satuan: (thedata[6] ? thedata[6] : ''),
            jumlah: (thedata[8] ? thedata[8] : ''),
            eval1: (thedata[11] ? thedata[11] : ''),
            eval2: (thedata[13] ? thedata[13] : ''),
            nmsatker_pengusul: (thedata[24] ? thedata[24] : '')
    }

    way.set('formData', waydata);
    $('#modalTitle').text('Verifikasi');
    console.log("======ready to show form");
    console.log("role yang aktif saat ini: " + role);
    console.log("role alias yang aktif saat ini: " + roledesc);
    console.log("role yang harusnya melakukan approval: " + roleberhak);
    console.log('kode satker pengusul: ' + waydata.kode_satker);
    console.log('kode satker user saat ini: ' + user_satker);

    var rolePusat = ['pemrog', 'adps', 'kpsj', 'konstruksi', 'pavement', 'structures', 'metrokobes', 'jbh', 'mejd', 'tanah', 'gml', 'lkj', 'kompetensi', 'sditjen', 'buk', 'ditbang', 'ditpreservasi', 'ditjembatan', 'ditjbh', 'pjj'];
    console.log('child satker: ' + child_satker);
    var aChildSatker = child_satker.replace(/'/g, "").split(',');
    console.log(aChildSatker);
    if (role == roleberhak) {
    if ((waydata.kode_satker == user_satker) && (!isEmpty(user_satker))) {
    //boleh approve
    console.log('boleh aprove, role cocok, satker sama');
    $('#modal-edit').modal('show');
    $('.elem-eval_usulan1').show();
    $('.elem-eval_usulan2').hide();
    } else if (aChildSatker.indexOf(waydata.kode_satker) >= 0) {
    //jika user_satker yang sekarang login , punya child yang salah satunya adalah satker pengusul
    console.log('boleh aprove, role cocok, satker seinduk');
    $('#modal-edit').modal('show');
    $('.elem-eval_usulan1').show();
    $('.elem-eval_usulan2').hide();
    } else {
    if (rolePusat.indexOf(roledesc) >= 0) {
    //boleh approve
    console.log('boleh aprove, role cocok, oleh satker pusat');
    $('#modal-edit').modal('show');
    } else {
    console.log('gak boleh approve karena meskipun role sama, tapi beda satker');
    alert('Verifikasi tidak bisa dilakukan (satker tidak sesuai!)');
    }
    }
    } else {
    console.log('gak boleh approve karena rolenya beda');
    alert('Verifikasi tidak bisa dilakukan (role tidak sesuai!)');
    }

    }
    function bindTagging_rj_rams(string_rams){
    var arr_rj_rams = string_rams.split(",");
    for (var i = 0; i <= arr_rj_rams.length - 2; i++){
    alert(arr_rj_rams[i]);
    }
    }

    function dtTagging(strTag) {
        console.log(strTag);
    var str = strTag.split("::");
    var id = str[1];
    var label = str[0] + '|' + str[1];
    var rujukan = str[0];
    var elt = $('.div-tags');
    elt.tagsinput({
    tagClass: function (item) {
    switch (item.rujukan) {
    case 'DPR':
            return 'label label-info';
    case 'PEMDA':
            return 'label label-default';
    case 'SIPRO':
            return 'label label-primary';
    case 'IRMS':
            return 'label label-success';
    case 'RAMS':
            return 'label label-info';
    case 'EPROG':
            return 'label label-warning';
    case 'RENSTRA':
            return 'label label-danger';
    }
    },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to set id of tag
            itemText: 'text' // this will be used to set text of tag
    });
    elt.tagsinput('add', {value: id, text: label, rujukan: rujukan});
    }

    function dtTaggingTbhDetail(strTag) {

    var str = strTag.split("::");
    var id = str[1];
    var label = str[0] + '|' + str[1];
    var rujukan = str[0];
    var elt = $('.div-tags2');
    elt.tagsinput({
    tagClass: function (item) {
    switch (item.rujukan) {
    case 'DPR':
            return 'label label-info';
    case 'PEMDA':
            return 'label label-default';
    case 'SIPRO':
            return 'label label-primary';
    case 'IRMS':
            return 'label label-success';
    case 'RAMS':
            return 'label label-info';
    case 'EPROG':
            return 'label label-warning';
    case 'RENSTRA':
            return 'label label-danger';
    }
    },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to set id of tag
            itemText: 'text' // this will be used to set text of tag
    });
    elt.tagsinput('add', {value: id, text: label, rujukan: rujukan});
    }

    function dtTaggingEditDetail(strTag) {

    var str = strTag.split("::");
    var vid = str[1];
    var vlabel = str[0] + '|' + str[1];
    var vrujukan = str[0];

    $('#wtags').tagsinput({
    allowDuplicates: true,
            itemValue: 'value', // this will be used to set id of tag
            itemText: 'text', // this will be used to set text of tag
            tagClass: function(item) {
            switch (item.tipe_rujukan) {
            case 'DPR':
                    return 'label label-info';
            case 'PEMDA':
                    return 'label label-default';
            case 'SIPRO':
                    return 'label label-primary';
            case 'IRMS':
                    return 'label label-success';
            case 'RAMS':
                    return 'label label-info';
            case 'EPROG':
                    return 'label label-warning';
            case 'RENSTRA':
                    return 'label label-danger';
            }
            }
    });
    $('#wtags').tagsinput('add', {id:vid, label:vlabel, tipe_rujukan:vrujukan});
    }

    function dtTaggingEdit(strTag) {

    var str = strTag.split("::");
    var id = str[1];
    var label = str[0] + '|' + str[1];
    var rujukan = str[0];
    var elt = $('.div-tags');
    elt.tagsinput({
    tagClass: function (item) {
    switch (item.rujukan) {
    case 'DPR':
            case 'PEMDA':
            return 'label label-default';
    case 'SIPRO':
            return 'label label-primary';
    case 'IRMS':
            return 'label label-success';
    case 'RAMS':
            return 'label label-info';
    case 'EPROGRAM':
            return 'label label-warning';
    case 'RENSTRA':
            return 'label label-danger';
    }
    },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to set id of tag
            itemText: 'text' // this will be used to set text of tag
    });
    elt.tagsinput('add', {value: id, text: label, rujukan: rujukan});
    }

    function handleKegiatan(el)
    {
        if(el.value=="")
        {
            $("#kd_output").prop("disabled",true)
            $("#kd_output").empty();
            $('#kd_output').append(new Option("--Pilih--",""));

          $("#kd_sub_output").prop("disabled",true);
          $("#kd_komponen").prop("disabled",true);

        }else
        {
            $("#kd_output").prop("disabled",false)
            refreshCombobox('kd_output', 30, 'kdgiat', el.value);
          $("#kd_sub_output").prop("disabled",true);
          $("#kd_komponen").prop("disabled",true);
        }
//    $("#kd_output").prop("disabled",false)

    $('#kd_sub_output').empty();
    $('#kd_sub_output').append(new Option("--Pilih--",""));
    $('#kd_komponen').empty();
    $('#kd_komponen').append(new Option("--Pilih--",""));
    if (el.value == '2409'){
    $(".divRuas").show();
    $("#VolumeKegiatan").hide();
    } else{
    $(".divRuas").hide();
    $("#VolumeKegiatan").show();
    $(".id_radioRuas").hide();
    $(".id_radioJembatan").hide();
    $(".id_radioHonor").show();
    $("input[name='xradioVolume']").val([3]);
    setTimeout(function(){handleRadioVolume('x'); }, 3000);
    }

    }

    function handleKegiatanMaster(el)
    {
         if(el.value=="")
        {
            $("#zkd_output").prop("disabled",true)
            $("#zkd_output").empty();
            $('#zkd_output').append(new Option("--Pilih--",""));

          $("#zkd_sub_output").prop("disabled",true);
          $("#zkd_komponen").prop("disabled",true);

        }else
        {
            $("#zkd_output").prop("disabled",false)
            refreshCombobox('zkd_output', 30, 'kdgiat', el.value);
          $("#zkd_sub_output").prop("disabled",true);
          $("#zkd_komponen").prop("disabled",true);
        }


    $('#zkd_sub_output').empty();
    $('#zkd_sub_output').append(new Option("--Pilih--",""));
    $('#zkd_komponen').empty();
    $('#zkd_komponen').append(new Option("--Pilih--",""));
    }

    function handleJnsBelanja(el)
    {
    if(el.value=='')
        {
            $("#kdakun").prop("disabled",true);
            $("#kdakun").empty();
            $("#kdakun").append("<option value=''>--Pilih--</option>")
              $("#ykdakun").prop("ykdakun",true)
        }
        else
            {
                 $("#kdakun").prop("disabled",false);
                 $("#wkdakun").prop("disabled",false);
//        refreshCombobox('kdakun', 37, 'kdgbkpk', el.value);
//        refreshCombobox('ykdakun', 37, 'kdgbkpk', el.value);
              $("#ykdakun").prop("disabled",false)
              refreshComboboxSearch('kdakun', 37, 'kdgbkpk', el.value);
              refreshComboboxSearch('ykdakun', 37, 'kdgbkpk', el.value);
              refreshComboboxSearch('wkdakun', 37, 'kdgbkpk', el.value);
              refreshComboboxSearch('zkdakun', 37, 'kdgbkpk', el.value);
            }

    }


    function handleSoutput2(el) {
    var kdgiat = $('#kd_kegiatan_sel').val();
    var kdoutput = $('#kd_output_sel').val();
    var kdsoutput = el.value;
    var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

//    var kdgiat = $('#zkd_kegiatan_sel').val();
//    var kdoutput = $('#zkd_output_sel').val();
//    var kdsoutput = el.value;
//    var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
//    refreshCombobox4('zkd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);
    }

    function handleSoutput(el, prefix) {
        //update code
        $('#'+prefix+'panelOutput').hide();
        $('#'+prefix+'panelSubOutput').show();
        $('#'+prefix+'panelKomponen').hide();

                if(el.value=="")
        {
            $("#kd_sub_output").prop("disabled",true)
            $("#kd_sub_output").empty();
            $('#kd_sub_output').append(new Option("--Pilih--",""));

            $("#zkd_sub_output").prop("disabled",true)
            $("#zkd_sub_output").empty();
            $('#zkd_sub_output').append(new Option("--Pilih--",""));
        }
        else
        {
    $("#kd_komponen").prop("disabled",false)
    $("#zkd_komponen").prop("disabled",false)
    var kdgiat = $('#kd_kegiatan').val();
    var kdoutput = $('#kd_output').val();
    var kdsoutput = el.value;
    var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

    var ykdgiat = $('#kd_kegiatan').val();
    var ykdoutput = $('#kd_output').val();
    var ykdsoutput = el.value;
    var yvalSelect = ykdgiat + "::" + ykdoutput + "::" + ykdsoutput;
    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', yvalSelect);


    var yykdgiat = $('#zkd_kegiatan').val();
    var yykdoutput = $('#zkd_output').val();
    var yykdsoutput = el.value;
    var yyvalSelect = yykdgiat + "::" + yykdoutput + "::" + yykdsoutput;
    refreshCombobox4('zkd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', yyvalSelect);

        }
        $('#kd_sub_output').append(new Option("--Pilih--",""));
        $('#zkd_sub_output').append(new Option("--Pilih--",""));

        //update code
        var thang = null;
        var giat = null;
        var out = null;
        var sout = null;
        if (prefix === 'x'){
            thang = $('#thang').val();
            giat = $('#kd_kegiatan').val();
            out = $('#kd_output').val();
            sout = $('#kd_sub_output').val();
        } else if ( prefix === 'y'){
            thang = $('#ythang_sel').val();
            giat = $('#ykd_kegiatan_sel').val();
            out = $('#ykd_output_sel').val();
            sout = $('#ykd_sub_output_sel').val();
        } else {
            thang = $('#'+prefix+'thang').val();
            giat = $('#'+prefix+'kd_kegiatan').val();
            out = $('#'+prefix+'kd_output').val();
            sout = $('#'+prefix+'kd_sub_output').val();
        }

        get_rfsatuan(giat, out, prefix);
        get_soutput(giat, out, sout, thang, prefix);

    }

    function handleOutput(el, prefix) {
        //update code
        $('#'+prefix+'panelOutput').show();
        $('#'+prefix+'panelSubOutput').hide();
        $('#'+prefix+'panelKomponen').hide();
        var date = new Date();
        var now = date.getFullYear();

        $('#'+prefix+'Thn1').text(parseInt(tahun_anggaran) - 1);
        $('#'+prefix+'Thn2').text(parseInt(tahun_anggaran));
        $('#'+prefix+'Thn3').text(parseInt(tahun_anggaran) + 1);
        $('#'+prefix+'Thn4').text(parseInt(tahun_anggaran) + 2);
        $('#'+prefix+'Thn5').text(parseInt(tahun_anggaran) + 3);

        var kdgi=$("#kd_kegiatan").val();
        var kdou=$("#kd_output").val();
        if(el.value=="")
        {
            $("#kd_sub_output").prop("disabled",true)
            $("#kd_sub_output").empty();
            $('#kd_sub_output').append(new Option("--Pilih--",""));

            $("#zkd_sub_output").prop("disabled",true)
            $("#zkd_sub_output").empty();
            $('#zkd_sub_output').append(new Option("--Pilih--",""));

        }
        else
        {

            $("#kd_sub_output").prop("disabled",false)
                var kdgiat = $('#kd_kegiatan').val();
                 var kdoutput = el.value;
                 var valSelect = kdgiat + "::" + kdoutput;
             refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);
             var selectedText = el.options[el.selectedIndex].text;

            $("#zkd_sub_output").prop("disabled",false)
                var kdgiat = $('#zkd_kegiatan').val();
                 var kdoutput = el.value;
                 var valSelect = kdgiat + "::" + kdoutput;
             refreshCombobox4('zkd_sub_output', 44, 'kdgiat::kdoutput', valSelect);
             var selectedText = el.options[el.selectedIndex].text;

            if(kdgi=='2409' && (kdou=='014' || kdou=='015' || kdou=='016' || kdou=='017' || kdou=='018' || kdou=='951' || kdou=='970' || kdou=='994' || kdou=='999'))
               {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                     $(".uraians").show();
                   //$("#fsk").hide();
                  // alert("ilang");
                    $("#VolumeKegiatan").show();
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").show();
                    $("input[name='xradioVolume']").val([3]);
                    setTimeout(function(){handleRadioVolume('x'); }, 1000);
               }
               else if(kdgi=='2409' && (kdou=='001' || kdou=='002' || kdou=='003' || kdou=='004' || kdou=='005' || kdou=='006' || kdou=='007' || kdou=='008' || kdou=='009' || kdou=='010' || kdou=='011' || kdou=='012' || kdou=='013'))
                {
                    if(kdou=='004' || kdou=='005' || kdou=='006' || kdou=='010')
                    {
                     $(".ruasjalan").show();
                     $(".ruasjalan").removeClass('col-md-6');
                     $(".ruasjalan").addClass('col-md-12');
                    $(".staw").hide();
                    $("#sta_awal").empty();
                    $("#sta_akhir").empty();
                    $(".jembatans").show();
                    $("#l1").text("Longitude (X1)");
                    $("#l2").text("Latitude (Y1)");
                    $("#longitudes").empty();
                    $("#latitudes").empty();
                    $("#longitude").empty();
                    $("#latitude").empty();
                    $(".longs").show();
                    $(".longsa").hide();
                    $("#VolumeKegiatan").show();
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").show();
                    $(".id_radioHonor").hide();
                    $("input[name='xradioVolume']").val([2]);
                    setTimeout(function(){handleRadioVolume('x'); }, 1000);
                    }
                    else
                    {
                    $(".ruasjalan").show();
                   $(".ruasjalan").removeClass('col-md-12');
                     $(".ruasjalan").addClass('col-md-6');
                    $(".staw").show();
                    $(".jembatans").hide();
                    $("#xid_jembatan").empty();
                    $("#l1").text("Longitude (X1)");
                    $("#l2").text("Latitude (Y1)");
                     $("#l3").text("Longitude (X2)");
                    $("#l4").text("Latitude (Y2)");
                    $(".longs").show();
                    $(".longsa").show();
                    $("#VolumeKegiatan").show();
                    $(".id_radioRuas").show();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").hide();
                    $("input[name='xradioVolume']").val([1]);
                    setTimeout(function(){handleRadioVolume('x'); }, 1000);
                    }

                }
                else if(kdgi !='2409')
                {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                      $(".uraians").show();
                      $("#VolumeKegiatan").show();
                      $(".id_radioRuas").hide();
                      $(".id_radioJembatan").hide();
                      $(".id_radioHonor").show();
                      $("input[name='xradioVolume']").val([3]);
                      setTimeout(function(){handleRadioVolume('x'); }, 1000);
                }

        }
        $('#kd_sub_output').append(new Option("--Pilih--",""));
         $("#kd_komponen").prop("disabled",true)
//    $('#kd_sub_output').empty();
//    $('#kd_sub_output').append("<option>--pilih</option>");
    $('#kd_komponen').empty();
    $('#kd_komponen').append(new Option("--Pilih--",""));

    $('#zkd_sub_output').append(new Option("--Pilih--",""));
         $("#zkd_komponen").prop("disabled",true)
//    $('#kd_sub_output').empty();
//    $('#kd_sub_output').append("<option>--pilih</option>");
    $('#zkd_komponen').empty();
    $('#zkd_komponen').append(new Option("--Pilih--",""));
    var x = function () {
    var defer = $.Deferred();

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var y = function () {
    var defer = $.Deferred();

    if (kdoutput == '001' || kdoutput == '002' || kdoutput == '003' || kdoutput == '004' || kdoutput == '005' || kdoutput == '006' || kdoutput == '007' || kdoutput == '008' || kdoutput == '009' || kdoutput == '010' || kdoutput == '011' || kdoutput == '012' || kdoutput == '013')
    {
    $('.divNonFisik').hide();
    $('#detail').prop('disabled', true);
    $('.divRuas').show();
    $("#xid_ruas").prop('disabled', false);
    $("#id_ruas").prop('disabled', false);
    $("#sta_awal").prop('disabled', false);
    $("#sta_akhir").prop('disabled', false);
    $("#treatment").prop('disabled', false);
    $("#xid_jembatan").prop('disabled', false);
    $("#longitude").prop('disabled', false);
    $("#latitude").prop('disabled', false);
    } else
    {
    $('.divNonFisik').show();
    $('#detail').prop('disabled', false);
    $('.divRuas').hide();
    $("#xid_ruas").prop('disabled', true);
    $("#sta_awal").prop('disabled', true);
    $("#sta_akhir").prop('disabled', true);
    $("#treatment").prop('disabled', true);
    $("#xid_jembatan").prop('disabled', true);
    $("#longitude").prop('disabled', true);
    $("#latitude").prop('disabled', true);
    }

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    x().then(y);

        //update code
        var thang = null;
        var giat = null;
        var out = null;
        if (prefix === 'x'){
            thang = $('#thang').val();
            giat = $('#kd_kegiatan').val();
            out = $('#kd_output').val();
        } else if ( prefix === 'y'){
            thang = $('#ythang_sel').val();
            giat = $('#ykd_kegiatan_sel').val();
            out = $('#ykd_output_sel').val();
        } else {
            thang = $('#'+prefix+'thang').val();
            giat = $('#'+prefix+'kd_kegiatan').val();
            out = $('#'+prefix+'kd_output').val();
        }
        $('#'+prefix+'inputTAAwal').val(parseInt(tahun_anggaran));
        get_output(giat, out, thang, prefix);
        //get_sum_sout(giat, out, thang, prefix);

    }

    function handleOutput2(el) {

    var kdgiat = $('#kd_kegiatan_sel').val();
    var kdoutput = el.value;
    var valSelect = kdgiat + "::" + kdoutput;
    refreshCombobox4('kd_sub_output_sel', 44, 'kdgiat::kdoutput', valSelect);

    var selectedText = el.options[el.selectedIndex].text;
    var x = function () {
    var defer = $.Deferred();

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    var y = function () {
    var defer = $.Deferred();
    var sat = $('#satuan').val();
    if (sat == 'km' || sat == 'm')
    {
    $('.divNonFisik').hide();
    $('#detail').prop('disabled', true);
    $('.divRuas').show();
    $("#xid_ruas").prop('disabled', false);
    $("#sta_awal").prop('disabled', false);
    $("#sta_akhir").prop('disabled', false);
    $("#treatment").prop('disabled', false);
    $("#xid_jembatan").prop('disabled', false);
    $("#longitude").prop('disabled', false);
    $("#latitude").prop('disabled', false);
    } else
    {
    $('.divNonFisik').show();
    $('#detail').prop('disabled', false);
    $('.divRuas').hide();
    $("#xid_ruas").prop('disabled', true);
    $("#sta_awal").prop('disabled', true);
    $("#sta_akhir").prop('disabled', true);
    $("#treatment").prop('disabled', true);
    $("#xid_jembatan").prop('disabled', true);
    $("#longitude").prop('disabled', true);
    $("#latitude").prop('disabled', true);
    }

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 1000);
    return defer;
    };
    x().then(y);

    }
    function handleRuaskabkot(el)
    {
      var ruas=el.value
      //console.log(ruas);
       // alert(el.value)
        refreshComboboxOutput('xid_ruas', 70, 'KODE_KABKOTA',ruas.split('-')[1]);
         refreshComboboxOutput('id_ruas', 70, 'KODE_KABKOTA',ruas.split('-')[1]);
         refreshComboboxOutput('wid_ruas', 70, 'KODE_KABKOTA',ruas.split('-')[1]);
    }
    function handleRuas(el, prefix)
    {
        $('#latitude').empty();
        $('#longitude').empty();
        $('#latitude2').empty();
        $('#longitude2').empty();
        $('#'+prefix+'latitude').empty();
        $('#'+prefix+'longitude').empty();
        $('#'+prefix+'latitude2').empty();
        $('#'+prefix+'longitude2').empty();
//        alert($("#sta_awal").val())
//        alert($("#sta_akhir").val())
        if(el.value=='#')
            {
                $("#sta_awal").prop('disabled',true);
                $("#sta_akhir").prop('disabled',true);
                $("#sta_awal").empty();
                $("#sta_akhir").empty();
                $("#sta_awal_sel").prop('disabled',true);
                $("#sta_akhir_sel").prop('disabled',true);
                $("#sta_awal_sel").empty();
                $("#sta_akhir_sel").empty();
                $("#wsta_awal").prop('disabled',true);
                $("#wsta_akhir").prop('disabled',true);
                $("#wsta_awal").empty();
                $("#wsta_akhir").empty();
                  alert('Harap Pilih Ruas Yang Benar');
            }
        else
            {


   // refreshCombobox2('xid_jembatan', 32, 'linkid', el.value);

//    refreshComboboxstax('sta_awal', el.value);
//    refreshComboboxstax('sta_akhir', el.value);

     $("#sta_awal").prop('disabled',false);
     $("#sta_akhir").prop('disabled',false);
      $("#sta_awal_sel").prop('disabled',false);
     $("#sta_akhir_sel").prop('disabled',false);
       $("#yid_jembatan").prop('disabled',false);

            $("#wsta_awal").prop('disabled',false);
     $("#wsta_akhir").prop('disabled',false);
     $("#wid_jembatan").prop('disabled',false);
     refreshComboboxSTASatker('sta_awal', el.value);
    refreshComboboxSTASatker('sta_akhir', el.value);
    //modal tambah detail
//    refreshComboboxSTASatker('id_jembatan', 32, 'linkid', el.value);
    refreshComboboxSTASatker('sta_awal_sel', el.value);
    refreshComboboxSTASatker('sta_akhir_sel', el.value);
     //initComboboxJembatan('xid_jembatan', 32, 'linkid', el.value);
    initComboboxJembatan('xid_jembatan', el.value);
     initComboboxJembatan('yid_jembatan', el.value);
    refreshComboboxSTASatker('wsta_awal', el.value);
    refreshComboboxSTASatker('wsta_akhir', el.value);
    //modal edit detail
       //  initComboboxJembatan('wid_jembatan', 32, 'linkid', el.value);
       initComboboxJembatan('wid_jembatan', el.value);
  //  refreshCombobox2('wid_jembatan', 32, 'linkid', el.value);
//    var tab=$('#tirmsv3_wp').DataTable();
//    tab.destroy();
//    $('#tirmsv3_wp td').empty();
//    irms();
    if ($("#xid_ruas").val() != "") {
    $("#detail").val($("#id_ruas").val());
    }
    if ($("#xid_jembatan").val() != "") {
    $("#xdetail").val($("#id_ruas").val());
    }

    //modal tambah
    var urutans=$("#urutans").val()
    var tab=$('#tirmsv3_wp'+urutans).DataTable();
    tab.destroy()
    irms(urutans);
    var itirmsv3_wp = $('#tirmsv3_wp'+urutans).DataTable();
    itirmsv3_wp.ajax.reload();
//
//    var tirmsv3_wp3 = $('#tirmsv3_wp3').DataTable();
//    tirmsv3_wp3.ajax.reload();

//    var tirmsv3_wp4 = $('#tirmsv3_wp4').DataTable();
//    tirmsv3_wp4.ajax.reload();
//    //modal tambah detail
//    var xtirmsv3_wp = $('#xtirmsv3_wp').DataTable();
//    xtirmsv3_wp.ajax.reload();
//    //modal edit detail
//    var wtirmsv3_wp = $('#wtirmsv3_wp').DataTable();
//    wtirmsv3_wp.ajax.reload();
            }
//         $("#sta_awal").append("<option value='#'>--Pilih--</option>");
//         $("#sta_akhir").append("<option value='#'>--Pilih--</option>");
    console.log(el.value);
        if(el.value !== null){
            maxRuas(el.value, prefix);
        }
    }
//    function handlestas(st)
//    {
//        alert(st.value)
//        if(st.value=='#')
//            {
//                 $("#sta_akhir").prop("disabled",true)
//            }else
//
//            {
//        $("#sta_akhir").prop("disabled",false)
//
//            }
//    }

function handleSTA(mode, el)
    {
        var timeout = null;
        clearTimeout(timeout);
        timeout = setTimeout(function() {
            var val2 = $("#xid_ruas").val();
            var sta = $("#sta_awal").val();
            var ste = $("#sta_akhir").val();

            setSTACoord(mode, el.value, val2, 'longitude');
            setSTACoord(mode, el.value, val2, 'latitude');

            setSTACoord(mode, el.value, val2, 'longitude2');
            setSTACoord(mode, el.value, val2, 'latitude2');

            if(val2 !== '' && sta !== '' && ste !== '') {
                getGEOMRuas('xgeom', val2, sta, ste);
            }
        }, 3000);
    }

function handleSTAdetail(mode, el)
    {
        var timeout = null;
        clearTimeout(timeout);
        timeout = setTimeout(function() {
            var val2 = $("#id_ruas").val();
            var sta = $("#sta_awal_sel").val();
            var ste = $("#sta_akhir_sel").val();

            setSTACoord(mode, el.value, val2, 'ylongitude');
            setSTACoord(mode, el.value, val2, 'ylatitude');

            setSTACoord(mode, el.value, val2, 'ylongitude2');
            setSTACoord(mode, el.value, val2, 'ylatitude2');

            if(val2 !== '' && sta !== '' && ste !== '') {
                getGEOMRuas('ygeom', val2, sta, ste);
            }
        }, 3000);
    }

function handleSTAEditdetail(mode, el)
    {
        var timeout = null;
        clearTimeout(timeout);
        timeout = setTimeout(function() {
            var val2 = $("#wid_ruas").val();
            var sta = $("#wsta_awal").val();
            var ste = $("#wsta_akhir").val();

            if (kdtahap === 'KO'){
                setSTACoord(mode, el.value, val2, 'wlongitude3');
                setSTACoord(mode, el.value, val2, 'wlatitude3');

                setSTACoord(mode, el.value, val2, 'wlongitude4');
                setSTACoord(mode, el.value, val2, 'wlatitude4');

            } else {
                setSTACoord(mode, el.value, val2, 'wlongitude');
                setSTACoord(mode, el.value, val2, 'wlatitude');

                setSTACoord(mode, el.value, val2, 'wlongitude2');
                setSTACoord(mode, el.value, val2, 'wlatitude2');
            }

            if(val2 !== '' && sta !== '' && ste !== '') {
                getGEOMRuas('wgeom', val2, sta, ste);
            }
        }, 3000);
    }



    function handleJembatan(el, prefix)
    {

//    setInputVal2('longitude', 40, 'id_jembatan', el.value);
//    setInputVal2('latitude', 41, 'id_jembatan', el.value);
//console.log("xid"+$("#xid_ruas").val())

console.log("detail"+$("#id_ruas").val())
console.log("wid"+$("#wid_ruas").val())
console.log("paket"+$("#xid_ruas").val())
///paket

    setlonlat('longitude', el.value);
    setlonlat('latitude', el.value);
//tambahdetail


    setlonlat('ylongitude', el.value);
    setlonlat('ylatitude', el.value);
 //

    setlonlat('wlongitude', el.value);
    setlonlat('wlatitude', el.value);

    //new code
    var lat = null;
    var lon = null;
    if (prefix === 'x'){
        lat = $('#latitude').val();
        lon = $('#longitude').val();
    } else {
        lat = $('#'+prefix+'latitude').val();
        lon = $('#'+prefix+'longitude').val();
    }

    if(el.value !== ''){
        $('#'+prefix+'geom').val(lon+' '+lat);
    }

    }

//    function setlonlats(divname, selvalue1, selvalue2) {
//        if(selvalue1 != null)
//        {
//    url = "exercise_wp/get_lonlat/"+selvalue1+"/"+selvalue2;
//    $.get(url).done(function (data) {
//        //alert(data);
//
//        jdata = JSON.parse(data);
//        console.log(jdata);
////
//           $('#'+divname).empty();
//        var lonlat = jdata.val;
//        var lon = lonlat.split("||")[0];
//        var lat = lonlat.split("||")[1];
//
//        if(divname=='longitude') {
//            $('#'+ divname).val(lon);
//        }
//        else if(divname=='latitude')
//        {
//            $('#'+ divname).val(lat);
//        }
//        else if(divname=='ylongitude')
//        {
//            $('#'+ divname).val(lon);
//        }
//        else if(divname=='ylatitude')
//        {
//            $('#'+ divname).val(lat);
//        }
//
//        else if(divname=='wlongitude')
//        {
//            $('#'+ divname).val(lon);
//        }
//        else if(divname=='wlatitude')
//        {
//            $('#'+ divname).val(lat);
//        }
//
//
////            $.each(jdata, function (i, el) {
//        //$('#' + divname).val(jdata.val);
////            });
//
//        //if (selvalue != '') $('#'+divname).val(selvalue)
//    })
//            .fail(function () {
//                alert("error");
//            })
//            .always(function () {
//                // alert("finished");
//            });
//        }
//}



    function dtEditRowDetail(id) {

    $(".decformat2").keyup(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
    var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ",").replace(/\D/g, "");
    $('#hargasat').val(decform);
    var hargasat = $('#hargasat').val();
    var volume = $('#volume').val();
    var hasil = volume * hargasat;
    $("#totalpagu").val(hasil);
    $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    $('#rm').val(hasil);
    });
    $(".decformat").change(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
    });
    $(".decformat2").select(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
    });

    $('#thang_sel').prop('disabled', true);
    $('#kd_kegiatan_sel').prop('disabled', true);
    $('.form-group').removeClass('has-error'); // clear error class
    $('.help-block').empty(); // c
    $('#modeform').val('edit_detail');
    $('#modal-detail').modal('show'); // show bootstrap modal when complete loaded
    $('.tbhItem').text('Edit Detail Paket');
    $('.rujuk').show(); // Set title to Bootstrap modal title
    //
    //Ajax Load data from ajax
    $.ajax({
    url: "<?php echo base_url(); ?>pagu_tahapan/ajax_edit/detail/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {
            $('#id_paket').val(data.id_paket);
            $('#id_usulan').val(id);
            $('#thang_sel').val(data.thang);
            $('#id_ppk_sel').val(data.id_ppk);
            $('#kdkppn_sel').val(data.kdkppn);
            $('[name="thang_sel"]').val(data.thang);
            $('[name="id_ppk_sel"]').val(data.id_ppk);
            $('[name="kdkppn_sel"]').val(data.kdkppn);
            $('#kd_sub_komponen_sel').val(data.kd_sub_komponen);
            $('#nama_sub_komponen_sel').val(data.nama_sub_komponen);
            $('[name="kd_sub_komponen_sel"]').val(data.kd_sub_komponen);
            $('[name="nama_sub_komponen_sel"]').val(data.nama_sub_komponen);
            $('[name="kd_kegiatan_sel"]').val(data.kd_kegiatan);
            console.log(data);
            console.log("-----");
            var jmlmath = Math.round(data.jumlah);
            var rjml = jmlmath + " ";
            var hsmath = Math.round(data.hargasat);
            var rhs = hsmath + " ";
            $('[name="volume"]').val(data.volume);
            $('[name="satuan"]').val(data.satuan);
            $('[name="hargasat"]').val(parseInt(rhs));
            $('[name="totalpagu"]').val(parseInt(rjml));
            $('[name="treatment"]').val(data.treatment);
            $('[name="xid_ruas"]').val(data.no_ruas);
            $('[name="latitude"]').val(data.latitude);
            $('[name="longitude"]').val(data.longitude);
            $('[name="rm"]').val(data.rpm);
            updateCombobox('kd_kegiatan_sel', 5, data.kd_kegiatan);
            if (role == 7) {
            $('#jns_giat').val('NF');
            $('.rujuk').show();
            $('.divNonFisik').show();
            $('#detail').prop('disabled', false);
            $('.divRuas').hide();
            $("#xid_ruas").prop('disabled', true);
            $("#sta_awal").prop('disabled', true);
            $("#sta_akhir").prop('disabled', true);
            $("#treatment").prop('disabled', true);
            $("#xid_jembatan").prop('disabled', true);
            $("#longitude").prop('disabled', true);
            $("#latitude").prop('disabled', true);
            } else if (role == 3) {
            $('#jns_giat').val('F');
            $('.rujuk').show();
            $('.divNonFisik').hide();
            $('#detail').prop('disabled', true);
            $('.divRuas').show();
            $("#xid_ruas").prop('disabled', false);
            $("#sta_awal").prop('disabled', false);
            $("#sta_akhir").prop('disabled', false);
            $("#treatment").prop('disabled', false);
            $("#xid_jembatan").prop('disabled', false);
            $("#longitude").prop('disabled', false);
            $("#latitude").prop('disabled', false);
            }

            if (data.kd_kegiatan == '2409')
            {
            $('#jns_giat').val('F');
            $('.rujuk').show();
            $('.divNonFisik').hide();
            $('#detail').prop('disabled', true);
            $('.divRuas').show();
            $("#xid_ruas").prop('disabled', false);
            $("#sta_awal").prop('disabled', false);
            $("#sta_akhir").prop('disabled', false);
            $("#treatment").prop('disabled', false);
            $("#xid_jembatan").prop('disabled', false);
            $("#longitude").prop('disabled', false);
            $("#latitude").prop('disabled', false);
            } else {
            $('#jns_giat').val('NF');
            $('.rujuk').show();
            $('.divNonFisik').show();
            $('#detail').prop('disabled', false);
            $('.divRuas').hide();
            $("#xid_ruas").prop('disabled', true);
            $("#sta_awal").prop('disabled', true);
            $("#sta_akhir").prop('disabled', true);
            $("#treatment").prop('disabled', true);
            $("#xid_jembatan").prop('disabled', true);
            $("#longitude").prop('disabled', true);
            $("#latitude").prop('disabled', true);
            }

            var j = function () {
            var defer = $.Deferred();
            //updateCombobox('kdgbkpk', 36, data.kdgbkpk);
            refreshComboboxOutput('kd_output_sel', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);
            //refreshComboboxOutput('kdakun', 37, 'kdgbkpk', data.kdgbkpk, data.kdakun);
            updateComboboxAndSelected('ykdgbkpk', 36, data.kdgbkpk);
            refreshComboboxSearch('ykdakun', 37, 'kdgbkpk', data.kdgbkpk, data.kdakun);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };
            var a = function () {
            var defer = $.Deferred();
            var valSelect = data.kd_kegiatan + "::" + data.kd_output;
            refreshCombobox4('kd_sub_output_sel', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };
            var b = function () {
            var defer = $.Deferred();
            var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
            refreshCombobox4('kd_komponen_sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };
            var c = function () {
            var defer = $.Deferred();
            //console.log('a() called');
            var tlist_detail = $('#tlist_detail').DataTable();
            tlist_detail.ajax.reload();
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };
            var g = function () {
            var defer = $.Deferred();
            refreshComboboxJBT('xid_jembatan', 32, 'linkid', data.no_ruas, data.id_jembatan);
            refreshComboboxSTA('sta_awal_sel', 35, 'kode_ruas', data.no_ruas, data.sta_awal);
            refreshComboboxSTA('sta_akhir_sel', 35, 'kode_ruas', data.no_ruas, data.sta_akhir);

            $(".decformat").val(rjml.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ","));
            $(".decformat2").val(rhs.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ","));
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };

            var pbar = function () {
            var defer = $.Deferred();
            $(".overlay").show();
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };

            refreshComboboxOutput('kabkot', 49, 'kd_prov_irmsv3', da.kd_prov_irmsv3, data.kdkabkota);
            $.when(
                    pbar().then(j).then(a).then(b).then(g).then(c)
                    ).then(function () {
            $(".overlay").hide(); // All have been resolved (or rejected), do your thing

            });
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
            alert('Error get data from ajax');
            }
    });
    }
    function hitvol(){
    var has=parseFloat($("#xxvolume").val())*parseInt($("#xxxhargasat").val());
//    alert(has)
    $("#xxxjumlah").val(has.toFixed(1))
//    alert("asd")
    }





     function dtEditRowPaket(id) {
        $('#frm-edit-paket')[0].reset();
        $('#zpanelOutput').hide();
        $('#zpanelSubOutput').hide();
        $('#zpanelKomponen').hide();
        var id_paket = id;
               if(role==3)
        {
            $("#zfsk").show('');
            $("#znon").show('');
        }
        else if(role==7)
        {
            $("#znon").show('');
        }

        //alert(id_paket);
        var data_selected = xhrdata.data.filter(x => x[0] == id_paket)[0];
        console.log(data_selected);
        //alert(data_selected[2]);
        refreshComboboxOutput('zthang', 21, 'thang', data_selected[1], data_selected[1]);

        updateComboboxAndSelected('zkd_isu', 3, data_selected[20]);

        // updateComboboxAndSelected('zwps_kode', 3,data_selected[27]);

        //refreshComboboxOutput('zkd_kegiatan', 5, 'kdgiat', data_selected[10], data_selected[10]);
        updateCombobox('zkd_kegiatan', 5, data_selected[13]);

        setTimeout(set_kegiatan, 8000);//set kegiatan berdasarkan kode

        $("#zkd_sub_komponen").val(data_selected[18]);

        $("#znama_sub_komponen").val($.trim(data_selected[19].split("-")[1]));
        $("#zid_paket").val(id);


        var id_rprov = $("#id_rprov").val();
      refreshComboboxOutput('zprov', 64, 'kdsatker', kd_satker,prrr);

        refreshComboboxOutput('zkd_output', 30, 'kdgiat', data_selected[13], data_selected[14]);

        //perlu passing parameter div_id,indexs lookup,fields lookup,value lookup,data selected hasil
        refreshComboboxOutput4('zkd_sub_output', 44, 'kdgiat::kdoutput', data_selected[13]+"::"+data_selected[14], data_selected[15]);

        refreshComboboxOutput4('zkd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', data_selected[13]+"::"+data_selected[14]+"::"+data_selected[15], data_selected[17]);
        //
        updateComboboxAndSelected('zjnskontrak', 23, data_selected[21]);

       // updateComboboxAndSelected('zkdkppn', 9, data_selected[29]);


        //updateComboboxAndSelected('zkdkppn', 46, data_selected[22]);
        //refreshComboboxOutput('zkdkppn',46,'kdsatker', kd_satker, data_selected[22]);
        //new code
        //refreshComboboxKPPN('zkdkppn', 46, 'kdsatker', kd_satker, data_selected[22]);
        //setTimeout(set_kppn_jakarta, 3000);

       // updateComboboxAndSelected('zid_ppk', 11, data_selected[30]);

        //refreshComboboxOutput('zid_ppk',47,'kdsatker', kd_satker, data_selected[23]);
        //alert(data_selected[30]);

     //   updateComboboxAndSelected('zwps_kode', 57, data_selected[32]);

        //updateComboboxAndSelected('zkdgbkpk', 36, data_selected[37]);
        //refreshComboboxOutput('zkdgbkpk', 36, 'kdgbkpk',data_selected[20],data_selected[37]);

        //updateComboboxAndSelected('zid_ppk',11,data_selected[30]);

        //refreshComboboxOutput('ykdgbkpk', 36, 'kdgbkpk',data_selected[20],data_selected[20]);
        //initCombobox("zkdgbkpk",36);

        //initCombobox("zkdakun", 37);

        $("#zrc_ded_status").val(data_selected[26])
        $("#zrc_fs_status").val(data_selected[27])
        $("#zrc_lahan_status").val(data_selected[28])
        $("#zrc_doklin_status").val(data_selected[29])
        $("#zkak").val(data_selected[35])
        $("#zrab").val(data_selected[34])
        $("#flow1").val(data_selected[36])
        $("#flow2").val(data_selected[37])
        $("#flow3").val(data_selected[38])

//         $("#zrc_ded_status").append("<option selected value=" + data_selected[26] + ">" + data_selected[26] + "</option>");
//        $("#zrc_fs_status").append("<option selected " + data_selected[27] + ">" + data_selected[27] + "</option>");
//        $("#zrc_lahan_status").append("<option selected " + data_selected[28] + ">" + data_selected[28] + "</option>");
//        $("#zrc_doklin_status").append("<option selected " + data_selected[29] + ">" + data_selected[29] + "</option>");

      //  updateComboboxAndSelected('zkws_kode', 57, data_selected[31]);
        // alert( $('#yjns_giat option:eq(1)').val());
        //alert($("#ykd_kegiatan_sel").val());

        setTimeout(set_kegiatan, 2000);//
        var aa = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            var thang = $('#zthang').val();
            var kdgiat = $('#zkd_kegiatan').val();
            var kdoutput = $('#zkd_output').val();
            get_output(kdgiat, kdoutput, thang, 'z');
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        var ab = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            var thang = $('#zthang').val();
            var kdgiat = $('#zkd_kegiatan').val();
            var kdoutput = $('#zkd_output').val();
            var kdsoutput = $('#zkd_sub_output').val();
            get_soutput(kdgiat, kdoutput, kdsoutput, thang, 'z');
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        var ac = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            var thang = $('#zthang').val();
            var kdgiat = $('#zkd_kegiatan').val();
            var kdoutput = $('#zkd_output').val();
            var kdsoutput = $('#zkd_sub_output').val();
            var kdkmpnen = $('#zkd_komponen').val();
            get_kmpnen(kdgiat, kdoutput, kdsoutput, kdkmpnen, thang, 'z');
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        aa().then(ab).then(ac);


        $("#modal-edit-paket").modal("show");

        }
        function set_kppn_jakarta(){
        var zhtml_option = [
                "<option value=" + "555" + " >",
                "VI KHUSUS JAKARTA",
                "</option>"
        ].join("\n");

        $("#zkdkppn").append(zhtml_option);

    }

    function dtEditRowPaket2(id) {

    $('.form-group').removeClass('has-error'); // clear error class
    $('.help-block').empty(); // c
    $('#modeform').val('edit_paket');
    $('#modal-tambah').modal('show'); // show bootstrap modal when complete loaded
    $('.tbhPaketDetail').text('Edit Paket'); // Set title to Bootstrap modal title
    $('#blockItmPaket').hide();

    //Ajax Load data from ajax
    $.ajax({
    url: "<?php echo base_url(); ?>pagu_tahapan/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {
            var x = $("#wps_kode").val();
            $.ajax({
            url: "<?php echo base_url('pagu_tahapan/get_kws/') ?>" + data.wps_kode,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (dat) {
                    //console.log("--ajax data--");
                    console.log(data);

                            x = dat;
                    lookkws(x);

                    },
                    failure: function (errMsg) {
                    alert(errMsg);
                    }
            });
            $.ajax({
            url: "<?php echo base_url('pagu_tahapan/get_sub/') ?>" + data.kws_kode,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                    console.log(data);
                            x = data;
                    looksub(x);

                    },
                    failure: function (errMsg) {
                    alert(errMsg);
                    }
            });
            console.log(data);
            console.log("----------");

            $('[name="id_paket"]').val(data.id_paket);
            $('[name="thang"]').val(data.thang);
            updateCombobox('kd_isu', 3, data.kd_isu);
            updateCombobox('kd_kegiatan', 5, data.kd_kegiatan);

            var j = function () {
            var defer = $.Deferred();
            refreshComboboxOutput('kd_output', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);

            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };
            var a = function () {
            var defer = $.Deferred();
            var valSelect = data.kd_kegiatan + "::" + data.kd_output;
            refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);

            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };
            var b = function () {
            var defer = $.Deferred();
            var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
            refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);

            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };

            $('[name="jnskontrak"]').val(data.jnskontrak);
            $('[name="id_ppk"]').val(data.id_ppk);
            $('[name="kdkppn"]').val(data.kdkppn);
            $('[name="kd_isu"]').val(data.kd_isu);
            $('[name="wps_kode"]').val(data.wps_kode);
            $('[name="kws_kode"]').val(data.kws_kode);
            $('[name="subkw"]').val(data.subkawasan_nama);
            $('[name="rc_ded_status"]').val(data.rc_ded_status);
            $('[name="rc_fs_status"]').val(data.rc_fs_status);
            $('[name="rc_lahan_status"]').val(data.rc_lahan_status);
            $('[name="rc_doklin_status"]').val(data.rc_doklin_status);

            var pbar = function () {
            var defer = $.Deferred();

            $(".overlay").show();
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
            };
            $.when(
                    pbar().then(j).then(a).then(b)
                    ).then(function () {
            $(".overlay").hide(); // All have been resolved (or rejected), do your thing
            });
            $('[name="kd_sub_komponen"]').val(data.kd_sub_komponen);
            $('[name="nama_sub_komponen"]').val(data.nama_sub_komponen);

            },
            error: function (jqXHR, textStatus, errorThrown)
            {
            alert('Error get data from ajax');
            }
    });
    }

    function dtEditRow(id) {

    $('#frm-detail')[0].reset();
    $('.form-group').removeClass('has-error'); // clear error class
    $('.help-block').empty(); // c


    //$('#modeform').val('edit');
    $('#modal-tambah').modal('show'); // show bootstrap modal when complete loaded
    $('.tbhPaketDetail').text('Tambah Detail Paket'); // Set title to Bootstrap modal title
    $('#blockItmPaket').show();

    //Ajax Load data from ajax
    $.ajax({
    url: "<?php echo base_url(); ?>pagu_tahapan/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {

            $('[name="id_paket"]').val(data.id_paket);
            $('[name="thang"]').val(data.thang);

            var j = function () {
            var defer = $.Deferred();

            initCombobox('kd_kegiatan', 5);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 2000);
            return defer;
            };
            var a = function () {
            var defer = $.Deferred();

            $('[name="kd_kegiatan"]').val(data.kd_kegiatan);
            refreshCombobox('kd_output', 30, 'kdgiat', data.kd_kegiatan);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 2000);
            return defer;
            };
            var b = function () {
            var defer = $.Deferred();

            $('[name="kd_output"]').val(data.kd_output);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 2000);
            return defer;
            };
            var c = function () {
            var defer = $.Deferred();

            var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
            refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 2000);
            return defer;
            };
            var d = function () {
            var defer = $.Deferred();
            //console.log('a() called');
            $('[name="kd_komponen"]').val(data.kd_komponen);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 2000);
            return defer;
            };
            var e = function () {
            var defer = $.Deferred();

            var tlist_detail = $('#tlist_detail').DataTable();
            tlist_detail.ajax.reload();
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);
            return defer;
            };
            var f = function () {
            var defer = $.Deferred();
            var valSelect = data.kd_kegiatan + "::" + data.kd_output;
            refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);
            return defer;
            };
            var g = function () {
            var defer = $.Deferred();

            $('[name="kd_sub_output"]').val(data.kd_sub_output);
            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);
            return defer;
            };
            var i = function () {
            var defer = $.Deferred();
            var sat = $('#satuan').val();
            if (sat == 'km' || sat == 'm')
            {
            $('.divNonFisik').hide();
            $('#detail').prop('disabled', true);
            $('.divRuas').show();
            $("#xid_ruas").prop('disabled', false);
            $("#sta_awal").prop('disabled', false);
            $("#sta_akhir").prop('disabled', false);
            $("#treatment").prop('disabled', false);
            $("#xid_jembatan").prop('disabled', false);
            $("#longitude").prop('disabled', false);
            $("#latitude").prop('disabled', false);
            } else
            {
            $('.divNonFisik').show();
            $('#detail').prop('disabled', false);
            $('.divRuas').hide();
            $("#xid_ruas").prop('disabled', true);
            $("#sta_awal").prop('disabled', true);
            $("#sta_akhir").prop('disabled', true);
            $("#treatment").prop('disabled', true);
            $("#xid_jembatan").prop('disabled', true);
            $("#longitude").prop('disabled', true);
            $("#latitude").prop('disabled', true);
            }

            setTimeout(function () {
            defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);
            return defer;
            };
            j().then(a).then(b).then(f).then(g).then(c).then(d).then(e).then(i);
            $('[name="kd_sub_komponen"]').val(data.kd_sub_komponen);
            $('[name="nama_sub_komponen"]').val(data.nama_sub_komponen);
            $('#archive-preview').show(); // show photo preview modal

            if (data.archive)
            {

            $('#label-archive').text('Ubah File'); // label photo upload
            $('#archive-preview div').html('<input type="checkbox" id="remove_archive" name="remove_archive" value="' + data.archive + '"/>&nbsp;Hapus file saat save<br><br>'); // remove photo

            } else
            {
            $('#label-archive').text('Unggah File'); // label photo upload
            $('#archive-preview div').text('(File Tidak Tersedia)');
            }

            },
            error: function (jqXHR, textStatus, errorThrown)
            {
            alert('Error get data from ajax');
            }
    });
    }

    function dtHistory($db, id) {
    $.ajax({
    url: "<?php echo base_url(); ?>pagu_tahapan/history/" + id + "/" + $db,
            type: "GET",
            dataType: "html",
            success: function (data)
            {
            $('#content-history').html(data);
            $('#modal-history').modal('show');
            $('.block-title').text('History Paket'); // Set title to Bootstrap modal title
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
            alert('Error get data from ajax');
            }

    });
    }

    function dtTambahRowPaket() {
    $('#frm-tambah')[0].reset();
    $('#xmaxRuas').text('');
    $('#xpanelOutput').hide();
    $('#xpanelSubOutput').hide();
    $('#xpanelKomponen').hide();
    //$('#frm-tambah').trigger("reset");
//        alert($("#xid_ruas").val())
//        alert($("#sta_awal").val())
//        alert($("#sta_akhir").val())
//
        $("#yrm").val('')
        $("#wrm").val('')
        if(role==3)
        {
            $("#xfsk").show('');
            $("#xnon").show('');
        }
        else if(role==7)
        {
            $("#xnon").show('');
        }

        var tab=$('#tirmsv3_wp2').DataTable();
        tab.destroy()
        irms('2');


    refreshComboboxOutput('prov', 64, 'kdsatker', kd_satker,prrr);
    $("#prov").val(prrr)
    var kd_kegiatan = $("#kd_kegiatan").val();
    initCombobox('thang', 28);
    initCombobox('kd_isu', 3);
    bind_wps_by_province();
    initCombobox('kd_kegiatan', 5);
     initCombobox('xsumber', 65);
     //new code
     // initCombobox('xregister', 73);
     // initCombobox('xregisterpdp', 73);
     initCombobox('xkd_blokir', 76);

    var html_jns_kegiatan = ["<option value='#'>",
            "Pilih",
            "</option>",
            "<option value='F'>",
            "Fisik",
            "</option>",
            "<option value='NF'>",
            "Non Fisik",
            "</option>"
    ].join("\n");
    $("#jns_giat").empty();
    $("#jns_giat").append(html_jns_kegiatan);
//          $("#kd_output").prop("disabled",true);
//          $("#kd_sub_output").prop("disabled",true);
//           $("#kd_kegiatan").prop("disabled",false);
//          $("#kd_komponen").prop("disabled",false);
    $("#kws_kode").prop("disabled",true);
    $("#subkw").prop("disabled",true);
        $("#kdakun").prop("disabled",true);
        $("#sta_awal").prop("disabled",true);
        $("#sta_akhir").prop("disabled",true);
//    $("#kd_sub_output").empty();
//    $("#kd_komponen").empty();
    initCombobox('jnskontrak', 23);
    refreshCombobox('id_ppk', 47, 'kdsatker', kd_satker);
    refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
//    $(".multiple-form-group").not(':first').remove();
//    $(".input-group-btn").children(".btn-remove").text("+");
//    $(".input-group-btn").children(".btn-remove").addClass('btn-success btn-add');
//    $(".input-group-btn").children(".btn-add").removeClass("btn-remove");
//    $(".input-group-btn").children(".btn-add").removeClass("btn-danger");
    $("#xrm").val("");
    //mengosongkan nilai tagging sebelumnya
    obj_rj_rams = {};
    obj_rj_irms = {};
    obj_rj_renstra = {};
    obj_rj_eprogram = {};
    obj_rj_dpr = {};
    obj_rj_pemda = {};
    obj_rj_sipro = {};
    //btn btn-danger btn-remove
    var html_option = [
            "<option value=" + "" + " >",
            "--Pilih--",
            "</option>",
            "<option value=" + "siap" + " >",
            "Siap",
            "</option>",
            "<option value=" + "tidak_siap" + " >",
            "Tidak Siap",
            "</option>" ,
        "<option value=" + "tidak_perlu" + " >",
            "Tidak Perlu",
            "</option>"
    ].join("\n");
    $("#rc_ded_status").empty();
    $("#rc_ded_status").append(html_option);
    $("#rc_fs_status").empty();
    $("#rc_fs_status").append(html_option);
    $("#rc_lahan_status").empty();
    $("#rc_lahan_status").append(html_option);
    $("#rc_doklin_status").empty();
    $("#rc_doklin_status").append(html_option);
        $("#rab").empty();
    $("#rab").append(html_option);
        $("#kak").empty();
    $("#kak").append(html_option);
    initCombobox('kdgbkpk', 36);
//    $("#kdakun").empty();
    var id_rprov = $("#id_rprov").val();

    refreshComboboxOutput('kabkot', 71, 'kd_prov_rkakl', prrr);
    //refreshCombobox('kabkot', 71, 'kd_prov', id_rprov);

    $("#modal-tambah").find('input:text').val('');
        // initComboboxRuasSatker('xid_ruas', kd_satker);
//    $("#sta_awal").empty();
//    $("#sta_akhir").empty();

    if (role == 7) {
    $("#fskk").css( "display","none" );
    $("#fsk").css( "display","none" );
    $('#jns_giat').val('NF');
    $('.rujuk').show();
    $('.divNonFisik').show();
    $('#detail').prop('disabled', false);
    $('.divRuas').hide();
    $("#xid_ruas").prop('disabled', true);
    $("#sta_awal").prop('disabled', true);
    $("#sta_akhir").prop('disabled', true);
    $("#treatment").prop('disabled', true);
    $("#xid_jembatan").prop('disabled', true);
    $("#longitude").prop('disabled', true);
    $("#latitude").prop('disabled', true);
    } else if (role == 3) {
            $("#fskk").css( "display","block" );
            $("#fsk").css( "display","block" );
        $("#xid_ruas").prop('disabled', false);
    $('#jns_giat').val('F');
    $('.rujuk').show();
    $('.divNonFisik').css("display", "none");
    $('#detail').prop('disabled', true);
    $('.divRuas').css("display", "");
    $("#xid_ruas").prop('disabled', false);
//    $("#sta_awal").prop('disabled', false);
//    $("#sta_akhir").prop('disabled', false);
    $("#treatment").prop('disabled', false);
    $("#xid_jembatan").prop('disabled', false);
    $("#longitude").prop('disabled', false);
    $("#latitude").prop('disabled', false);
    }

    $(".decformat").change(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
    });
    $(".decformat2").select(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
    });

    initCombobox('kdgbkpk', 36);
    //modal tambah
//    multipleSelects('kdsdana', 38);
    //modal edit
    multipleSelect('ykdsdana', 38);
    //modal edit
    initCombobox('ykdgbkpk', 36);
    // initCombobox('treatment', 42);

    $('.div-tags').tagsinput({

    tagClass: function (item) {
    console.log("struktur data item");
    console.log(item);
    switch (item.rujukan) {
    case 'DPR':
            case 'PEMDA':
            return 'label label-default';
    case 'SIPRO':
            return 'label label-primary';
    case 'IRMS':
            return 'label label-success';
    case 'RAMS':
            return 'label label-info';
    case 'EPROG':
            return 'label label-warning';
    case 'RENSTRA':
            return 'label label-danger';
    }
    },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
    });

    $('#modeform').val('tambah_detail');
    $('.form-group').removeClass('has-error'); // clear error class
    $('.help-block').empty(); // clear error string
    $('#modal-tambah').modal('show');
    $('.tbhItem').text('Tambah Paket Pagu');
    $("#urutans").empty();
    $("#urutans").val("2");
    }

    // modal detail
    function set_kegiatan() {
    if ($("#ykd_kegiatan_sel").val() == "2409") {
    $('#yjns_giat option:eq(1)').attr('selected', 'selected');
    }
    if ($("#zkd_kegiatan").val() == "2409") {
    $('#zjns_giat option:eq(1)').attr('selected', 'selected');
    }

    if ($("#wkd_kegiatan").val() == "2409") {
    $('#wjns_giat option:eq(1)').attr('selected', 'selected');
    }
    }

    function get_akun_by_kdgbkpk(kd_jns_belanja) {
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_akun_by_kdgbkpk') ?>" + "/" + kd_jns_belanja;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function bind_jenis_akun(kd_jns_belanja){
    var objakun = get_akun_by_kdgbkpk(kd_jns_belanja);
    for (var i = 0; i <= objakun.length - 1; i++){
    $("#ykdakun").append("<option value=" + objakun[i].kdakun + ">" + objakun[i].nmakun + "</option>");
    }
    }

    function dtTambahRowDetail(id, isFisik) {
        $('#frm-detail')[0].reset();
        $('#ymaxRuas').text('');
        //$('#frm-detail').trigger("reset");
        $("#id_ruas").empty()
        $("#xid_ruas").empty()
        $("#wid_ruas").empty()
        $("#xrm").val('')
        $("#wrm").val('')
        if(role==3)
        {
            $("#yfsk").show('');
            $("#ynon").show('');
        }
        else if(role==7)
        {
            $("#ynon").show('');
        }
         initCombobox('ysumber', 65);
         //new code
        //  initCombobox('yregister', 73);
        //  initCombobox('yregisterpdp', 73);
        initCombobox('ykd_blokir', 76);
         //setTimeout(function(){ ChangeSumberList('y'); }, 2000);

//        alert($("#id_ruas").val())
//        alert($("#xid_ruas").val())
//        alert($("#wid_ruas").val())
//         $("#wlatitude").empty()
            var tab=$('#tirmsv3_wp3').DataTable();
            tab.destroy()
        irms('3')
    obj_rj_rams = {};
    obj_rj_irms = {};
    obj_rj_renstra = {};
    obj_rj_eprogram = {};
    obj_rj_dpr = {};
    obj_rj_sipro = {};
    obj_rj_pemda = {};
//    $(".ctk-tb-detail .input-group").not(':first').remove();
//    $(".ctk-tb-detail .btn-remove").removeClass("btn-danger").addClass("btn-success");
//    $(".ctk-tb-detail .btn-success").removeClass("btn-remove").addClass("btn-add");
//    $(".btn-add").empty();
//    $(".btn-add").append("+");
//    $("#yrm").val("");
    var id_paket = id;
    var data_selected = xhrdata.data.filter(x => x[0] == id_paket)[0];
    console.log(data_selected);
    refreshComboboxOutput('ythang_sel', 21, 'thang', data_selected[1], data_selected[1]);
    refreshComboboxOutput('ykd_kegiatan_sel', 5, 'kdgiat', data_selected[9], data_selected[9]);
    refreshComboboxOutput('ykd_output_sel', 30, 'kdgiat', data_selected[9], data_selected[14]);
    refreshComboboxOutput('ykd_sub_output_sel', 44, 'kdoutput', data_selected[14], data_selected[15]);
    refreshComboboxOutput('ykd_komponen_sel', 18, 'kdsoutput', data_selected[15], data_selected[17]);
    //refreshComboboxKPPN('ykdkppn', 46, 'kdsatker', kd_satker, data_selected[22]);
    refreshComboboxKPPN('ykdkppn', 46, 'kdsatker', kd_satker);
    //refreshComboboxOutput('ykdkppn', 46, 'kdsatker', kd_satker, data_selected[22]);

//    var kd_jns_belanja = data_selected[37];
//    bind_jenis_akun(kd_jns_belanja);
    //updateComboboxAndSelected('ykdgbkpk', 36, data_selected[37]);
    updateComboboxAndSelected('yjnskontrak', 23, data_selected[21]);
    //updateComboboxAndSelected('yid_ppk', 47, data_selected[23]);
    refreshCombobox('yid_ppk', 47, 'kdsatker', kd_satker);
    $('#yrc_ded_status').val(data_selected[26]);
    $('#yrc_fs_status').val(data_selected[27]);
    $('#yrc_lahan_status').val(data_selected[28]);
    $('#yrc_doklin_status').val(data_selected[29]);
    $('#ykak').val(data_selected[34]);
    $('#yrab').val(data_selected[35]);
    setTimeout(set_kegiatan, 8000); //

    var kdgi= data_selected[13];
    var kdou=data_selected[14];
   if(kdgi=='2409' && (kdou=='014' || kdou=='015' || kdou=='016' || kdou=='017' || kdou=='018' || kdou=='951' || kdou=='970' || kdou=='994' || kdou=='999'))
               {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                   $(".uraians").show();
                   //$("#fsk").hide();
                  // alert("ilang");
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").show();
                    $("input[name='yradioVolume']").val([3]);
                    setTimeout(function(){handleRadioVolume('y'); }, 3000);
               }
               else if(kdgi=='2409' && (kdou=='001' || kdou=='002' || kdou=='003' || kdou=='004' || kdou=='005' || kdou=='006' || kdou=='007' || kdou=='008' || kdou=='009' || kdou=='010' || kdou=='011' || kdou=='012' || kdou=='013'))
                {
                    if(kdou=='004' || kdou=='005' || kdou=='006' || kdou=='010')
                    {
                     $(".ruasjalan").show();
                     $(".ruasjalan").removeClass('col-md-6');
                     $(".ruasjalan").addClass('col-md-12');
                    $(".staw").hide();
                    $("#sta_awal_sel").empty();
                    $("#sta_akhir_sel").empty();
                    $(".jembatans").show();
                    $("#l5").text("Longitude (X1)")
                    $("#l6").text("Latitude  (Y1)")
                    $("#ylongitudes").empty()
                    $("#ylatitudes").empty()
                    $("#ylongitude").empty()
                    $("#ylatitude").empty()
                    $(".longs").show();
                    $(".longsa").hide();
                    //new code
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").show();
                    $(".id_radioHonor").show();
                    $("input[name='yradioVolume']").val([2]);
                    setTimeout(function(){handleRadioVolume('y'); }, 3000);
                    }else
                    {
                    $(".ruasjalan").show();
                     $(".ruasjalan").removeClass('col-md-12');
                     $(".ruasjalan").addClass('col-md-6');
                    $(".staw").show();
                    $(".jembatans").hide();
                    $("#yid_jembatan").empty()
                    $("#l5").text("Longitude (X1)")
                    $("#l6").text("Latitude (Y1)")
                     $("#l7").text("Longitude (X2)")
                    $("#l8").text("Latitude (Y2)")
                    $(".longs").show();
                    $(".longsa").show();
                    //new code
                    $(".id_radioRuas").show();
                    $(".id_radioJembatan").show();
                    $(".id_radioHonor").show();
                    $("input[name='yradioVolume']").val([1]);
                    setTimeout(function(){handleRadioVolume('y'); }, 3000);
                    }

                }
                else if(kdgi !='2409')
                {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").show();
                    $("input[name='yradioVolume']").val([3]);
                    setTimeout(function(){handleRadioVolume('y'); }, 3000);
                }

//    $("#ykdgbkpk").prop("disabled",true)
     $("#ykdakun").prop("disabled",true)
       $("#sta_awal_sel").prop("disabled",true)
       $("#sta_akhir_sel").prop("disabled",true)
     $("#yid_jembatan").prop("disabled",true)
         $("#ykd_output_sel").prop("disabled",true)
         $("#ykd_sub_output_sel").prop("disabled",true)
              $("#ykd_komponen_sel").prop("disabled",true)
               $("#ykd_sub_komponen_sel").prop("disabled",true)

              $("#ynama_sub_komponen_sel").prop("disabled",true)




    $("#ykd_sub_komponen_sel").val(data_selected[18]);
    $("#ynama_sub_komponen_sel").val(data_selected[19]);
    $("#yid_paket").val(id_paket);
    var id_rprov = $("#id_rprov").val();
        refreshComboboxOutput('yprov', 64, 'kdsatker', kd_satker,prrr);
   // refreshComboboxOutput('yprov', 60, 'kd_prov', id_rprov, id_rprov);
        //initCombobox('ykdgbkpk', 36);
    //refreshComboboxOutput('ykabkot', 71, 'KD_SATKER', kd_satker);
    refreshComboboxOutput('ykabkot', 71, 'kd_prov_rkakl', prrr);
    //refreshCombobox('ykabkot', 61, 'kd_prov', id_rprov);
    var yrj_rams = "";
    var yrj_irms = "";
    var yrj_renstra = "";
    var yrj_eprogram = "";
    var yrj_dpr = "";

    $(".decformat2").keyup(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
    var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ",").replace(/\D/g, "");
    $('#hargasat').val(decform);
    var hargasat = $('#hargasat').val();
    var volume = $('#volume').val();
    var hasil = volume * hargasat;
    $("#totalpagu").val(hasil);
    $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));
    $('#rm').val(hasil);
    });
    $(".decformat").change(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
    });
    $(".decformat2").select(function (event) {
    if (event.which >= 37 && event.which <= 40)
            return;
    // format number
    $(this).val(function (index, value) {
    return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      });
    });

    //initCombobox('kdgbkpk', 36);

//    initCombobox('treatment', 42);
    multipleSelect('ykdsdana', 38);
  //  initComboboxRuasSatker('id_ruas',kd_satker);
    $("#sta_awal_sel").empty();
    $("#sta_akhir_sel").empty();
    $('#modeform').val('tambah_detail');
    $('.form-group').removeClass('has-error'); // clear error class
    $('.help-block').empty(); // clear error string
    $('#modal-detail').modal('show');
    $('.tbhItem').text('Tambah Detail Paket');
    $('#thang_sel').prop('disabled', true);
    $('#kd_kegiatan_sel').prop('disabled', true);
    $('#kd_output_sel').prop('disabled', false);
    $('#kd_sub_output_sel').prop('disabled', false);
    $('#kd_komponen_sel').prop('disabled', false);
    $('#kd_sub_komponen_sel').prop('disabled', true);
    $('#nama_sub_komponen_sel').prop('disabled', true);
    $('#id_ppk_sel').prop('disabled', false);
    $('#kdkppn_sel').prop('disabled', false);
    if (isFisik == '2409')
    {

    $('#jns_giat').on('change', function () {
    var jnsg = $('#jns_giat').val();

    if (jnsg == 'F')
    {
    $('.rujuk').show();
    $('.divNonFisik').hide();
    $('#detail').prop('disabled', true);
    $('.divRuas').show();
    $("#id_ruas").prop('disabled', false);
    $("#sta_awal").prop('disabled', false);
    $("#sta_akhir").prop('disabled', false);
    $("#treatment").prop('disabled', false);
    $("#xid_jembatan").prop('disabled', false);
    $("#longitude").prop('disabled', false);
    $("#latitude").prop('disabled', false);
    } else {
    $('.rujuk').hide();
    $('.divNonFisik').show();
    $('#detail').prop('disabled', false);
    $('.divRuas').hide();
    $("#id_ruas").prop('disabled', true);
    $("#sta_awal").prop('disabled', true);
    $("#sta_akhir").prop('disabled', true);
    $("#treatment").prop('disabled', true);
    $("#xid_jembatan").prop('disabled', true);
    $("#longitude").prop('disabled', true);
    $("#latitude").prop('disabled', true);
    }

    });
    $('#jns_giat').val('F');
    $('.rujuk').show();
    $('.divNonFisik').hide();
    $('#detail').prop('disabled', true);
    $('.divRuas').show();
    $("#id_ruas").prop('disabled', false);
    $("#sta_awal").prop('disabled', false);
    $("#sta_akhir").prop('disabled', false);
    $("#treatment").prop('disabled', false);
    $("#xid_jembatan").prop('disabled', false);
    $("#longitude").prop('disabled', false);
    $("#latitude").prop('disabled', false);
    $('.div-tags2').tagsinput({

    tagClass: function (item) {

    switch (item.rujukan) {
    case 'DPR':
            case 'PEMDA':
            return 'label label-default';
    case 'SIPRO':
            return 'label label-primary';
    case 'IRMS':
            return 'label label-success';
    case 'RAMS':
            return 'label label-info';
    case 'EPROG':
            return 'label label-warning';
    case 'RENSTRA':
            return 'label label-danger';
    }
    },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
    });
    } else {
    $('#jns_giat').val('NF');
    $('#jns_giat').hide();
    $('.rujuk').show();
    $('.divNonFisik').show();
    $('#detail').prop('disabled', false);
    $('.divRuas').hide();
    $("#id_ruas").prop('disabled', true);
    $("#sta_awal").prop('disabled', true);
    $("#sta_akhir").prop('disabled', true);
    $("#treatment").prop('disabled', true);
    $("#xid_jembatan").prop('disabled', true);
    $("#longitude").prop('disabled', true);
    $("#latitude").prop('disabled', true);
    }
    $("#prov").empty();

    document.getElementById("ythang_sel").disabled = true;
    document.getElementById("ykd_kegiatan_sel").disabled = true;
    //document.getElementById("yjns_giat").disabled = true;
    document.getElementById("ykd_output_sel").disabled = true;
    document.getElementById("ykd_komponen_sel").disabled = true;
    document.getElementById("ykd_sub_komponen_sel").disabled = true;
    document.getElementById("ynama_sub_komponen_sel").disabled = true;
    document.getElementById("yprov").disabled = true;
    document.getElementById("ykd_sub_output_sel").disabled = true;

    var htmldropdownsda = $("#htmldropdownsdana").val();
    if ($(".ctk-tb-detail").children().length == 1){
    var html_single_sda = ['<div class="form-group multiple-form-group input-group input-group-edit-detail">',
            '<div class="input-group-btn input-group_select">',
            '<button data-toggle="dropdown" type="button" class="btn btn-' + "default" + ' dropdown-toggle" data-toggle="dropdown">',
            '<span class="concept">' + "RM" + '</span> <span class="caret"></span>',
            '</button>',
            '<ul class="dropdown-menu" role="menu" id="wkdsdana">',
            htmldropdownsda,
            '</ul>',
            '</div>',
            '<input id=y' + 'rm' + ' name="yrm" class="form-control valuesdanas number" type="text">',
            '<span class="input-group-btn">',
            '<button type="button" class="btn btn-success btn-' + 'add' + '">' + "+" + '</button>',
            '</span>',
            '</div>',
    ].join('\n');
    $(".ctk-tb-detail").append(html_single_sda);
    }
        $("#urutans").empty;
        $("#urutans").val("3");

        //new code
        initCombobox('ykdgbkpk', 36);

        var aa = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            var sum = $('#ykdkppn').val();
            //console.log(sum);
            //var ysum = akun.kdbeban+"_"+akun.kdjnsban+"_"+akun.kdctarik;
            //updateComboboxAndSelectedSumber('ysumber', 65, ysum);
            initCombobox('ysumber', 65);
            //setTimeout(function(){ ChangeSumberList('y'); }, 3000);
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        var ab = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            //updateComboboxAndSelected('yregister', 73, akun.register);
            var header = cek_header(data_selected[0]);
            if (header !== null || header !== ''){
                //console.log(header);
                refreshComboboxOutput('yid_ppk',47,'kdsatker', kd_satker, header.id_ppk);
            }

            if (header.kdheader === '2'){
                $('#yheader2').val(header.detail);
            }
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        var ac = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            //$("input[name='ycaraHitung']").val([akun.carahitung]);
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        var ad = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            //console.log("set ini");
            //$("#yinputPHLN").val(akun.prosenphln);
            //$("#yinputRMPdp").val(akun.prosenrmp);
            //$("#yinputRPLNPdp").val(akun.prosenrkp);
            //refreshComboboxKPPN('yinputKPPNph', 46, 'kdsatker', kd_satker, akun.kppnphln);
            //refreshComboboxKPPN('yinputKPPNrm', 46, 'kdsatker', kd_satker, akun.kppnrmp);
            //refreshComboboxKPPN('yinputKPPNrp', 46, 'kdsatker', kd_satker, akun.kppnrkp);
            //updateComboboxAndSelected('yregisterpdp', 73, akun.regdam);
            //$('#yinputKPPNph').val(akun.kppnphln);
            //$('#yinputKPPNrm').val(akun.kppnrmp);
            //$('#yinputKPPNrp').val(akun.kppnrkp);
            //$('#yregisterpdp').val(akun.regdam);
//            if (akun.carahitung === '4'){
//                $("#ypanelNonSharing").show();
//                $("#yinputNSPHLN2").val(akun.paguphln);
//                $("#yinputNSRMPdp2").val(akun.pagurmp);
//                $("#yinputNSRPLNPdp2").val(akun.pagurkp);
//            }
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        aa().then(ab);//.then(ad);
        //$('#yinputHal4').val(akun.ket);
        //$('#yinputDipa').val(akun.ket2);
    }
    function get_prov() {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_prov') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            console.log(data)
                    x = data;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function dtDeleteRow(type, id) {
    if (confirm('Yakin untuk menghapus data ini?'))
    {

    var url = "<?php echo base_url(); ?>" + "pagu_tahapan/ajax_delete/" + type + "/" + id;
    var params = {"formData":{}, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
            .done(function (data) {

            var tlist_paket = $('#tlist_paket').DataTable();
            tlist_paket.ajax.reload();
            })
            .fail(function () {
            alert("error");
            })
    }
    }

    function hapus_lampiran(id, idus) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

            var url = "<?php echo base_url(); ?>" + "usulan_paket/hps_lampiran/" + id + "/" + idus;
            var params = {"formData": {}, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                    .done(function (data) {
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload()
                        var tab = $('#table_id2').DataTable();
                        tab.ajax.reload();
                        ;
                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }

    function simpanFormPaket() {

    var mode = $('#modeform').val();
    var url;
    var formDataPaket = new FormData($("#frm-paket")[0]);
    console.log(formDataPaket);
    if (mode == 'tambah_paket') {
    url = "<?php echo base_url(); ?>pagu_tahapan/ajax_add/paket";
    } else if (mode == 'edit_paket') {
    url = "<?php echo base_url(); ?>pagu_tahapan/ajax_update/paket";
    }
    // ajax adding data to database
    $.ajax({
    url: url,
            type: "POST",
            data: formDataPaket,
            dataType: "JSON",
            //async : false,
            //cache : false,
            contentType: false,
            processData: false,
            success: function (data)
            {

            if (data.status) //if success close modal and reload ajax table
            {
            $("#modal-tambah").modal('hide');
            var tlist_paket = $('#tlist_paket').DataTable();
            tlist_paket.ajax.reload();
            } else
            {

            }

            },
            error: function (jqXHR, textStatus, errorThrown)
            {
            alert('Error adding / update Paket');
            }
    }
    );
    }


    function simpanForm() {
        $("#ykdakun").prop("disabled",false)
        $("#frm-detail").validate({
           submitHandler: function(form) {

           }
        });

        if($( "#frm-detail" ).valid()==true){
            var phln = 0;
            var sbsn = 0;
            var jumlah = 0;
            var volume = 0;
            var rmp = 0;
            var ii='2';

        //    if ($("#id_ruas").val() != "-1" || $("#id_ruas").val() != "#") {
        //
        //    if ($("#id_jembatan").val() == "#" || $("#id_jembatan").val() == "-1") {
        //
        //    $("#ydetail").val($("#id_ruas option:selected").text());
        //    } else {
        //
        //
        //    $("#ydetail").val($("#id_ruas option:selected").text());
        //    }
        //    }
                    var id_ruas=$("#id_ruas").val();
                    var id_jembatan=$("#yid_jembatan").val();
                    var isidetail=null;
                    if(id_ruas=='' || id_ruas == null && id_jembatan=='' || id_jembatan == null)
                    {
                        isidetail=$("#ydetail").val();
                    }else if(id_ruas != '' && id_jembatan == '')
                    {
                        isidetail=$("#id_ruas option:selected").text();

                    }else
                    {
                        isidetail=$("#yid_jembatan option:selected").text();

                    }

        //    checkOutSumberdanaDetail();
                    checkOutPenandaanDetail();
                    var uraian = $("#ydetail").val();
                    var tags_val = JSON.stringify(rujukan_all).replace(/\\/g, '');
                    tags_val = tags_val.replace(/\\/g, '');
                    tags_val = tags_val.replace('"[', "[");
                    tags_val = tags_val.replace(']"', "]");
                    tags_val = tags_val.replace(/\"{/g, '{');
                    tags_val = tags_val.replace(/\}"/g, '}');
                        var xsumber=$("#ysumber").val();
                    var kdbeban=xsumber.split('_')[0];
                    var kdjnsban=xsumber.split('_')[1];
                    var kdctarik=xsumber.split('_')[2];

                    //new code
                    var kdkppn = $("#ykdkppn").val();
                    var register = $("#yregister").val();
                    var caraHitung = $("input[name='ycaraHitung']:checked").val();
                    var inputPHLN = $("#yinputPHLN").val(); //prosen
                    var inputRMPdp = $("#yinputRMPdp").val(); //prosen
                    var inputRPLNPdp = $("#yinputRPLNPdp").val(); //prosen
                    var rinputPHLN = $("#yinputNSPHLN2").val(); //Rp
                    var rinputRMPdp = $("#yinputNSRMPdp2").val(); //Rp
                    var rinputRPLNPdp = $("#yinputNSRPLNPdp2").val(); //Rp
                    var jumlahhh = $("#yjumlah").val();
                    var jumlahh = jumlahhh.split(',').join('');
                    var paguPHLN = 0;
                    var paguRMPdp = 0;
                    var paguRPLNPdp = 0;
                    if(caraHitung === '4'){
                        paguPHLN = rinputPHLN.split(',').join('');
                        paguRMPdp = rinputRMPdp.split(',').join('');
                        paguRPLNPdp = rinputRPLNPdp.split(',').join('');
                    } else {
                        paguPHLN = (jumlahh * inputPHLN)/100;
                        paguRMPdp = (jumlahh * inputRMPdp)/100;
                        paguRPLNPdp = (jumlahh * inputRPLNPdp)/100;
                    }

                    var inputKPPNph = $("#yinputKPPNph").val();
                    var inputKPPNrm = $("#yinputKPPNrm").val();
                    var inputKPPNrp = $("#yinputKPPNrp").val();
                    var registerpdp = $("#yregisterpdp").val();
                    var vol1 = $("#yinputVol1").val();
                    var vol2 = $("#yinputVol2").val();
                    var vol3 = $("#yinputVol3").val();
                    var vol4 = $("#yinputVol4").val();
                    var sat1 = $("#yinputSat1").val();
                    var sat2 = $("#yinputSat2").val();
                    var sat3 = $("#yinputSat3").val();
                    var sat4 = $("#yinputSat4").val();
                    var inputHal4 = $("#yinputHal4").val();
                    var inputDipa = $("#yinputDipa").val();
                    //$("#ykdakun").prop('disabled', false);
                    var block = $("input[name='yitemblokir']:checked").val();
                    console.log(block);
                    var kdblokir = null;
                    if (block === '1'){
                        kdblokir = '*';
                    } else {
                        kdblokir = null;
                    }
                    var blokirphlns = $('#yinputBlokirPHLN').val();
                    var blokirphln = blokirphlns.split(',').join('');
                    var blokirrmps = $('#yinputBlokirRMPdp').val();
                    var blokirrmp = blokirrmps.split(',').join('');
                    var blokirrkps =  $('#yinputBlokirRPLNPdp').val();
                    var blokirrkp = blokirrkps.split(',').join('');
                    var RphBlokirs =  $('#yinputRphBlokir').val();
                    var RphBlokir = RphBlokirs.split(',').join('');

                    var kab = $('#ykabkot').val();
                    var kabkota = kab.split('-')[0];

                    var objmasterdetail = {
                    "kd_kegiatan": $("#ykd_kegiatan_sel").val(), //master
                            "kd_output": $("#ykd_output_sel").val(),
                            "kd_sub_output": $("#ykd_sub_output_sel").val(),
                            "kd_komponen": $("#ykd_komponen_sel").val(),
                            "kd_sub_komponen": $("#ykd_sub_komponen_sel").val(),
                            "thang": $("#ythang_sel").val(),
                            "satuan": $("#ysatuan").val(),
                            "volume": $("#volume").val(),
                            "kdgbkpk": $("#ykdgbkpk").val(),
                            "kdakun": $("#ykdakun").val(),
                            "jumlah": $("#yjumlah").val().replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"),
                            "hargasat":$("#yhargasat").val().replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"),
                            "kdlokasi": $("#yprov").val(),
                            "kdkabkota": kabkota, //$("#ykabkot").val(),
                            "id_jembatan": $("#yid_jembatan").val(),
                            "id_ruas": $("#id_ruas").val(),
                            "sta_awal": $("#sta_awal_sel").val(),
                            "sta_akhir": $("#sta_akhir_sel").val(),
                            "longitude": $("#ylongitude").val(),
                            "latitude": $("#ylatitude").val(),
                            "longitude2": $("#ylongitude2").val(),
                            "latitude2": $("#ylatitude2").val(),
                            "id_paket": $("#yid_paket").val(),
                //            "obj_sumber_dana":JSON.stringify(arr_sumber_dana),
                            "kd_program" :"08",
                            "detail": isidetail,
                            "rj_rams":JSON.stringify(obj_rj_rams),
                            "rj_irms":JSON.stringify(obj_rj_irms),
                            "rj_renstra":JSON.stringify(obj_rj_renstra),
                            "rj_eprogram":JSON.stringify(obj_rj_eprogram),
                            "rj_dpr":JSON.stringify(obj_rj_dpr),
                            "rj_pemda":JSON.stringify(obj_rj_pemda),
                            "rj_sipro":JSON.stringify(obj_rj_sipro),
                            "tags_val":tags_val,
                            "kdbeban":kdbeban,
                            "kdjnsban":kdjnsban,
                            "kdctarik":kdctarik,
                            "kd_tahapan":kd_tahapan,

                            //new code
                            "id_ppk": $('#yid_ppk').val(),
                            "kdkppn": kdkppn,
                            "register": register,
                            "carahitung": caraHitung,
                            "prosenphln": inputPHLN,
                            "prosenrmp": inputRMPdp,
                            "prosenrkp": inputRPLNPdp,
                            "kppnphln": inputKPPNph,
                            "kppnrmp": inputKPPNrm,
                            "kppnrkp": inputKPPNrp,
                            "paguphln": paguPHLN,
                            "pagurmp": paguRMPdp,
                            "pagurkp": paguRPLNPdp,
                            "regdam": registerpdp,
                            "ket": inputHal4,
                            "ket2": inputDipa,
                            "vol1": vol1,
                            "vol2": vol2,
                            "vol3": vol3,
                            "vol4": vol4,
                            "sat1": sat1,
                            "sat2": sat2,
                            "sat3": sat3,
                            "sat4": sat4,
                            "geom": $('#ygeom').val(),
                            "kdsbu": $('#ykdsbu').val(),
                            "kdblokirs" :kdblokir,
                            "kdblokir" :$('#ykd_blokir').val(),
                            "urblokir" :$('#yurblokir').val(),
                            "blokirphln": blokirphln,
                            "blokirrmp": blokirrmp,
                            "blokirrkp": blokirrkp,
                            "rphblokir": RphBlokir,
                            "header2": $('#yheader2').val()
                    };
                    console.log(objmasterdetail);
                    var url = "<?php echo base_url("pagu_tahapan/simpan_detail") ?>"

                    var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
                    $.post(url, params).done(function (data) {
                        if(data === '1'){
                            alert('penanganan pada ruas ini sudah ada di paket lain');
                        } else if (data === '2'){
                            alert('penanganan pada jembatan ini sudah ada di paket lain');
                        } else if (data === '3'){
                            alert('Akun sudah ada');
                        } else {
                            var tlist_paket = $('#tlist_paket').DataTable();
                            tlist_paket.ajax.reload();
                            $('#modal-detail').modal('hide');
                        }
                    //$('#modal-detail').modal('hide');
                    })
                    .fail(function () {
                    alert("error");
                    })

                    $(".error").css("color","red");
        }


    }

    function preview()
    {

    $('#imagepreview').attr('src', $('#imageresource').attr('src')); // here asign the image to the modal when the user click the enlarge link
    $('#imagemodal').modal('show'); // imagemodal is the id attribute assigned to the bootstrap modal, then i use the show function

    }

    function upload_attachment(id) {
    var data_selected = xhrdata.data.filter(x => x[0] == id)[0];
    var xiduser = id_user_get;
    var xidusulan = data_selected[0];
    var xthang = data_selected[1];
    var str_src = "<?php echo base_url('/upload_file_pagu_tahapan/fileupload?id_user='); ?>" + xiduser + "&id_usulan=" + xidusulan + "&thang=" + xthang;

    $("#iframeupload").attr("src", str_src);
    $("#modal-upload").modal("show");
    }

    function download_attachment(id) {
                     $("#judul").val('')
                    $("#nmfile").val('')
                    $("#kate").empty();
                    if(role==7)
                    {
                    $("#kate").append("<option value=''>--Pilih--</option>",
                                        "<option value='KAK'>KAK</option>",
                                        "<option value='RAB'>RAB</option>");
                     }else
                     {
                     $("#kate").append("<option value=''>--Pilih--</option>",
                                         "<option value='KAK'>KAK</option>",
                                        "<option value='RAB'>RAB</option>",
                                        "<option value='DED'>RC DED</option>",
                                        "<option value='FS'>RC FS</option>",
                                        "<option value='LAHAN'>RC LAHAN</option>",
                                        "<option value='DOKLING'>RC DOK.LINGKUNGAN</option>",
                                        );
                     }
//                    alert(id);
        $("#id_usulans").val(id);
    var tab = $('#table_id2').DataTable();
    tab.destroy()
            $('#table_id2 td').empty();
    listing_attachment2(id);
    $("#modal-download").modal("show");
    }

    function close_modal_attachment() {

    $("#modal-download").modal("hide");

    }

    var table_attachment = null;
    function listing_attachment2(id) {

        var data_selected = xhrdata.data.filter(x => x[0] == id)[0];
        var xiduser = role;
        var xthang = data_selected[1];
        if ($.fn.dataTable.isDataTable('#table_id2')) {

            table_attachment = $('#table_id2').DataTable();
        } else {

            table_attachment = $('#table_id2').DataTable({
                "createdRow": function (row, data, index) {
                    var ico_class = get_extentsion_file(data[0]);
                    var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp"
                    $('td', row).eq(0).prepend(html_icon);
                },
                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]],
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>pagu_tahapan/ssp_attachment",
                    data: function (d) {
                        d.id = id;
                        d.role = role;
                        d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    }
                },
                "aoColumnDefs": [
                    {
                        "aTargets": [0],
                        "mRender": function (data, type, full) {


                            return full[3];
                        }
                    }, {
                        "aTargets": [1],
                        "mRender": function (data, type, full) {


                            return full[4];
                        }
                    },

                    {
                        "aTargets": [2],
                        "mRender": function (data, type, full) {
                            //console.log("full attachment");
                            //console.log(full);
                            var data_full_attachment = full[0];
                            var dire = data_full_attachment.substr(0, 6);
                            var html_button = [
                                "<a target='_blank' class='btn btn-primary' href='<?php echo base_url(); ?>uploads/" + dire + "/" + data_full_attachment + "'><i class='fa fa-download'></i></a>",
                                "<a target='_blank' class='btn btn-danger' onclick=hapus_lampiran('" + full[1] + "','" + full[2] + "')><i class='fa fa-trash'></i></a>",
//                            "<button onclick=download_lampiran('" + data_full_attachment + "') class='btn btn-primary btn-xs'>",
//                            "<i class='fa fa-download'>",
//                            "</i>",
//                            "</button>",
                            ].join("\n");
                            return html_button;
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();
                console.log(xhrdata1);
            });

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        }

    }

    function download_file(data) {

    var filname = data;
    window.open('<?php echo base_url("/pagu_tahapan/download/"); ?>' + filname, '_blank');
    }

    function get_jenis_belanja(kd_jenis_belanja){
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_jenis_belanja') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            console.log(data)
                    x = data;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }


    function bind_kode_jenis_belanja(kd_jenis_belanja){
    var obj_jenis_belanja = get_jenis_belanja(kd_jenis_belanja);
    for (var i = 0; i <= obj_jenis_belanja.length - 1; i++){
    if (no_ruas == obj_jenis_belanja[i].no_ruas){
    $("#wid_ruas").append("<option selected value=" + obj_jenis_belanja[i].no_ruas + ">" + obj_jenis_belanja[i].linkname + "</option>");
    } else{
    $("#wid_ruas").append("<option value=" + obj_jenis_belanja[i].no_ruas + ">" + obj_jenis_belanja[i].linkname + "</option>");
    }
    }
    }

    function get_extentsion_file(file) {
    var extension = file.substr((file.lastIndexOf('.') + 1));
    switch (extension) {
    case 'jpg':
            case 'png':
            case 'jpeg':
            case 'gif':
            return 'fa fa-image'; // There's was a typo in the example where
    break; // the alert ended with pdf instead of gif.
    case 'zip':
            case 'rar':
            //alert('was zip rar');
            return 'fa fa-file-archive-o'
            break;
    case 'pdf':
            return 'fa fa-file-pdf-o';
    case 'xlsx':
            return 'fa fa-file-excel-o';
    break;
    default:
            "fa fa-file"
    }
    }

    function simpan_form_pagu(){
        $("#kdakun").prop("disabled",false)
        $("#kd_output").prop("disabled",false)
        $("#kd_sub_output").prop("disabled",false)
        $("#kd_komponen").prop("disabled",false)
        $("#xjumlah").prop("disabled",false)
        $("#frm-tambah").validate({
        submitHandler: function(form) {

        }
       });


       if($( "#frm-tambah" ).valid()==true){//simpanForm
            var jumlah = 0;
            var volume = 0;
            var id_ruas = $("#xid_ruas").val();
            var id_jembatan = $("#xid_jembatan").val();
            var sta_awal = $("#sta_awal").val();
            var sta_akhir = $("#sta_akhir").val();
            var longitude = $("#longitude").val();
            var latitude = $("#latitude").val();
            var longitudes = $("#longitude2").val();
            var latitudes = $("#latitude2").val();
            $("#kd_output").prop("disabled",false);
            $("#kd_sub_output").prop("disabled",false);
            $("#kd_kegiatan").prop("disabled",false);
            if(longitudes=='') { var longs=null;}else{ var longs=longitudes;}
            if(latitudes=='') { var lats=null;}else{ var lats=latitudes;}
            var ii='1';
             if (isNaN(parseInt($('#pln'+ii).val()))) {
                 var b = 0
             } else {
     //             $('#pln'+ii).val(formatRupiah($('#pln'+ii).val()))
                 var b = parseInt($('#pln'+ii).val().replace(/\,/g, ""))
             }
             if (isNaN(parseInt($('#rmp'+ii).val()))) {
                 var c = 0
             } else {
     //             $('#rmp'+ii).val(formatRupiah($('#rmp'+ii).val()))
                 var c = parseInt($('#rmp'+ii).val().replace(/\,/g, ""))
             }
             if (isNaN(parseInt($('#pnbp'+ii).val()))) {
                 var d = 0
             } else {
     //             $('#pnbp'+ii).val(formatRupiah($('#pnbp'+ii).val()))
                 var d = parseInt($('#pnbp'+ii).val().replace(/\,/g, ""))
             }
             if (isNaN(parseInt($('#pdn'+ii).val()))) {
                 var e = 0
             } else {
     //             $('#pdn'+ii).val(formatRupiah($('#pdn').val()))
                 var e = parseInt($('#pdn'+ii).val().replace(/\,/g, ""))
             }
             if (isNaN(parseInt($('#blu'+ii).val()))) {
                 var f = 0
             } else {
     //             $('#blu'+ii).val(formatRupiah($('#blu'+ii).val()))
                 var f = parseInt($('#blu'+ii).val().replace(/\,/g, ""))
             }
             if (isNaN(parseInt($('#sbsn'+ii).val()))) {
                 var g = 0
             } else {
     //            $('#sbsn'+ii).val(formatRupiah($('#sbsn'+ii).val()))
                 var g = parseInt($('#sbsn'+ii).val().replace(/\,/g, ""))
             }

            $("#xtotalpagu").val($(".decformat").val());
            if (check_rc(obj_data_cart.phln) == "#") {
            phln = 0;
            }

            if (check_rc(obj_data_cart.sbsn) == "#") {
            sbsn = 0;
            }

            if (check_rc(obj_data_cart.rmp) == "#") {
            rmp = 0;
            }

            if ($("#xtotalpagu").val() == "#") {
            jumlah = 0;
            } else {
            jumlah = $("#xtotalpagu").val();
            }

            if ($("#xvolume").val() == "#") {
            volume = 0;
            } else {
            volume = $("#xvolume").val();
            }

            var isidetail=null;
            if(id_ruas=='' || id_ruas == null && id_jembatan=='' || id_jembatan == null)
            {
                isidetail=$("#xdetail").val();
            }else if(id_ruas != '' && id_jembatan == '')
            {
                isidetail=$("#xid_ruas option:selected").text();

            }else
            {
                isidetail=$("#xid_jembatan option:selected").text();

            }

            var xhargasat = $("#xhargasat").val();
            var xjumlah = $("#xjumlah").val();
            var xrm = $("#xrm").val();

            checkOutPenandaan();

            var tags_val = JSON.stringify(rujukan_all).replace(/\\/g, '');
            tags_val = tags_val.replace(/\"{/g, '{');
            tags_val = tags_val.replace(/\}"/g, '}');

            var xsumber=$("#xsumber").val();
            var kdbeban=xsumber.split('_')[0];
            var kdjnsban=xsumber.split('_')[1];
            var kdctarik=xsumber.split('_')[2];

            //new code
            var register = $("#xregister").val();
            var caraHitung = $("input[name='xcaraHitung']:checked").val();
            var inputPHLN = $("#xinputPHLN").val(); //prosen
            var inputRMPdp = $("#xinputRMPdp").val(); //prosen
            var inputRPLNPdp = $("#xinputRPLNPdp").val(); //prosen
            var rinputPHLN = $("#xinputNSPHLN2").val(); //Rp
            var rinputRMPdp = $("#xinputNSRMPdp2").val(); //Rp
            var rinputRPLNPdp = $("#xinputNSRPLNPdp2").val(); //Rp
            var jumlahh = xjumlah.split(',').join('');

            var paguPHLN = 0;
            var paguRMPdp = 0;
            var paguRPLNPdp = 0;
            if(caraHitung === '4'){
                paguPHLN = rinputPHLN;
                paguRMPdp = rinputRMPdp;
                paguRPLNPdp = rinputRPLNPdp;
            } else {
                paguPHLN = (jumlahh * inputPHLN)/100;
                paguRMPdp = (jumlahh * inputRMPdp)/100;
                paguRPLNPdp = (jumlahh * inputRPLNPdp)/100;
            }

            var inputKPPNph = $("#xinputKPPNph").val();
            var inputKPPNrm = $("#xinputKPPNrm").val();
            var inputKPPNrp = $("#xinputKPPNrp").val();
            var registerpdp = $("#xregisterpdp").val();
            var vol1 = $("#xinputVol1").val();
            var vol2 = $("#xinputVol2").val();
            var vol3 = $("#xinputVol3").val();
            var vol4 = $("#xinputVol4").val();
            var sat1 = $("#xinputSat1").val();
            var sat2 = $("#xinputSat2").val();
            var sat3 = $("#xinputSat3").val();
            var sat4 = $("#xinputSat4").val();
            var inputHal4 = $("#xinputHal4").val();
            var inputDipa = $("#xinputDipa").val();
            var ursoutList = document.getElementById("kd_sub_output");
            var ur = ursoutList.options[ursoutList.selectedIndex].text;
            var ursout = ur.split(' - ')[1];
            var urkmpnenList = document.getElementById("kd_komponen");
            var urk = urkmpnenList.options[urkmpnenList.selectedIndex].text;
            var urkmpnen = urk.split(' - ')[1];
            var block = $("input[name='xitemblokir']:checked").val();
            var kdblokir = null;
            if (block === '1'){
                kdblokir = '*';
            } else {
                kdblokir = null;
            }
            var blokirphlns = $('#xinputBlokirPHLN').val();
            var blokirphln = blokirphlns.split(',').join('');
            var blokirrmps = $('#xinputBlokirRMPdp').val();
            var blokirrmp = blokirrmps.split(',').join('');
            var blokirrkps =  $('#xinputBlokirRPLNPdp').val();
            var blokirrkp = blokirrkps.split(',').join('');
            var RphBlokirs =  $('#xinputRphBlokir').val();
            var RphBlokir = RphBlokirs.split(',').join('');

            var kab = $('#kabkot').val();
            var kabkota = kab.split('-')[0];

            var xobj_usulan = {
                    "thang": $("#thang").val(),
                    "volume": volume,
                    "rm":'0',
                    "rmp":c,
                    "sbsn":g,
                    "blu":f,
                    "pnbp":d,
                    "phln":b,
                    "pdn":e,
                    "kd_unit": obj_data_cart.kd_unit, //not exists in form exists in table
                    "kd_program": obj_data_cart.kd_program, //not exists in form exists in table
                    "kd_kegiatan": $("#kd_kegiatan").val(), //not exists in form exists in table
                    "kd_output": $("#kd_output").val(),
                    "kd_sub_output": $("#kd_sub_output").val(),
                    "kdlokasi": $("#prov").val(), //kode provinsi
                    "kdkabkota": kabkota, // $("#kabkot").val(),
                    "id_jenis_kontrak": $("#jnskontrak").val(),
                    "kd_komponen": $("#kd_komponen").val(),
                    "kd_sub_komponen": $("#kd_sub_komponen").val(),
                    "nama_sub_komponen": $("#xnama_sub_komponen").val(), //tanpa x id elemen sama dengan modal detail
                    "kd_jns_belanja": $("#kdgbkpk").val(), //yang ada pada form sama dengan jenis belanja
                    "kdgbkpk": $("#kdgbkpk").val(),
                    "kdakun": $("#kdakun").val(),
                    "kdkppn": $("#kdkppn").val(),
                    "id_ppk": $("#id_ppk").val(),
                    "rc_ded_status": $("#rc_ded_status").val(),
                    "rc_fs_status": $("#rc_fs_status").val(),
                    "rc_lahan_status": $("#rc_lahan_status").val(),
                    "rc_doklin_status": $("#rc_doklin_status").val(),
                    "kak_status": $("#kak").val(),
                    "rab_status": $("#rab").val(),
                    "rj_rams":JSON.stringify(obj_rj_rams),
                    "rj_irms":JSON.stringify(obj_rj_irms),
                    "rj_renstra":JSON.stringify(obj_rj_renstra),
                    "rj_eprogram":JSON.stringify(obj_rj_eprogram),
                    "rj_dpr":JSON.stringify(obj_rj_dpr),
                    "rj_pemda":JSON.stringify(obj_rj_pemda),
                    "rj_sipro":JSON.stringify(obj_rj_sipro),
                    "tags_val":tags_val,
                    "id_ruas": id_ruas,
                    "sta_awal": sta_awal,
                    "sta_akhir": sta_akhir,
                    "id_jembatan": id_jembatan,
                    "longitude": longitude,
                    "latitude": latitude,
                    "longitude2": longs,
                    "latitude2": lats,
                    "detail": isidetail,
                    "satuan": $("#xsatuan").val(),
                    "hargasat":xhargasat.replace(/\D/g, ''),
                    "jumlah":xjumlah.replace(/\D/g, ''),
                    "kdbeban":kdbeban,
                    "kdjnsban":kdjnsban,
                    "kdctarik":kdctarik,
                    "kd_tahapan":kd_tahapan,
                    //new code
                    "register": register,
                    "carahitung": caraHitung,
                    "prosenphln": inputPHLN,
                    "prosenrmp": inputRMPdp,
                    "prosenrkp": inputRPLNPdp,
                    "kppnphln": inputKPPNph,
                    "kppnrmp": inputKPPNrm,
                    "kppnrkp": inputKPPNrp,
                    "paguphln": paguPHLN,
                    "pagurmp": paguRMPdp,
                    "pagurkp": paguRPLNPdp,
                    "regdam": registerpdp,
                    "vol1": vol1,
                    "vol2": vol2,
                    "vol3": vol3,
                    "vol4": vol4,
                    "sat1": sat1,
                    "sat2": sat2,
                    "sat3": sat3,
                    "sat4": sat4,
                    "ket": inputHal4,
                    "ket2": inputDipa,
                    "geom": $('#xgeom').val(),
                    "kdsbu": $('#xkdsbu').val(),
                    //update code
                    "kdblokirs" :kdblokir,
                    "kdblokir" :$('#xkd_blokir').val(),
                    "urblokir" :$('#xurblokir').val(),
                    "blokirphln": blokirphln,
                    "blokirrmp": blokirrmp,
                    "blokirrkp": blokirrkp,
                    "rphblokir": RphBlokir,
                    "header2": $('#xheader2').val(),
                    //d_output
                    "volmin1": $('#xinputVolKpjm1').val(),
                    "vol": $('#xinputVolKpjm2').val(),
                    "volpls1": $('#xinputVolKpjm3').val(),
                    "volpls2": $('#xinputVolKpjm4').val(),
                    "volpls3": $('#xinputVolKpjm5').val(),
                    "thangawal": $('#xinputTAAwal').val(),
                    "thangakhir": $('#xinputTAAkhir').val(),
                    //d_soutput
                    "volsout": $('#xinputSoutVolume').val(),
                    "ursoutput" : ursout,
                    //d_kmpnen
                    "kdbiaya": $('#xinputJnsBiaya').val(),
                    "kdsbiaya": $('#xinputSftBiaya').val(),
                    "indekskali": $("input[name='xinputIndexKpjm']:checked").val(),
                    "indeksout": $("input[name='xinputIndexOutput']:checked").val(),
                    "n1": $("input[id='xinputDianggarkanThn1']:checked").val(),
                    "n2": $("input[id='xinputDianggarkanThn2']:checked").val(),
                    "n3": $("input[id='xinputDianggarkanThn3']:checked").val(),
                    "n4": $("input[id='xinputDianggarkanThn4']:checked").val(),
                    "urkmpnen" : urkmpnen,

                    "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>",

            };
            console.log(xobj_usulan);
            //return;
            var url = "<?php echo base_url('pagu_tahapan/save_usulan_pagu'); ?>";
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                if(data === '1'){
                    alert('penanganan pada ruas ini sudah ada di paket lain');
                } else if (data === '2'){
                    alert('penanganan pada jembatan ini sudah ada di paket lain');
                } else if (data === '3'){
                    alert('Akun sudah ada');
                }else {
                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();
                    $('#modal-tambah').modal('hide');
                }
            //var tlist_paket = $('#tlist_paket').DataTable();
            //tlist_paket.ajax.reload();
            //$('#modal-tambah').modal('hide');
            })
            .fail(function () {
            alert("error");
            })
       }else{
           alert("usulan gagal disimpan, harap periksa informasi di setiap field isian")
            $(".error").css("color","red");
       }
    }

    function update_form_pagu(){


    $("#frm-tambah").validate({
        submitHandler: function(form) {

        }
    });
    var id_paket = $("#zid_paket").val();
        var data_selected = xhrdata.data.filter(x => x[0] == id_paket)[0];
        var nmsubs = $.trim(data_selected[19].split("-")[1]);
        var kdg = $("#zkd_kegiatan").val();
        var kdo = $("#zkd_output").val()
        var kdso = $("#zkd_sub_output").val()
        var kdkom = $("#zkd_komponen").val()
        var kdsub = $("#zkd_sub_komponen").val()
        var nmsub = $("#znama_sub_komponen").val()
        var than = $("#zthang").val()
        var kdgb = $("#zkdgbkpk").val()
        var kdlok = $("#zprov").val()
        var jnsk = $("#zjnskontrak").val()
        var kdkp = $("#zkdkppn").val()
        var idppk = $("#zid_ppk").val()
        var rcded = $("#zrc_ded_status").val()
        var rcfs = $("#zrc_fs_status").val()
        var rclhn = $("#zrc_lahan_status").val()
        var rcdoklin = $("#zrc_doklin_status").val()
        var kak = $("#zkak").val()
        var rab = $("#zrab").val()
        var flow1 = $("#flow1").val()
        var flow2 = $("#flow2").val()
        var flow3 = $("#flow3").val()
        if(flow3=='') {
            var gbung = flow1+','+flow2;

        }else{
            var gbung = flow1+','+flow2+','+flow3;
        }
        if (data_selected[9] == kdg && data_selected[14] == kdo && data_selected[15] == kdso && data_selected[17] == kdkom && data_selected[18] == kdsub && nmsubs == nmsub && data_selected[21] == jnsk && data_selected[22] == kdkp && data_selected[23] == idppk && data_selected[26] == rcded && data_selected[27] == rcfs && data_selected[28] == rclhn && data_selected[29] == rcdoklin && data_selected[34] == rab && data_selected[35] == kak)
        {
            // alert('tidak ada perubahan')
            var sp = "tidakberubah";
        } else
        {
            //  alert('ada perubahan')
            var sp = "berubah";
        }
    if($( "#frm-edit-paket" ).valid()==true){

        var ursoutList = document.getElementById("zkd_sub_output");
        var ur = ursoutList.options[ursoutList.selectedIndex].text;
        var ursout = ur.split(' - ')[1];
        var urkmpnenList = document.getElementById("zkd_komponen");
        var urk = urkmpnenList.options[urkmpnenList.selectedIndex].text;
        var urkmpnen = urk.split(' - ')[1];
         var objmasterdetail = {
            "kd_kegiatan": $("#zkd_kegiatan").val(), //master
            "kd_output": $("#zkd_output").val(),
            "kd_sub_output": $("#zkd_sub_output").val(),
            "kd_komponen": $("#zkd_komponen").val(),
            "kd_sub_komponen": $("#zkd_sub_komponen").val(),
            "nama_sub_komponen": $("#znama_sub_komponen").val(),
            "thang": $("#zthang").val(),
            "kdgbkpk": $("#zkdgbkpk").val(),
            "kdlokasi": $("#zprov").val(),
            "id_paket": $("#zid_paket").val(),
            "jnskontrak": $("#zjnskontrak").val(),
            "kdkppn": $("#zkdkppn").val(),
            "id_ppk": $("#zid_ppk").val(),
            "rc_ded_status": $("#zrc_ded_status").val(),
            "rc_fs_status": $("#zrc_fs_status").val(),
            "rc_lahan_status": $("#zrc_lahan_status").val(),
            "rc_doklin_status": $("#zrc_doklin_status").val(),
            "kak_status": $("#zkak").val(),
            "rab_status": $("#zrab").val(),
            "status_perubahan": sp,
            "perubahan":gbung,
            //update code
            //d_output
            "volmin1": $('#zinputVolKpjm1').val(),
            "vol": $('#zinputVolKpjm2').val(),
            "volpls1": $('#zinputVolKpjm3').val(),
            "volpls2": $('#zinputVolKpjm4').val(),
            "volpls3": $('#zinputVolKpjm5').val(),
            "thangawal": $('#zinputTAAwal').val(),
            "thangakhir": $('#zinputTAAkhir').val(),
            //d_soutput
            "volsout": $('#zinputSoutVolume').val(),
            "ursoutput": ursout,
            //d_kmpnen
            "kdbiaya": $('#zinputJnsBiaya').val(),
            "kdsbiaya": $('#zinputSftBiaya').val(),
            "indekskali": $("input[name='zinputIndexKpjm']:checked").val(),
            "indeksout": $("input[name='zinputIndexOutput']:checked").val(),
            "n1": $("input[id='zinputDianggarkanThn1']:checked").val(),
            "n2": $("input[id='zinputDianggarkanThn2']:checked").val(),
            "n3": $("input[id='zinputDianggarkanThn3']:checked").val(),
            "n4": $("input[id='zinputDianggarkanThn4']:checked").val(),
            "urkmpnen": urkmpnen,
            };
            console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("pagu_tahapan/update_paket_pagu") ?>";
            $.post(url, params).done(function (data) {
            var tlist_paket = $('#tlist_paket').DataTable();
            tlist_paket.ajax.reload();
            $('#modal-edit-paket').modal('hide');
            })
            .fail(function () {
            alert("error");
            })

    }else{
        alert("data gagal diupdate, silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda ")
    }


    }

     function bs_input_file() {
        $(".input-file").before(
                function () {
                    if (!$(this).prev().hasClass('input-ghost')) {
                        var element = $("<input type='file' class='input-ghost' style='visibility:hidden; height:0'>");
                        element.attr("name", $(this).attr("name"));
                        element.change(function () {
                            element.next(element).find('input').val((element.val()).split('\\').pop());
                        });
                        $(this).find("button.btn-choose").click(function () {
                            element.click();
                        });
                        $(this).find("button.btn-reset").click(function () {
                            element.val(null);
                            $(this).parents(".input-file").find('input').val('');
                        });
                        $(this).find('input').css("cursor", "pointer");
                        $(this).find('input').mousedown(function () {
                            $(this).parents('.input-file').prev().click();
                            return false;
                        });
                        return element;
                    }
                }
        );
    }



    function handle_form_fisik(element) {
    var field_value = element.value;
    if (field_value == "F") {
    $(".divRuas").css("display", "");
    $(".divNonFisik").css("display", "none");
    } else {
    $(".divRuas").css("display", "none");
    $(".divNonFisik").css("display", "");
    }
    }

    //reff_index= table_name
    function refreshComboboxString(divname, refindex, refresh_field, refresh_value) {
    url = "lookup/refreshlookstringid/" + refindex + "/" + refresh_field + "/" + refresh_value;
    $.get(url).done(function (data) {
    jdata = JSON.parse(data);
    $('#' + divname).empty();
    $('#' + divname).append(new Option("--Pilih--",""));
    $.each(jdata, function (i, el) {
    $('#' + divname).append(new Option(el.val, el.id));
    });

    })
            .fail(function () {
            alert("error");
            })
            .always(function () {

            });
    }

    function updateComboboxAndSelected(divname, refindex, selvalue) {
    url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex;
    $.get(url).done(function (data) {
    var jdata = JSON.parse(data);
    $('#' + divname).empty();
    $('#' + divname).append("<option value='#' >" + "--Pilih--" + "</option>");

    for (var i = 0; i <= jdata.length - 1; i++) {
    if (jdata[i].id == selvalue) {

    $('#' + divname).append("<option selected value=" + jdata[i].id + " >" + jdata[i].val + "</option>");
    } else {
    $('#' + divname).append("<option value=" + jdata[i].id + " >" + jdata[i].val + "</option>");
    }
    }


    })
            .fail(function () {
            alert("error");
            })
            .always(function () {

            });
    }

    function numberWithCommas(x) {

    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function toObject(arr) {
    var rv = {};
    for (var i = 0; i < arr.length; ++i)
            if (arr[i] !== undefined) rv[i] = arr[i];
    return rv;
    }

    function getValue(element){
    var elementvalue = element.value;
    console.log("--element value----");
    console.log(element.value);
    if ($(".dynamic-input input[type=text]").length > 1){
    var gsumber_dana = $(".dynamic-input input[type=text]").val();
    gsumber_dana = gsumber_dana.replace(/\D/g, ''); //masih bertipe string

    var new_gsumber_dana = parseFloat(gsumber_dana) - element.value;
    $(".dynamic-input input[type=text]").first().val(numberWithCommas(new_gsumber_dana));
    element.value = numberWithCommas(elementvalue);
    }

    }

    function getValueTbDetail(element){
    alert(4444);
    var elementvalue = element.value;
    console.log("--element value----");
    console.log(element.value);
    if ($(".tb-detail").length > 1){
    var gsumber_dana = $(".tb-detail").val();
    gsumber_dana = gsumber_dana.replace(/\D/g, ''); //masih bertipe string
    console.log("gsumberdana")
            console.log(gsumber_dana);
    var new_gsumber_dana = parseFloat(gsumber_dana) - element.value;
    console.log("new gsumberdana");
    console.log(new_gsumber_dana);
    $(".tb-detail").first().val(numberWithCommas(new_gsumber_dana));
    element.value = numberWithCommas(elementvalue);
    }

    }

    var arr_sumber_dana = [];

    function checkOutSumberdana(){           //tambah paket

    for (var i = 0; i <= $(".dynamic-input input[type=text]").length - 1; i++){

    var sdlabel = $(".concept:eq(" + i + ")").text();
    var sdvalue = $(".dynamic-input input[type=text]:eq(" + i + ")").val();
    var obj_sumber_dana = {};
    obj_sumber_dana[sdlabel] = sdvalue.replace(/\D/g, '');
    arr_sumber_dana[i] = obj_sumber_dana;
    }
    }



    function checkOutSumberdanaDetail(){   //tambah ddetail

    for (var i = 0; i <= $(".dynamic-input-frm-tambah-detail input[type=text]").length; i++){

    var sdlabel = $(".concept:eq(" + i + ")").text();
    var sdvalue = $(".dynamic-input input[type=text]:eq(" + i + ")").val();
    var obj_sumber_dana = {};
    obj_sumber_dana[sdlabel] = sdvalue.replace(/\D/g, '');
    arr_sumber_dana[i] = obj_sumber_dana;
    }

    var new_arr_sumberdana = [];
    //membuang nilai null
    for (var i = 1; i <= arr_sumber_dana.length; i++){
    if (typeof arr_sumber_dana[i] == 'object'){
    new_arr_sumberdana[i] = arr_sumber_dana[i];
    }
    }

    var filtered = new_arr_sumberdana.filter(function (el) {
    return el != null;
    });
    arr_sumber_dana = filtered;
    }

    var arr_rj_rams = [];
    var obj_rj_rams = {};
    var arr_rj_irms = [];
    var obj_rj_irms = {};
    var arr_rj_renstra = [];
    var obj_rj_renstra = {};
    var arr_rj_eprogram = [];
    var obj_rj_eprogram = {};
    var arr_rj_dpr = [];
    var obj_rj_dpr = {};
    var arr_rj_pemda = [];
    var obj_rj_pemda = {};
    var arr_rj_sipro = [];
    var obj_rj_sipro = {};
    var rujukan_all = [];
    function checkOutPenandaan() {

         for (var i = 0; i <= $("#md-tbh-penandaan .tag").length - 1; i++) {
             var obj_rujukan = {};
             var tagged_rujukan = $("#md-tbh-penandaan .tag:eq(" + i + ")").text();

             if (tagged_rujukan.split("|")[0] == "RAMS") {
                 arr_rj_rams[i] = tagged_rujukan.split("|")[1];
                 obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "RAMS", kd_jns_rujukan:"2"};
             } else if (tagged_rujukan.split("|")[0] == "IRMS") {
                 arr_rj_irms[i] = tagged_rujukan.split("|")[1];
                 obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "IRMS", kd_jns_rujukan:"3"};
             } else if (tagged_rujukan.split("|")[0] == "RENSTRA") {
                 arr_rj_renstra[i] = tagged_rujukan.split("|")[1];
                 obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "RENSTRA", kd_jns_rujukan:"8"};
             } else if (tagged_rujukan.split("|")[0] == "EPROG") {
                 arr_rj_eprogram[i] = tagged_rujukan.split("|")[1];
                 obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "EPROG", kd_jns_rujukan:"7"};
             } else if (tagged_rujukan.split("|")[0] == "DPR") {
                 arr_rj_dpr[i] = tagged_rujukan.split("|")[1];
                 obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "DPR", kd_jns_rujukan:"4"};
             } else if (tagged_rujukan.split("|")[0] == "PEMDA") {
                 arr_rj_pemda[i] = tagged_rujukan.split("|")[1];
                 obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "PEMDA", kd_jns_rujukan:"5"};
             } else if (tagged_rujukan.split("|")[0] == "SIPRO") {
                 arr_rj_sipro[i] = tagged_rujukan.split("|")[1];
                 obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "SIPRO", kd_jns_rujukan:"1"};
             }
             rujukan_all[i] = JSON.stringify(obj_rujukan);
         }

         obj_rj_rams = toObject(arr_rj_rams);
         obj_rj_irms = toObject(arr_rj_irms);
         obj_rj_renstra = toObject(arr_rj_renstra);
         obj_rj_dpr = toObject(arr_rj_dpr);
         obj_rj_eprogram = toObject(arr_rj_eprogram);
         obj_rj_sipro = toObject(arr_rj_sipro);
         obj_rj_pemda = toObject(arr_rj_pemda);
     }


    function checkOutPenandaanDetail(){
      for (var i = 0; i <= $(".mdl-xtbh-detail .tag").length - 1; i++) {
                 var tagged_rujukan = $(".mdl-xtbh-detail .tag:eq(" + i + ")").text();
                 var obj_rujukan = {};
                 if (tagged_rujukan.split("|")[0] == "RAMS") {
                     arr_rj_rams[i] = tagged_rujukan.split("|")[1];
                     obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "RAMS", kd_jns_rujukan:"2"};
                 } else if (tagged_rujukan.split("|")[0] == "IRMS") {
                     arr_rj_irms[i] = tagged_rujukan.split("|")[1];
                     obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "IRMS", kd_jns_rujukan:"3"};
                 } else if (tagged_rujukan.split("|")[0] == "RENSTRA") {
                     arr_rj_renstra[i] = tagged_rujukan.split("|")[1];
                     obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "RENSTRA", kd_jns_rujukan:"8"};
                 } else if (tagged_rujukan.split("|")[0] == "EPROG") {
                     arr_rj_eprogram[i] = tagged_rujukan.split("|")[1];
                     obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "EPROG", kd_jns_rujukan:"7"};
                 } else if (tagged_rujukan.split("|")[0] == "DPR") {
                     arr_rj_dpr[i] = tagged_rujukan.split("|")[1];
                     obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "DPR", kd_jns_rujukan:"4"};
                 } else if (tagged_rujukan.split("|")[0] == "PEMDA") {
                     arr_rj_pemda[i] = tagged_rujukan.split("|")[1];
                     obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "PEMDA", kd_jns_rujukan:"5"};
                 } else if (tagged_rujukan.split("|")[0] == "SIPRO") {
                     arr_rj_sipro[i] = tagged_rujukan.split("|")[1];
                     obj_rujukan = {value: tagged_rujukan.split("|")[1], text: tagged_rujukan, rujukan: "SIPRO", kd_jns_rujukan:"1"};
                 }
                 rujukan_all[i] = JSON.stringify(obj_rujukan);
             }

             obj_rj_rams = toObject(arr_rj_rams);
             obj_rj_irms = toObject(arr_rj_irms);
             obj_rj_renstra = toObject(arr_rj_renstra);
             obj_rj_dpr = toObject(arr_rj_dpr);
             obj_rj_eprogram = toObject(arr_rj_eprogram);
             obj_rj_sipro = toObject(arr_rj_sipro);
             obj_rj_pemda = toObject(arr_rj_pemda);
    }

    function set_rams(){
    return $("#rj_rams").val();
    }

    function set_irms(){
    return $("#rj_irms").val();
    }

    function set_renstra(){
    return $("#rj_renstra").val();
    }

    function set_eprog(){
    return $("#rj_eprog").val();
    }

    function set_dpr(){
    return $("#rj_dpr").val();
    }

    function set_sipro(){
    return $("#rj_sipro").val();
    }
    function set_pemda(){
    return $("#rj_pemda").val();
    }

    function delete_tagss(id){
    var id_button = id;
    $('#' + id_button).parents('.tag').remove();

    }

    function bindTagging2(string_tagging){

    $(".bootstrap-tagsinput").remove();
    var arr_rj_rams = string_tagging.split(",");
    var html_tag_parent = [
            "<div class='bootstrap-tagsinput'>",
            "<div>"
    ].join("\n");
    $("#md-edit-penandaan").children(".div-tags3").append(html_tag_parent);
    var arr_tagging = string_tagging.split(",");
    var newlinenumber = 0;
    if (arr_tagging.length > 0){
    $("#md-edit-penandaan").children(".div-tags3").css("display", "block")
    }
    for (var i = 0; i <= arr_tagging.length - 1; i++){
    var taggkey = arr_tagging[i].split("|");
    var taggclass = "";
    if (taggkey[0] == 'RAMS'){
    taggclass = '-info';
    } else if (taggkey[0] == 'IRMS'){
    taggclass = '-success';
    } else if (taggkey[0] == 'RENSTRA'){
    taggclass = '-danger';
    }
    else if (taggkey[0] == 'EPROG'){
    taggclass = '-warning';
    }
    else if (taggkey[0] == 'SIPRO'){
    taggclass = '-primary';
    }
    else if (taggkey[0] == 'PEMDA'){
    taggclass = '-default';
    }
    else if (taggkey[0] == 'DPR'){
    taggclass = '-info';
    }

    var html_tag_childrens = ["<span class='tag label label" + taggclass + "'>",
            arr_tagging[i],
            "<span data-role='remove' id='" + taggkey[1] + "' onclick='delete_tagss(this.id)'>",
            "",
            "</span>",
            "</span>"].join("\n");
    console.log("HTML Tag Childrens");
    console.log(html_tag_childrens);
    $("#md-edit-penandaan").children(".div-tags3").children(".bootstrap-tagsinput").append(html_tag_childrens);
    if (newlinenumber >= 6){
    $("#md-edit-penandaan").children(".div-tags3").children(".bootstrap-tagsinput").append("<br/>");
    newlinenumber = 0;
    }

    newlinenumber ++;
    }
    }

    function extract_tags(){
    var arreditrams = [];
    var arreditirmsv3 = [];
    var arreditrenstra = [];
    var arrediteprog = [];
    var arreditdpr = [];
    var arreditsipro = [];
    var arreditpemda = [];
    var objedittag = {};
    var data_tag = $("#md-edit-penandaan .tag");
    var i = 0;

    data_tag.each(function() {

    //var data_text = $(this).text();
    var data_text = this.textContent;
    data_text = data_text.replace(/\s/g, '');

    if (data_text.split("|").length > 1){  //menghindari hasil split array([0,""])
    var tagkey = data_text.split("|")[0].trim();
    var tagvalue = data_text.split("|")[1].trim();
    var find = '\n';
    var re = new RegExp(find, 'g');
    tagvalue = tagvalue.replace(re, '');
    tagvalue = tagvalue.replace("X", "");
    var obj_rujukan = {};
    //var newdatatext=
    data_text = data_text.trim();
    data_text = data_text.replace(re, '');
    data_text = data_text.replace("X", "");
    if (tagkey == "RAMS" && tagvalue){
    arreditrams[i] = tagvalue;
    objedittag[tagkey] = Object.assign({}, arreditrams);
    obj_rujukan = {value:tagvalue, text:data_text, rujukan:"RAMS"};
    } else if (tagkey == 'IRMS' && tagvalue){
    arreditirmsv3[i] = tagvalue;
    objedittag[tagkey] = Object.assign({}, arreditirmsv3);
    obj_rujukan = {value:tagvalue, text:data_text, rujukan:"IRMS"};
    } else if (tagkey == 'RENSTRA' && tagvalue){
    arreditrenstra[i] = tagvalue;
    objedittag[tagkey] = arreditrenstra;
    objedittag[tagkey] = Object.assign({}, arreditrenstra);
    obj_rujukan = {value:tagvalue, text:data_text, rujukan:"RENSTRA"};
    } else if (tagkey == 'EPROG' && tagvalue){
    arrediteprog[i] = tagvalue;
    objedittag[tagkey] = arrediteprog;
    objedittag[tagkey] = Object.assign({}, arrediteprog);
    obj_rujukan = {value:tagvalue, text:data_text, rujukan:"EPROG"};
    } else if (tagkey == 'PEMDA' && tagvalue){
    arreditpemda[i] = tagvalue;
    objedittag[tagkey] = arreditpemda;
    objedittag[tagkey] = Object.assign({}, arreditpemda);
    obj_rujukan = {value:tagvalue, text:data_text, rujukan:"PEMDA"};
    } else if (tagkey == 'SIPRO' && tagvalue){
    arreditsipro[i] = tagvalue;
    objedittag[tagkey] = arreditsipro;
    objedittag[tagkey] = Object.assign({}, arreditsipro);
    obj_rujukan = {value:tagvalue, text:data_text, rujukan:"SIPR0"};
    }
    else if (tagkey == 'DPR' && tagvalue){

    arreditdpr[i] = tagvalue;
    var find = ' ';
    var re = new RegExp(find, 'g');
    tagvalue = tagvalue.replace(re, '');
    objedittag[tagkey] = objedittag[tagkey] = Object.assign({}, arreditdpr);
    obj_rujukan = {value:tagvalue, text:data_text, rujukan:"DPR"};
    }


    i++;
    rujukan_all.push(obj_rujukan);
    }

    });

    return objedittag;
    }


    function wait_sec(){

    var n = $('.c').attr('id');
    var c = n;
    $('.c').text(c);
    setInterval(function(){
    c--;
    if (c >= 0){
    $('.c').text(c);
    }
    if (c == 0){
    $('.c').text(n);
    $("#alert-tunggu").hide();
    $(".content-sumber-dana").show();
    }
    }, 1000);
    }


    function hitung2() {
    var a = $(".a2").val();
    var b = $(".b2").val();
    c = a * b; //a kali b
    $(".c2").val(c);
    }

   function setSumberDana(){
     $(".content-sumber-dana").hide();

    //$(".dropdown-menu.dropdown-wkdsdana").removeAttr('style');
    //$("#alert-tunggu").show();



    var objsumberdana=JSON.parse($("#objsumberdana").val());
    wait_sec();
    var html_select=$("#htmldropdownsdana").val();
    const keys = Object.keys(objsumberdana);

    console.log(keys);

    console.log("nilai saat set sumberdana");
    console.log(keys.length);
    if(keys.length > 1){
    //alert(1);
    $(".mdl-edit-detail").empty();
    var i=0;
    for (const key of keys) {
        var btnlabelclass='';
        if(key=='pln'){
            btnlabelclass='primary';
        }else if(key=='rmp'){
            btnlabelclass='success'
        }else if(key=='pnpb'){
            btnlabelclass='danger'
        }else if(key=='blu'){
            btnlabelclass='warning'
        }else if(key=='sbsn'){
            btnlabelclass='info'
        }else if(key== 'opr'){
            btnlabelclass= 'dark'
        }else if(key== 'pdp'){
            btnlabelclass= 'secondary'
        }else if(key== 'pdn'){
            btnlabelclass= 'outline'
        }else if(key== 'rpm'){
            btnlabelclass= 'default'
        }


        var sdalabel=key.toUpperCase();
        var sdavalue=objsumberdana[key];
        sdavalue=sdavalue.split(".")[0];

        var x= Object.entries(objsumberdana);
        var buttonactionclass='';
        var buttonactionicon='-';

         if(i != keys.length-1){

            buttonactionclass="danger btn-remove-sda";
            buttonactionicon ="-";
         }else{
            buttonactionclass="success btn-add-sda";
            buttonactionicon ="+";
         }
         if(sdalabel.toLowerCase()=='rm')
         {
             var lab='wrm';
         }else
         {
             var lab=sdalabel.toLowerCase()+'3';

         }
         var l="'"+lab+"','edit'";
          var html_container=['<div class="form-group multiple-form-group input-group input-group-edit-detail">',
                                '<div class="input-group-btn input-group_select">',
                                    '<button data-toggle="dropdown" type="button" class="btn btn-'+btnlabelclass+' dropdown-toggle" data-toggle="dropdown">',
                                        '<span class="concept">'+sdalabel+'</span> <span class="caret"></span>',
                                    '</button>',
                                    '<ul class="dropdown-menu" role="menu" id="wkdsdana">',
                                        html_select,
                                    '</ul>',
                                    '</div>',
                                    '<input value="'+numberWithCommas(sdavalue)+'" id="'+lab+'" name="wrm" class="form-control valuesdanas" type="text" onkeyup="hitung('+l+')">',
                                    '<span class="input-group-btn">',
                                       '<button type="button" class="btn btn-'+buttonactionclass+'">'+buttonactionicon+'</button>',
                                    '</span>',
                                '</div>',
                           ].join('\n');

                         //binding dynamic event 1
                         //element yang di generate secara dinamis tidak bisa di binding event secara global tetapi harus seketika ketika element itu di create
                        $(".mdl-edit-detail").append(html_container);
//                        if($("#w"+sdalabel.toLowerCase()).attr("id")=="wrm"){
//                            $("#wrm").attr("readonly",true);
//                        }

                        $(".mdl-edit-detail .valuesdana").each(function( index ) {

                            this.addEventListener('keyup',function(e){
                             var rm_value = $('#wrm').val();
                             console.log("nilai valuesdana jquery on keyup");
                             //var current_value=this.value;
                             if(this.value != ""){//prevent string regexp still work event value null
//                                var rm_value= $("#wrm").val();
//
//                                //this.value=numberWithCommasSDA(this.value);
//
//                                var bulatrm = parseInt(rm_value.replace(/,/g, ''));
//
//                                var bulatcurr = parseInt(current_value.replace(/,/g, ''));
//
//                                 var result = bulatrm - bulatcurr;
//
//
//                                console.log(result);


                                //var result= parseInt(rm_value.replace(/,/g, ''))-parseInt(current_value.replace(/,/g, ''));
                                //result = Math.ceil(result);
                                //console.log(numberWithCommasSDA(result.toString());

                                //$("#wrm").val(numberWithCommasSDA(result.toString()));

                                var current_value=this.value;
                                     this.value=numberWithCommasSDA(this.value);

                                 setTimeout(function(){

                                     var bulatrm = parseInt(rm_value.replace(/,/g, ''), 10);

                                     var bulatcurr = parseInt(current_value.replace(/,/g, ''));

                                     var result = bulatrm - bulatcurr;
                                     //console.log(result);
                                     $("#wrm").val(numberWithCommasSDA(result.toString()));
                                }, 3000);
                             }

                             if(this.id != "wrm"){
                                 if(parseInt(current_value.replace(/,/g, '') > $("#wrm").val())){
                                     alert("Nilai tidak boleh lebih dari sumberdana");
                                     this.value="";
                                 }
                             }

                            });

                            /*
                   this.addEventListener('change',function(e){
                     var sdaItem=$(":focus");

                     if($(".mdl-edit-detail").index(sdaItem) !=0){
                         var rm_value= $("#wrm").val();
                         var current_value=this.value;
                         var result= parseInt(rm_value.replace(/,/g, ''))-parseInt(current_value.replace(/,/g, ''));

                          result=Math.ceil(result);
                          console.log("Nilai RM MD tambah =>"+result);
                          $("#wrm").val(numberWithCommasSDA(result.toString()));

                     }

                });
                  //*/

                        });

           console.log(i);
           i++;

           }


    }else{

        console.log("nilai wkdsdana");
        //console.log($("#wkdsdana").val());
        console.log(typeof $("#wkdsdana").val())
        if($("#objsumberdana").val()=="{}" || $("#objsumberdana").val()==""){
        //alert(77777);
            //alert("block detected");
           //create element dropdown
           $(".mdl-edit-detail").empty();
                var default_sdana=[
                        '<div class="form-group multiple-form-group input-group input-group-edit-detail">',
                        '<div class="input-group-btn input-group_select input-edit-detail">',
                            '<button data-toggle="dropdown" type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">',
                                '<span class="concept">'+'RM'+'</span> <span class="caret"></span>',
                            '</button>',
                            '<ul class="dropdown-menu dropdown-wkdsdana" role="menu" id="wkdsdana">',
                                html_select,
                            '</ul>',
                            '<input type="hidden" class="input-group_select-val number"  value="RM">',
                        '</div>',
                        '<input type="text" id="wrm" name="wrm" class="form-control valuesdanas" onkeyup="hitung('+l+')">',
                            '<span class="input-group-btn">',
                                '<button type="button" class="btn btn-success btn-add">+</button>',
                            '</span>',
                        '</div>',
                ].join("\n");
                $(".mdl-edit-detail").append(default_sdana);
                //numberWithCommas()
                //alert("w jumlah vvvv")
                //alert(numberWithCommas($("#wjumlah").val().split(".")[0]));
                //$("#wrm").val(numberWithCommas($("#wjumlah").val().split(".")[0]));
                $("#wrm").val(0);

                //$("#wkdsdana").append("#htmldropdownsdana").va();
        }
         $("#wkdsdana").html(html_select);
         $("#wrm").val(numberWithCommas($("#wjumlah").val().split(".")[0]));
//         $(".btn-dropdown-edit-detail").click(function(){
//                //console.log(value_triger+" =>value triger");
//                   if(value_triger==1){
//                       //alert(5555);
//                       $("#wkdsdana").hide();
//                       //value_triger=value_triger - 1;
//                   }
//                   if(value_triger==0){
//                       $("#wkdsdana").css("display","block");
//                       $("#wkdsdana").show();
//                       // value_triger=value_triger + 1;
//                   }
//
//                   if(value_triger==0){
//                       value_triger=1;
//                   }else{
//                       value_triger=0;
//                   }
//                });
         $("#wrm").val(objsumberdana[keys[0]]);

        //$(".mdl-edit-detail")
    }

    if($("#objsumberdana").val()=="{}" || $("#objsumberdana").val() == ""){
        $("#wrm").val($("#wjumlah").val());
    }
    //alert($("#objsumberdana").val());
    if($("#objsumberdana").val()=="{}" || $("#objsumberdana").val()==""){

           $(".mdl-edit-detail").empty();
                var default_sdana=[
                        '<div class="form-group multiple-form-group input-group input-group-edit-detail">',
                        '<div class="input-group-btn input-group_select input-edit-detail">',
                            '<button data-toggle="dropdown" type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">',
                                '<span class="concept">'+'RM'+'</span> <span class="caret"></span>',
                            '</button>',
                            '<ul class="dropdown-menu dropdown-wkdsdana" role="menu" id="wkdsdana">',
                               html_select,
                            '</ul>',
                            '<input type="hidden" class="input-group_select-val number"  value="RM">',
                        '</div>',
                        '<input type="text" id="wrm" name="wrm" class="form-control valuesdanas" onkeyup="hitung('+l+')">',
                            '<span class="input-group-btn">',
                                '<button type="button" class="btn btn-success btn-add">+</button>',
                            '</span>',
                        '</div>',
                ].join("\n");
                $(".mdl-edit-detail").append(default_sdana);
                //numberWithCommas()
                //alert("w jumlah vvvv")
                //alert(numberWithCommas($("#wjumlah").val().split(".")[0]));
                //$("#wrm").val(numberWithCommas($("#wjumlah").val().split(".")[0]));
                $("#wrm").val(0);

                //$("#wkdsdana").append("#htmldropdownsdana").va();
        }else{
            //fix problem delay data
            if(JSON.parse($("#objsumberdana").val()).length ==1){
            $(".mdl-edit-detail").empty();
                var default_sdana=[
                        '<div class="form-group multiple-form-group input-group input-group-edit-detail">',
                        '<div class="input-group-btn input-group_select input-edit-detail">',
                            '<button data-toggle="dropdown" type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">',
                                '<span class="concept">'+'RM'+'</span> <span class="caret"></span>',
                            '</button>',
                            '<ul class="dropdown-menu dropdown-wkdsdana" role="menu" id="wkdsdana">',
                                 html_select,
                            '</ul>',
                            '<input type="hidden" class="input-group_select-val number"  value="RM">',
                        '</div>',
                        '<input type="text" id="wrm" name="wrm" class="form-control valuesdanas " onkeyup="hitung('+l+')">',
                            '<span class="input-group-btn">',
                                '<button type="button" class="btn btn-success btn-add">+</button>',
                            '</span>',
                        '</div>',
                ].join("\n");
                $(".mdl-edit-detail").append(default_sdana);
                $("#wrm").val($("#wjumlah").val());
                }
            //alert("pppp");
        }
     var objsdana=JSON.parse($("#objsumberdana").val());
     if(Object.keys(objsdana).length==1){
              $(".mdl-edit-detail").empty();
                var default_sdana=[
                        '<div class="form-group multiple-form-group input-group input-group-edit-detail">',
                        '<div class="input-group-btn input-group_select input-edit-detail">',
                            '<button data-toggle="dropdown" type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">',
                                '<span class="concept">'+'RM'+'</span> <span class="caret"></span>',
                            '</button>',
                            '<ul class="dropdown-menu dropdown-wkdsdana" role="menu" id="wkdsdana">',
                                 html_select,
                            '</ul>',
                            '<input type="hidden" class="input-group_select-val number"  value="RM">',
                        '</div>',
                        '<input type="text" id="wrm" name="wrm" class="form-control valuesdanas" onkeyup="hitung('+l+')">',
                            '<span class="input-group-btn">',
                                '<button type="button" class="btn btn-success btn-add">+</button>',
                            '</span>',
                        '</div>',
                ].join("\n");
                $(".mdl-edit-detail").append(default_sdana);
                $("#wrm").val($("#wjumlah").val());
        }
}


 $('input.number').keyup(function(event) {
   //console.log(this.id + "xxxcccc");
  // skip for arrow keys
  if(event.which >= 37 && event.which <= 40) return;

            $(this).val(function(index, value) {
              //inisiasi perhitungan jumlah,hargasatuan dan RPM

              //modal edit detail
              var wvolume  =$("#wvolume").val();
              var whargasat=$("#whargasat").val().replace(/\D/g, '');

              if(this.id=="whargasat" || this.id =="wvolume"){
                   var whasil=parseFloat(whargasat) * parseFloat(wvolume);
                   var wwwhasil=whasil.toFixed(0)
                   var wwhasil = Math.round(wwwhasil / 1000) * 1000;
                   $("#wjumlah").val(wwhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                   $("#wrm").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                   //$("#wrm").val("");
                   //new code
                  $("#wTotal").val(wwhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
              }

              if(this.id == "wjumlah"){
                  //console.log("xxxx w jumlah xxxxx")
                  var wjumlah=$("#wjumlah").val().replace(/\D/g, '');
                  var whasil=parseFloat(wjumlah) / parseFloat(wvolume);
                  $("#whargasat").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                  $("#wrm").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                 // $(".mdl-edit-detail .valuesdana:not(:eq(0))").val("");
              }



              //modal tambah detail

              var yhargasat=$("#yhargasat").val().replace(/\D/g, '');
              var volume  =$("#volume").val();
              //var volume  =$("#volume").val().replace(/\D/g, '');


              if(this.id == "volume" || this.id== "yhargasat"){

                  var yhasil=parseFloat(yhargasat) * parseFloat(volume);
                  var yyyhasil=yhasil.toFixed(0)
                  var yyhasil = Math.round(yyyhasil / 1000) * 1000;
                  //$("#yjumlah").val(yhasil);
                  $("#yjumlah").val(yyhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                  $("#yrm").val(yhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
//                  $(".ctk-tb-detail .valuesdanas:not(:eq(0))").val("");
                  //new code
                  $("#yTotal").val(yyhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
              }

              if(this.id == "yjumlah"){
                  //console.log("xxxx w jumlah xxxxx")
                  var yjumlah=$("#yjumlah").val().replace(/\D/g, '');
                  var yhasil=parseFloat(yjumlah) / parseFloat(volume);
                  $("#yhargasat").val(yhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                  $("#yrm").val(yhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
//                  $(".ctk-tb-detail .valuesdanas:not(:eq(0))").val("");
                  $("#yrm").val($("#yjumlah").val());
              }


              //modal tambah paket

              var xhargasat=$("#xhargasat").val().replace(/\D/g, '');
              var xvolume  =$("#xvolume").val();


              if(this.id == "xvolume" || this.id== "xhargasat"){

                  var xhasil=parseFloat(xhargasat) * parseFloat(xvolume);
                  var xxxhasil=xhasil.toFixed(0)
                  var xxhasil = Math.round(xxxhasil / 1000) * 1000;
                  //console.log(xhasil);
                  $("#xjumlah").val(xxhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                  $("#xrm").val(xhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                  $("").val("");
                  //new code
                  $("#xTotal").val(xxhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
              }

              if(this.id == "xjumlah"){

                  var xjumlah=$("#xjumlah").val().replace(/\D/g, '');
                  var xhasil=parseFloat(xjumlah) / parseFloat(volume);
                  $(".mdl-tbh-paket .xhargasat").val(xhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
                  $("#xrm").val(xhasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
//                  $(".mdl-tbh-paket .valuesdanas:not(:eq(0))").val("");
              }


                        return value
                        .replace(/\D/g, "")
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");


         });

 });

    $('input.vol').keyup(function(event) {
    //modal edit detail
    if (this.id == 'wvolume'){
    var wvolume = $("#wvolume").val();
    var whargasat = $("#whargasat").val().replace(/\D/g, '');
    var whasil = parseFloat(whargasat) * parseFloat(wvolume);
    $("#wjumlah").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    $(".mdl-edit-detail .valuesdana:eq(0)").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    $(".mdl-edit-detail .valuesdana:not(:eq(0))").val("");
    //new code
    $('#wTotal').val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    }

    //modal tambah detail
    if (this.id == 'xvolume'){
    var wvolume = $("#xvolume").val();
    var whargasat = $("#xhargasat").val().replace(/\D/g, '');
    var whasil = parseFloat(whargasat) * parseFloat(wvolume);
    $("#xjumlah").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    $(".mdl-tbh-paket .valuesdana:eq(0)").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    $(".mdl-tbh-paket .valuesdana:not(:eq(0))").val("");
    //new code
    $('#xTotal').val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    }

    //modal edit detail
    var wvolume = $("#volume").val();
    var whargasat = $("#yhargasat").val().replace(/\D/g, '');
    var whasil = parseFloat(whargasat) * parseFloat(wvolume);
    $("#yjumlah").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    $(".ctk-tb-detail .valuesdana:eq(0)").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    $(".ctk-tb-detail .valuesdana:not(:eq(0))").val("");
    //new code
    $("#yTotal").val(whasil.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));
    });
    var arr_wsumberdana = {};

    function multipleSelects(divname, refindex) {
      $("#kdsdana li").empty()
    url = "lookup/fieldlook/" + refindex;
    $.get(url).done(function (data) {
    jdata = JSON.parse(data);

    var htmlsumberdana = "";
    $.each(jdata, function (i, el) {
    $('#' + divname).append("<li><a href='#" + el.id + "'>" + el.val + "</a></li>");
    htmlsumberdana += "<li><a href='#" + el.id + "'>" + el.val + "</a></li>";
    });
    $("#htmldropdownsdana").val(htmlsumberdana);
    })
            .fail(function () {
            alert("error");
            })
            .always(function () {
            });
    }

    function numberWithCommasSDA(x){
    return x.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    $('#modal-detail').on('hidden.bs.modal', function () {
    // do somethingâ€¦
    $('.div-tags2').tagsinput('removeAll');
    });

            $('#modal-tambah').on('hidden.bs.modal', function () {
    // do somethingâ€¦
    $('.div-tags').tagsinput('removeAll');
    });



    ///comboooo

function refreshComboboxstax(divname, selvalue, setval) {

    url = "bm_lrs/lrs_tools/lrs_route_len/"+selvalue;
    $("#loadings").fadeIn("slow");
    $("#loadingss").fadeIn("slow");
    $("#loadingsss").fadeIn("slow");
    $("#loadingssss").fadeIn("slow");
    $.get(url).done(function (data) {
        console.log(data);

        jdata = JSON.parse(data);
        console.log(jdata);

         url2 = "exercise_wp/get_irmsv3_sta/"+selvalue+"/"+jdata.length;

        $.get(url2).done(function (data2) {
                jdata2 = JSON.parse(data2);
                     //*
                $('#' + divname).empty();
                $('#' + divname).append(new Option("--Pilih--", "#"));


                $.each(jdata2, function (i, el) {
                    $('#' + divname).append(new Option(el.val, el.id));

                });
               // if (setval) $('#'+divname).val(setval);
                if (setval !=0) {
                    $('#'+divname).val(setval)
                }else{
                    $('#'+divname).val(0)
                }
//                $("#loadings").show();
             $("#loadings").fadeOut("slow");
             $("#loadingss").fadeOut("slow");
             $("#loadingsss").fadeOut("slow");
             $("#loadingssss").fadeOut("slow");
                 $("#sta_awal").prop('disabled',false);
     $("#sta_akhir").prop('disabled',false);
        //*/

        }).fail(function () {
                alert("error");
            })
            .always(function () {
                // alert("finished");
            });





    })
            .fail(function () {
                alert("error");
            })
            .always(function () {
                // alert("finished");
            });
}

//new code
function ChangeSumberList(el, prefix) {
    console.log(el);
    console.log(prefix);
    var selectKppn = null;
    if (el === null){
        selectKppn = $('#wkdkppn').val();
    } else {
        selectKppn = el.value;
    }
    var sumberList = document.getElementById(""+prefix+"sumber");
    if(selectKppn === '140' || selectKppn === '999'){
        //$(""+prefix+"sumber option[value='B_1_1_']").removeAttr("disabled");
        //$(""+prefix+"sumber option[value='B_1_2_']").removeAttr("disabled");
        //$(""+prefix+"sumber option[value='B_1_4_']").removeAttr("disabled");
        //$(""+prefix+"sumber option[value='E_1_2_']").removeAttr("disabled");
        //$(""+prefix+"sumber option[value='I_3_1_']").removeAttr("disabled");
        //$(""+prefix+"sumber option[value='I_3_2_']").removeAttr("disabled");
        //$(""+prefix+"sumber option[value='I_3_4_']").removeAttr("disabled");
        //$(""+prefix+"sumber option[value='K_0_1_']").removeAttr("disabled");
        //sumberList[2].disabled = false;
//        sumberList[3].disabled = false;
//        sumberList[5].disabled = false;
//        sumberList[10].disabled = false;
//        sumberList[15].disabled = false;
//        sumberList[16].disabled = false;
//        sumberList[18].disabled = false;
//        sumberList[22].disabled = false;
    }
    else{
        //$(""+prefix+"sumber option[value='B_1_1_']").attr("disabled", "disabled");
        //$(""+prefix+"sumber option[value='B_1_2_']").attr("disabled", "disabled");
        //$(""+prefix+"sumber option[value='B_1_4_']").attr("disabled", "disabled");
        //$(""+prefix+"sumber option[value='E_1_2_']").attr("disabled", "disabled");
        //$(""+prefix+"sumber option[value='I_3_1_']").attr("disabled", "disabled");
        //$(""+prefix+"sumber option[value='I_3_2_']").attr("disabled", "disabled");
        //$(""+prefix+"sumber option[value='I_3_4_']").attr("disabled", "disabled");
        //$(""+prefix+"sumber option[value='K_0_1_']").attr("disabled", "disabled");
        //sumberList[2].disabled = true;
//        sumberList[3].disabled = true;
//        sumberList[5].disabled = true;
//        sumberList[10].disabled = true;
//        sumberList[15].disabled = true;
//        sumberList[16].disabled = true;
//        sumberList[18].disabled = true;
//        sumberList[22].disabled = true;
    }
}

function handleAkun(el, prefix){
    //console.log(el.value);
    switch (prefix){
        case 'y':
            var idpaket = $('#yid_paket').val();
            if (idpaket === null || idpaket === ''){

            } else {
                var thang = $('#ythang_sel').val();
                var keg = $('#ykd_kegiatan_sel').val();
                var out = $('#ykd_output_sel').val();
                var sout = $('#ykd_sub_output_sel').val();
                var kom = $('#ykd_komponen_sel').val();
                var skom = $('#ykd_sub_komponen_sel').val();
                var akn = $('#ykdakun').val();
                var sumber = $('#ysumber').val();
                var beban = null;
                var jnsban = null;
                var ctarik = null;
                if (sumber === null || sumber === ''){
                    beban = null;
                    jnsban = null;
                    ctarik = null;
                } else {
                    beban = sumber.split('_')[0];
                    jnsban = sumber.split('_')[1];
                    ctarik = sumber.split('_')[2];
                }
                get_akun_update(idpaket, thang, user_satker, keg, out, sout, kom, skom, akn,
                    beban, jnsban, ctarik, 'y');
            }
            break;
        case 'w':
            //console.log('kesini');
            var idpaket = $('#wid_paket').val();
            if (idpaket === null || idpaket === ''){

            } else {
                var thang = $('#wthang').val();
                var keg = $('#wkd_kegiatan').val();
                var out = $('#wkd_output').val();
                var sout = $('#wkd_sub_output').val();
                var kom = $('#wkd_komponen').val();
                var skom = $('#wkd_sub_komponen').val();
                var akn = $('#wkdakun').val();
                var sumber = $('#wsumber').val();
                var beban = null;
                var jnsban = null;
                var ctarik = null;
                if (sumber === null || sumber === ''){
                    beban = null;
                    jnsban = null;
                    ctarik = null;
                } else {
                    beban = sumber.split('_')[0];
                    jnsban = sumber.split('_')[1];
                    ctarik = sumber.split('_')[2];
                }
                get_akun_update(idpaket, thang, user_satker, keg, out, sout, kom, skom, akn,
                    beban, jnsban, ctarik, 'w');
            }
            break;
    }
}

function handleSumber(el, prefix){
    var a = prefix.length;
    var sumber = '';
    if (a > 1){
        sumber = prefix.split('::')[0];
        prefix = prefix.split('::')[1];
    } else {
        sumber = el.value;
    }

    refreshComboboxKPPN(''+prefix+'inputKPPNph', 46, 'kdsatker', kd_satker);
    refreshComboboxKPPN(''+prefix+'inputKPPNrm', 46, 'kdsatker', kd_satker);
    refreshComboboxKPPN(''+prefix+'inputKPPNrp', 46, 'kdsatker', kd_satker);

    var sumbers= sumber.split('_')[3];
    var hitung = $("#"+prefix+"panelHitung :input");
    var cara_hitung = sumber.split('_')[1];
    //console.log(sumbers);
    if (cara_hitung === '0'){
        $("input[name='"+prefix+"caraHitung']").val([0]);
        //console.log(cara_hitung);
        //$("[name='kppn']").show();
        //$("[name='percent']").show();
        //$('#'+prefix+'text1').hide();
        $('#'+prefix+'inputPHLN').val(0);
        //$('#'+prefix+'inputPHLN2').val(null);
        $('#'+prefix+'inputKPPNph').val(null);
        $('#'+prefix+'inputRMPdp').val(0);
        //$('#'+prefix+'inputRMPdp2').val(null);
        $('#'+prefix+'inputKPPNrm').val(null);
        $('#'+prefix+'inputRPLNPdp').val(0);
        //$('#'+prefix+'inputRPLNPdp2').val(null);
        $('#'+prefix+'inputKPPNrp').val(null);
        $('select[name=' + prefix + 'register]').val('');
        $('select[name=' + prefix + 'registerpdp]').val('');
        //$('#'+prefix+'registerpdp').val(null);
        //$('#'+prefix+'register').val(null);
    }

    var aa = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            console.log(sumbers);
            switch(sumbers){
                case '1':
                    $('#'+prefix+'register').prop("disabled",true);
                    hitung.prop("disabled",true);
                    $('#'+prefix+'register').val('');
                    break;
                case '2':
                    $('#'+prefix+'register').prop("disabled",false);
                    initSelectCombobox(''+prefix+'register', 73);
                    hitung.prop("disabled",false);
                    $("input[name='"+prefix+"caraHitung']").val([2]);
                    setTimeout(function(){ handleRadioHitung(prefix); }, 1000);
                    break;
                case '3':
                    $('#'+prefix+'register').prop("disabled",false);
                    hitung.prop("disabled",true);
                    break;
            }
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
    var ab = function () {
        var defer = $.Deferred();
        setTimeout(function () {
            //new
            //console.log(el.value);
            switch(prefix){
                case 'y':
                    var idpaket = $('#yid_paket').val();
                    if (idpaket === null || idpaket === ''){

                    } else {
                        var thang = $('#ythang_sel').val();
                        var keg = $('#ykd_kegiatan_sel').val();
                        var out = $('#ykd_output_sel').val();
                        var sout = $('#ykd_sub_output_sel').val();
                        var kom = $('#ykd_komponen_sel').val();
                        var skom = $('#ykd_sub_komponen_sel').val();
                        var akn = $('#ykdakun').val();
                        var sumber = $('#ysumber').val();
                        var beban = sumber.split('_')[0];
                        var jnsban = sumber.split('_')[1];
                        var ctarik = sumber.split('_')[2];
                        //console.log(idpaket);
                        get_akun_update(idpaket, thang, user_satker, keg, out, sout, kom, skom, akn,
                            beban, jnsban, ctarik, 'y');
                    }
                    break;
                case 'w':
                    //console.log('kesini');
                    var idpaket = $('#wid_paket').val();
                    if (idpaket === null || idpaket === ''){

                    } else {
                        var thang = $('#wthang').val();
                        var keg = $('#wkd_kegiatan').val();
                        var out = $('#wkd_output').val();
                        var sout = $('#wkd_sub_output').val();
                        var kom = $('#wkd_komponen').val();
                        var skom = $('#wkd_sub_komponen').val();
                        var akn = $('#wkdakun').val();
                        var sumber = $('#wsumber').val();
                        var beban = sumber.split('_')[0];
                        var jnsban = sumber.split('_')[1];
                        var ctarik = sumber.split('_')[2];
                        get_akun_update(thang, user_satker, keg, out, sout, kom, skom, akn,
                            beban, jnsban, ctarik, 'w');
                    }
                    break;
            }
        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
        }, 1000);
        return defer;
        };
        aa().then(ab);
}

function handleRadioHitung(prefix){
    var kdkppn = null;
    if (prefix === 'x'){
        kdkppn = $("#kdkppn").val();
    } else {
        kdkppn = $("#"+prefix+"kdkppn").val();
    }
    //console.log(kdkppn);
    var radio = $("input[name='"+prefix+"caraHitung']:checked").val();
    switch(radio){
        case '0':
            //$("[name='kppn']").show();
            //$("[name='percent']").show();
            //$('#'+prefix+'text1').hide();
            //$('#'+prefix+'inputPHLN').show();
            //$('#'+prefix+'inputKPPNph').show();
            //$('#'+prefix+'inputRMPdp').show();
            //$('#'+prefix+'inputKPPNrm').show();
            //$('#'+prefix+'inputRPLNPdp').show();
            //$('#'+prefix+'inputKPPNrp').show();
            //$('#'+prefix+'inputPHLN2').hide();
            //$('#'+prefix+'inputRMPdp2').hide();
            //$('#'+prefix+'inputRPLNPdp2').hide();
            //$('#'+prefix+'totalkppn').hide();
            $('#'+prefix+'inputPHLN').prop("disabled",true);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrm').prop("disabled",true);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrp').prop("disabled",true);
            $('#'+prefix+'registerpdp').prop("disabled",true);
            $('#'+prefix+'inputPHLN').val(0);
            $('#'+prefix+'inputKPPNph').val(null);
            $('#'+prefix+'inputRMPdp').val(0);
            $('#'+prefix+'inputKPPNrm').val(null);
            $('#'+prefix+'inputRPLNPdp').val(0);
            $('#'+prefix+'inputKPPNrp').val(null);
            //$('#'+prefix+'inputPHLN2').val(null);
            //$('#'+prefix+'inputRMPdp2').val(null);
            //$('#'+prefix+'inputRPLNPdp2').val(null);
            //$('#'+prefix+'registerpdp').val(null);
            $('select[name=' + prefix + 'registerpdp]').val('');
            $('#'+prefix+'panelNonSharing').hide();
            break;
        case '1':
            //$("[name='kppn']").show();
            //$("[name='percent']").show();
            //$('#'+prefix+'text1').hide();
            //$('#'+prefix+'inputPHLN').show();
            //$('#'+prefix+'inputKPPNph').show();
            //$('#'+prefix+'inputRMPdp').show();
            //$('#'+prefix+'inputKPPNrm').show();
            //$('#'+prefix+'inputRPLNPdp').show();
            //$('#'+prefix+'inputKPPNrp').show();
            //$('#'+prefix+'inputPHLN2').hide();
            //$('#'+prefix+'inputRMPdp2').hide();
            //$('#'+prefix+'inputRPLNPdp2').hide();
            //$('#'+prefix+'totalkppn').hide();
            $('#'+prefix+'inputPHLN').prop("disabled",true);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrm').prop("disabled",true);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrp').prop("disabled",true);
            $('#'+prefix+'registerpdp').prop("disabled",true);
            $('#'+prefix+'inputPHLN').val(100);
            $('#'+prefix+'inputKPPNph').val(kdkppn);
            $('#'+prefix+'inputRMPdp').val(0);
            $('#'+prefix+'inputKPPNrm').val(null);
            $('#'+prefix+'inputRPLNPdp').val(0);
            $('#'+prefix+'inputKPPNrp').val(null);
            //$('#'+prefix+'inputPHLN2').val(null);
            //$('#'+prefix+'inputRMPdp2').val(null);
            //$('#'+prefix+'inputRPLNPdp2').val(null);
            //$('#'+prefix+'registerpdp').val(null);
            $('select[name=' + prefix + 'registerpdp]').val('');
            $('#'+prefix+'panelNonSharing').hide();
            break;
        case '2':
            //$("[name='kppn']").show();
            //$("[name='percent']").show();
            //$('#'+prefix+'text1').hide();
            //$('#'+prefix+'inputPHLN').show();
            //$('#'+prefix+'inputPHLN').show();
            //$('#'+prefix+'inputKPPNph').show();
            //$('#'+prefix+'inputRMPdp').show();
            //$('#'+prefix+'inputKPPNrm').show();
            //$('#'+prefix+'inputRPLNPdp').show();
            //$('#'+prefix+'inputKPPNrp').show();
            //$('#'+prefix+'inputPHLN2').hide();
            //$('#'+prefix+'inputRMPdp2').hide();
            //$('#'+prefix+'inputRPLNPdp2').hide();
            //$('#'+prefix+'totalkppn').hide();
            $('#'+prefix+'inputPHLN').prop("disabled",false);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",false);
            $('#'+prefix+'inputKPPNrm').prop("disabled",false);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",false);
            $('#'+prefix+'inputKPPNrp').prop("disabled",false);
            $('#'+prefix+'registerpdp').prop("disabled",true);
            $('#'+prefix+'inputPHLN').val(100);
            $('#'+prefix+'inputKPPNph').val(kdkppn);
            $('#'+prefix+'inputRMPdp').val(0);
            $('#'+prefix+'inputKPPNrm').val(null);
            $('#'+prefix+'inputRPLNPdp').val(0);
            $('#'+prefix+'inputKPPNrp').val(null);
            $('#'+prefix+'inputPHLN2').val(null);
            $('#'+prefix+'inputRMPdp2').val(null);
            $('#'+prefix+'inputRPLNPdp2').val(null);
            //$('#'+prefix+'registerpdp').val(null);
            $('select[name=' + prefix + 'registerpdp]').val('');
            $('#'+prefix+'panelNonSharing').hide();
            break;
        case '4':
//            $("[name='kppn']").show();
//            $("[name='percent']").hide();
//            $('#'+prefix+'text1').show();
//            $('#'+prefix+'inputPHLN').hide();
//            $('#'+prefix+'inputKPPNph').show();
//            $('#'+prefix+'inputRMPdp').hide();
//            $('#'+prefix+'inputKPPNrm').show();
//            $('#'+prefix+'inputRPLNPdp').hide();
//            $('#'+prefix+'inputKPPNrp').show();
//            $('#'+prefix+'inputPHLN2').show();
//            $('#'+prefix+'inputRMPdp2').show();
//            $('#'+prefix+'inputRPLNPdp2').show();
//            $('#'+prefix+'inputRPLNPdp2').prop("disabled",true);
//            $('#'+prefix+'totalkppn').show();
            //$('#'+prefix+'Total').prop("disabled",true);
            $('#'+prefix+'inputPHLN').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",true);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",true);
            $('#'+prefix+'registerpdp').prop("disabled",false);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputKPPNrm').prop("disabled",false);
            $('#'+prefix+'inputKPPNrp').prop("disabled",false);
            $('#'+prefix+'registerpdp').val(null);
            $('#'+prefix+'inputPHLN').val(0);
            $('#'+prefix+'inputKPPNph').val(kdkppn);
            $('#'+prefix+'inputRMPdp').val(0);
            $('#'+prefix+'inputKPPNrm').val(null);
            $('#'+prefix+'inputRPLNPdp').val(0);
            $('#'+prefix+'inputKPPNrp').val(null);
            $('#'+prefix+'panelNonSharing').show();
            $('#'+prefix+'inputNSPHLN2').prop("disabled",false);
            $('#'+prefix+'inputNSRMPdp2').prop("disabled",false);
            $('#'+prefix+'inputNSRPLNPdp2').prop("disabled",true);
            initSelectCombobox(''+prefix+'registerpdp', 73);
            break;
    }
}


function handleregister(dat){
    initComboboxSearch('xregister', 73,dat.value);
    initComboboxSearch('yregister', 73,dat.value);
    initComboboxSearch('wregister', 73,dat.value);
  // alert(dat.value)
}
function handleregisterpdp(dat){
    initComboboxSearch2('xregisterpdp', 73,dat.value);
    initComboboxSearch2('yregisterpdp', 73,dat.value);
    initComboboxSearch('wregister', 73,dat.value);
  // alert(dat.value)
}

function hitung(prefix){
    var phln = $("#"+prefix+"inputPHLN").val();
    var rmpdp = $("#"+prefix+"inputRMPdp").val();
    var rpln = $("#"+prefix+"inputRPLNPdp").val();
    var total = parseInt(phln) + parseInt(rmpdp) + parseInt(rpln);
    if (total > '100'){
        console.log(total);
        $("#"+prefix+"inputPHLN").val(100);
        $("#"+prefix+"inputRMPdp").val(0);
        $("#"+prefix+"inputRPLNPdp").val(0);
        $("#"+prefix+"inputKPPNrm").val('');
        $("#"+prefix+"inputKPPNrp").val('');
    }
}

function handleRadioVolume(prefix){
    var radio = $("input[name='"+prefix+"radioVolume']:checked").val();
    //console.log(radio);
    switch(radio){
        case '1':
            $('#'+prefix+'radioRuas').show();
            $('.jembatans').hide();
            $('.staw').show();
            $('#'+prefix+'radioHonor').hide();
            $('.longs').show();
            $('.longsa').show();
            $('#'+prefix+'inputVol1').val(null);
            $('#'+prefix+'inputVol2').val(null);
            $('#'+prefix+'inputVol3').val(null);
            $('#'+prefix+'inputVol4').val(null);
            $('#'+prefix+'inputSat1').val(null);
            $('#'+prefix+'inputSat2').val(null);
            $('#'+prefix+'inputSat3').val(null);
            $('#'+prefix+'inputSat4').val(null);
            //$('#'+prefix+'detail').val(null);
            $('#id_ruas').val('#');
            //$('#nm_ruas').val('#');
            $('#sta_awal_sel').val('#');
            //$('#sta_awal').val('#');
            $('#sta_akhir_sel').val('#');
            //$('#sta_akhir').val('#');
            $('#'+prefix+'id_jembatan').val('#');
            //$('#nm_jembatan').val('#');
            $('#'+prefix+'longitude').val(null);
            $('#'+prefix+'latitude').val(null);
            $('#'+prefix+'longitude2').val(null);
            $('#'+prefix+'latitude2').val(null);
            $('#'+prefix+'id_ruas').val('#');
            $('#sta_awal').val('#');
            $('#sta_akhir').val('#');
            $(".ruasjalan").removeClass('col-md-12');
            $(".ruasjalan").addClass('col-md-6');
            break;
        case '2':
            $('#'+prefix+'maxRuas').text('');
            //$('#'+prefix+'textMaxx').text('');
            $('#'+prefix+'radioRuas').show();
            $('.jembatans').show();
            $('.staw').hide();
            $('#'+prefix+'radioHonor').hide();
            $('.longs').show();
            $('.longsa').hide();
            $('#'+prefix+'inputVol1').val(null);
            $('#'+prefix+'inputVol2').val(null);
            $('#'+prefix+'inputVol3').val(null);
            $('#'+prefix+'inputVol4').val(null);
            $('#'+prefix+'inputSat1').val(null);
            $('#'+prefix+'inputSat2').val(null);
            $('#'+prefix+'inputSat3').val(null);
            $('#'+prefix+'inputSat4').val(null);
            //$('#'+prefix+'detail').val(null);
            $('#id_ruas').val(null);
            //$('#nm_ruas').val('#');
            $('#sta_awal_sel').val('#');
            //$('#sta_awal').val('#');
            $('#sta_akhir_sel').val('#');
            //$('#sta_akhir').val('#');
            $('#'+prefix+'id_jembatan').val('#');
            //$('#nm_jembatan').val('#');
            $('#'+prefix+'longitude').val(null);
            $('#'+prefix+'latitude').val(null);
            $('#'+prefix+'longitude2').val(null);
            $('#'+prefix+'latitude2').val(null);
            $('#'+prefix+'id_ruas').val('#');
            $('#sta_awal').val('#');
            $('#sta_akhir').val('#');
            $(".ruasjalan").removeClass('col-md-6');
            $(".ruasjalan").addClass('col-md-12');
            break;
        case '3':
            $('#'+prefix+'radioRuas').hide();
            $('#'+prefix+'radioHonor').show();
            $('#'+prefix+'inputVol1').val(null);
            $('#'+prefix+'inputVol2').val(null);
            $('#'+prefix+'inputVol3').val(null);
            $('#'+prefix+'inputVol4').val(null);
            $('#'+prefix+'inputSat1').val(null);
            $('#'+prefix+'inputSat2').val(null);
            $('#'+prefix+'inputSat3').val(null);
            $('#'+prefix+'inputSat4').val(null);
            //$('#'+prefix+'detail').val(null);
            $('#id_ruas').val(null);
            //$('#nm_ruas').val('#');
            $('#sta_awal_sel').val('#');
            //$('#sta_awal').val('#');
            $('#sta_akhir_sel').val('#');
            //$('#sta_akhir').val('#');
            $('#'+prefix+'id_jembatan').val('#');
            //$('#nm_jembatan').val('#');
            $('#'+prefix+'longitude').val(null);
            $('#'+prefix+'latitude').val(null);
            $('#'+prefix+'longitude2').val(null);
            $('#'+prefix+'latitude2').val(null);
            $('#'+prefix+'id_ruas').val('#');
            $('#sta_awal').val('#');
            $('#sta_akhir').val('#');
            break;
    }
}

function modalSBM(prefix){
    $('#modalSBM').modal('show');
    dataTableSBM(prefix);
}

function dataTableSBM(prefix) {
    tlist_sbm = $("#tlist_sbm").DataTable({
            "destroy":true,
            "scrollY":true,
            //"scrollX":true,
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "deferRender": true,
            "ajax": {
            url: "<?php echo base_url(); ?>pagu_tahapan/ssp_sbm",
                    type: "POST",
                    "iTotalRecords":  8500,
                    "iTotalDisplayRecords": 5,
                    "data": function (d) {
                    d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    }

            },
            "aoColumnDefs": [
            {
            "aTargets": [0],
                    "mRender": function (data, type, full) {
                            return full[0];
                    }
            },            {
            "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
            },            {
            "aTargets": [2],
                    "mRender": function (data, type, full) {
                        return full[2];
                    }
            },            {
            "aTargets": [3],
                    "mRender": function (data, type, full) {
                        return full[3];
                    }
            },
            {
            "aTargets": [4],
                    "mRender": function (data, type, full) {
                        if (full[2] === '' && full[3] === '.0000'){
                            var html_button = '';
                        } else{
                            var biaya = full[3].split('.')[0];
                            var html_button = '<button onclick="dtHonor(' + "'" + full[0] + '_' + full[1] + '_' + full[2] + '_' + biaya + '_' + prefix + "'" + ')" class="btn btn-success btn-xs">Pilih</button>';
                        }
                        return html_button;
                    }
            }],
            "autoWidth": false,
            "columns": [
            {"width": "10px"},
            {"width": "300px"},
            {"width": "20px"},
            {"width": "50px", render:$.fn.dataTable.render.number('.','.',0,'')},
            {"width": "5px"}
            ],"order": [[0, "asc"]],
            "pagingType": "full_numbers",
            //"pageLength": -1,
            //"paging":false,
            "language": {
            "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "Memuat...",
                    "processing": "Memroses...",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                            "last": "<i class='fa fa-angle-double-right'></i>",
                            "next": "<i class='fa fa-angle-right'></i>",
                            "previous": "<i class='fa fa-angle-left'></i>"
                    },
                    "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                            "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
            }
    });
    $("#tlist_sbm").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
}

function dtHonor(el){
    console.log(el);
    var kdsbu = el.split('_')[0];
    var uraian = el.split('_')[1];
    var satuan = el.split('_')[2];
    var biaya = el.split('_')[3];
    var prefix = el.split('_')[4];
    $('#'+prefix+'kdsbu').val(kdsbu);
    $('#'+prefix+'detail').val(uraian);
    $('#'+prefix+'satuan').val(satuan);
    $('#'+prefix+'hargasat').val(addCommas(biaya));
    setTimeout(function(){
        $('#modalSBM').modal('hide');
    }, 1000);
}

function addCommas(nStr)
{
    nStr += '';
    x = nStr.split('.');
    x1 = x[0];
    x2 = x.length > 1 ? '.' + x[1] : '';
    var rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1)) {
        x1 = x1.replace(rgx, '$1' + ',' + '$2');
    }
    return x1 + x2;
}

function hitungvolume(prefix){
    var vol1 = $("#"+prefix+"inputVol1").val();
    var vol2 = $("#"+prefix+"inputVol2").val();
    var vol3 = $("#"+prefix+"inputVol3").val();
    var vol4 = $("#"+prefix+"inputVol4").val();
    var sat1 = $("#"+prefix+"inputSat1").val();
    var sat2 = $("#"+prefix+"inputSat2").val();
    var sat3 = $("#"+prefix+"inputSat3").val();
    var sat4 = $("#"+prefix+"inputSat4").val();
    var urai = $("#"+prefix+"detail").val();
    var uraian = urai.split(' [')[0];
    var vol = $("#volume").val();
    var volw = $("#wvolume").val();
    var volx = $("#xvolume").val();
    var hargasat = $("#"+prefix+"hargasat").val();
    var jmlh = hargasat.split(',').join('');

    if (urai !== ''){
        if (vol1 > 0 && sat1 !== ''){
            $("#"+prefix+"detail").val(uraian + ' [' + vol1 + 'x' + sat1 +']');
            $("#volume").val(parseInt(vol1));
            $("#yjumlah").val(addCommas(parseFloat(vol) * parseFloat(jmlh)));
            $("#"+prefix+"volume").val(parseInt(vol1));
            $("#wjumlah").val(addCommas(parseFloat(volw) * parseFloat(jmlh)));
            $("#xjumlah").val(addCommas(parseFloat(volx) * parseFloat(jmlh)));
                if (vol2 > 0 && sat2 !== ''){
                    $("#"+prefix+"detail").val(uraian + ' [' + vol1 + 'x' + sat1 + ' ' + vol2 + 'x' + sat2 + ']');
                    $("#volume").val(parseInt(vol1) * parseInt(vol2));
                    $("#yjumlah").val(addCommas(parseFloat(vol) * parseFloat(jmlh)));
                    $("#"+prefix+"volume").val(parseInt(vol1) * parseInt(vol2));
                    $("#wjumlah").val(addCommas(parseFloat(volw) * parseFloat(jmlh)));
                    $("#xjumlah").val(addCommas(parseFloat(volx) * parseFloat(jmlh)));
                        if (vol3 > 0 && sat3 !== ''){
                            $("#"+prefix+"detail").val(uraian + ' [' + vol1 + 'x' + sat1 + ' ' + vol2 + 'x' + sat2 + ' ' + vol3 + 'x' + sat3 + ']');
                            $("#volume").val(parseInt(vol1) * parseInt(vol2) * parseInt(vol3));
                            $("#"+prefix+"jumlah").val(addCommas(parseFloat(vol) * parseFloat(jmlh)));
                            $("#"+prefix+"volume").val(parseInt(vol1) * parseInt(vol2) * parseInt(vol3));
                            $("#wjumlah").val(addCommas(parseFloat(volw) * parseFloat(jmlh)));
                            $("#xjumlah").val(addCommas(parseFloat(volx) * parseFloat(jmlh)));
                                if (vol4 > 0 && sat4 !== ''){
                                    $("#"+prefix+"detail").val(uraian + ' [' + vol1 + 'x' + sat1 + ' ' + vol2 + 'x' + sat2 + ' ' + vol3 + 'x' + sat3 + ' ' + vol4 + 'x' + sat4 + ']');
                                    $("#volume").val(parseInt(vol1) * parseInt(vol2) * parseInt(vol3) * parseInt(vol4));
                                    $("#"+prefix+"jumlah").val(addCommas(parseFloat(vol) * parseFloat(jmlh)));
                                    $("#"+prefix+"volume").val(parseInt(vol1) * parseInt(vol2) * parseInt(vol3) * parseInt(vol4));
                                    $("#wjumlah").val(addCommas(parseFloat(volw) * parseFloat(jmlh)));
                                    $("#xjumlah").val(addCommas(parseFloat(volx) * parseFloat(jmlh)));
                                }
                        }
                }
            }else{
                $("#"+prefix+"detail").val(uraian);
                $("#volume").val(0);
                $("#"+prefix+"jumlah").val(addCommas(parseFloat(vol) * parseFloat(jmlh)));
                $("#"+prefix+"volume").val(0);
                $("#wjumlah").val(addCommas(parseFloat(volw) * parseFloat(jmlh)));
                $("#xjumlah").val(addCommas(parseFloat(volx) * parseFloat(jmlh)));
            }
    }
}

function hitungkppn(prefix){
    var kppns1 = $("#"+prefix+"inputNSPHLN2").val();
    var kppns2 = $("#"+prefix+"inputNSRMPdp2").val();
    var totals = $("#"+prefix+"Total").val();
    var kppn1 = kppns1 === null ? 0 : kppns1.split(',').join('');
    var kppn2 = kppns2 === null ? 0 : kppns2.split(',').join('');
    var total = totals.split(',').join('');
    var totalkppn = parseInt(kppn1) + parseInt(kppn2);
    console.log(totalkppn);
    if ( totalkppn > total || kppn1 > total || kppn2 > total){
        //alert('Dana PHLN > Dana Kegiatan');
        $('#'+prefix+'text1').text('Dana PHLN > Dana Kegiatan');
    } else if ( totalkppn < total){
        //alert('Dana PHLN Tidak Sama Dengan Dana Kegiatan');
        $('#'+prefix+'text1').text('Dana PHLN Tidak Sama Dengan Dana Kegiatan');
    } else {
        $('#'+prefix+'text1').text('');
    }
}

function handleRadioVolumeEdit(prefix){
    var radio = $("input[name='"+prefix+"radioVolume']:checked").val();
    //console.log(radio);
    switch(radio){
        case '1':
            $('#'+prefix+'radioRuas').show();
            $('.jembatans').hide();
            $('.staw').show();
            $('#'+prefix+'radioHonor').hide();
            $('.longs').show();
            $('.longsa').show();
            $(".ruasjalan").removeClass('col-md-12');
            $(".ruasjalan").addClass('col-md-6');
            break;
        case '2':
            $('#'+prefix+'maxRuas').text('');
            //$('#'+prefix+'textMaxx').text('');
            $('#'+prefix+'radioRuas').show();
            $('.jembatans').show();
            $('.staw').hide();
            $('#'+prefix+'radioHonor').hide();
            $('.longs').show();
            $('.longsa').hide();
            $(".ruasjalan").removeClass('col-md-6');
            $(".ruasjalan").addClass('col-md-12');
            break;
        case '3':
            $('#'+prefix+'radioRuas').hide();
            $('#'+prefix+'radioHonor').show();
            break;
    }
}

function handleRadioHitungEdit(prefix){
    var kdkppn = null;
    if (prefix === 'x'){
        kdkppn = $("#kdkppn").val();
    } else {
        kdkppn = $("#"+prefix+"kdkppn").val();
    }
    var radio = $("input[name='"+prefix+"caraHitung']:checked").val();
    switch(radio){
        case '0':
            $('#'+prefix+'inputPHLN').prop("disabled",true);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrm').prop("disabled",true);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrp').prop("disabled",true);
            $('#'+prefix+'registerpdp').prop("disabled",true);
            $('#'+prefix+'inputPHLN').val(0);
            $('#'+prefix+'inputKPPNph').val(null);
            $('#'+prefix+'inputRMPdp').val(0);
            $('#'+prefix+'inputKPPNrm').val(null);
            $('#'+prefix+'inputRPLNPdp').val(0);
            $('#'+prefix+'inputKPPNrp').val(null);
            $('#'+prefix+'registerpdp').val(null);
            $('#'+prefix+'panelNonSharing').hide();
            break;
        case '1':
            $('#'+prefix+'inputPHLN').prop("disabled",true);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrm').prop("disabled",true);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",true);
            $('#'+prefix+'inputKPPNrp').prop("disabled",true);
            $('#'+prefix+'registerpdp').prop("disabled",true);
            $('#'+prefix+'inputPHLN').val(100);
            $('#'+prefix+'inputKPPNph').val(kdkppn);
            $('#'+prefix+'inputRMPdp').val(0);
            $('#'+prefix+'inputKPPNrm').val(null);
            $('#'+prefix+'inputRPLNPdp').val(0);
            $('#'+prefix+'inputKPPNrp').val(null);
            //$('#'+prefix+'registerpdp').val(null);
            $('select[name=' + prefix + 'registerpdp]').val('');
            $('#'+prefix+'panelNonSharing').hide();
            break;
        case '2':
            $('#'+prefix+'inputPHLN').prop("disabled",false);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",false);
            $('#'+prefix+'inputKPPNrm').prop("disabled",false);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",false);
            $('#'+prefix+'inputKPPNrp').prop("disabled",false);
            $('#'+prefix+'registerpdp').prop("disabled",true);
            $('#'+prefix+'inputPHLN').val(100);
            $('#'+prefix+'inputKPPNph').val(kdkppn);
            //$('#'+prefix+'inputRMPdp').val(0);
            //$('#'+prefix+'inputKPPNrm').val(null);
            //$('#'+prefix+'inputRPLNPdp').val(0);
            //$('#'+prefix+'inputKPPNrp').val(null);
            //$('#'+prefix+'inputPHLN2').val(null);
            //$('#'+prefix+'inputRMPdp2').val(null);
            //$('#'+prefix+'inputRPLNPdp2').val(null);
            //$('#'+prefix+'registerpdp').val(null);
            $('select[name=' + prefix + 'registerpdp]').val('');
            $('#'+prefix+'panelNonSharing').hide();
            break;
        case '4':
            $('#'+prefix+'inputPHLN').prop("disabled",true);
            $('#'+prefix+'inputRMPdp').prop("disabled",true);
            $('#'+prefix+'inputRPLNPdp').prop("disabled",true);
            $('#'+prefix+'registerpdp').prop("disabled",false);
            $('#'+prefix+'inputKPPNph').prop("disabled",true);
            $('#'+prefix+'inputKPPNrm').prop("disabled",false);
            $('#'+prefix+'inputKPPNrp').prop("disabled",false);
            //$('#'+prefix+'registerpdp').val(null);
            $('#'+prefix+'inputPHLN').val(0);
            $('#'+prefix+'inputKPPNph').val(kdkppn);
            $('#'+prefix+'inputRMPdp').val(0);
            //$('#'+prefix+'inputKPPNrm').val(null);
            $('#'+prefix+'inputRPLNPdp').val(0);
            //$('#'+prefix+'inputKPPNrp').val(null);
            $('#'+prefix+'panelNonSharing').show();
            $('#'+prefix+'inputNSPHLN2').prop("disabled",false);
            $('#'+prefix+'inputNSRMPdp2').prop("disabled",false);
            $('#'+prefix+'inputNSRPLNPdp2').prop("disabled",true);
            //initComboboxSearch2('xregisterpdp', 73);
            break;
    }
}

function get_akun_all(id_usulan) {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_akun_all') ?>" + "/" + id_usulan,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            x = data;
            console.log(x);
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
}

function get_akun_update(thang, kdsatker, kdgiat, kdoutput, kdsoutput, kdkmpnen, kdskmpnen, kdakun, kdbeban, kdjnsban, kdctarik, prefix) {
    var x = null;
    var obj_data_akun = {
        "thang": thang,
        "kdsatker": kdsatker,
        "kdgiat": kdgiat,
        "kdoutput": kdoutput,
        "kdsoutput": kdsoutput,
        "kdkmpnen": kdkmpnen,
        "kdskmpnen": kdskmpnen,
        "kdakun": kdakun,
        "kdbeban": kdbeban,
        "kdjnsban": kdjnsban,
        "kdctarik": kdctarik
    };
    var url = "<?php echo base_url("pagu_tahapan/get_akun_update"); ?>";
    var params = {"data": obj_data_akun, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
        .done(function (data) {
            //x = data;
            console.log(data);
            if (data === '0' ){
                $('#'+prefix+'inputHal4').val('');
                $('#'+prefix+'inputDipa').val('');
            } else {
                x = JSON.parse(data);
                refreshComboboxKPPN(''+prefix+'inputKPPNph', 46, 'kdsatker', kd_satker, x.kppnphln);
                refreshComboboxKPPN(''+prefix+'inputKPPNrm', 46, 'kdsatker', kd_satker, x.kppnrmp);
                refreshComboboxKPPN(''+prefix+'inputKPPNrp', 46, 'kdsatker', kd_satker, x.kppnrkp);
                $('#'+prefix+'inputPHLN').val(x.prosenphln);
                $('#'+prefix+'inputKPPNph').val(x.kppnphln);
                $('#'+prefix+'inputRMPdp').val(x.prosenrmp);
                $('#'+prefix+'inputKPPNrm').val(x.kppnrmp);
                $('#'+prefix+'inputRPLNPdp').val(x.prosenrkp);
                $('#'+prefix+'inputKPPNrp').val(x.kppnrkp);
                $('#'+prefix+'inputHal4').val(x.ket);
                $('#'+prefix+'inputDipa').val(x.ket2);
                if (x.carahitung === '4'){
                    if (x.regdam !== ''){
                        var regis = get_nmregister(x.regdam);
                        refreshSelectCombobox(''+prefix+'registerpdp', 73, x.regdam, regis.nmdonor);
                    } else {
                        refreshSelectCombobox(''+prefix+'registerpdp', 73, '', '');
                    }
                }
                else {
                    console.log('kosong');
                    refreshSelectCombobox(''+prefix+'registerpdp', 73, '', '');
                    //initSelectCombobox(''+prefix+'registerpdp', 73);
                }
                updateComboboxAndSelected(''+prefix+'kd_blokir', 76, x.kdblokir);
                $('#'+prefix+'urblokir').val(x.uraiblokir);
            }
        })
        .fail(function () {
            alert("error");
        });
    return x;
}

function updateComboboxAndSelectedKppn(divname, refindex, selvalue) {
    url = "lookup/fieldlook/" + refindex + "/" + selvalue;
    $.get(url).done(function (data) {
    var jdata = JSON.parse(data);
    $('#' + divname).empty();
    $('#' + divname).append("<option value='#' >" + "--Pilih--" + "</option>");

    $.each(jdata, function (i, el) {
            $('#' + divname).append(new Option(el.val, el.id));

        });
//        $('#' + divname).append(new Option("175 - KANWIL JAKARTA XI - JAKARTA VI", 175));
        $('#' + divname).append(new Option("140 - KHUSUS PINJAMAN DAN HIBAH", 140));
        $('#' + divname).append(new Option("999 - DIREKTORAT PENGELOLAAN KAS NEGARA", 999));

    })
            .fail(function () {
            alert("error");
            })
            .always(function () {

            });
}

//function ToGeom(x1, y1, x2, y2){
//    url = WGI_APP_BASE_URL + "bm_lrs/lrs_tools/lrs_coord_segment/"+x1+"/"+y1+"/"+x2+"/"+y2+"/wkt";
//    $.get(url).done(function (data) {
//        //alert(data);
//        jdata = JSON.parse(data);
//         $('#wgeom').val(jdata.geom);
//    })
//    .fail(function () {
//        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
//    })
//    .always(function () {
//        // alert("finished");
//    });
//}

function geoms(){
    var lon1 = $('#wlongitude3').val();
    var lon2 = $('#wlatitude3').val();
    var lon3 = $('#wlongitude4').val();
    var lon4 = $('#wlatitude4').val();

    if(lon1 !== '' && lon2 !== '' && lon3 !== '' && lon4 !== ''){
        toGeom(lon1, lon2, lon3, lon4);
    } else if (lon1 !== '' && lon2 !== '' && lon3 === '' && lon4 === ''){
        $('#wgeom').val(lon1+' '+lon2);
    }
}

function handleSumberEdit(sum, prefix){
    var sumber = sum;
    var sumbers= sumber.split('_')[3];
    var hitung = $("#"+prefix+"panelHitung :input");
    var cara_hitung = sumber.split('_')[1];
    //console.log(sumbers);
    if (cara_hitung === '0'){
        $("input[name='"+prefix+"caraHitung']").val([0]);
        //console.log(cara_hitung);
        //$("[name='kppn']").show();
        //$("[name='percent']").show();
        //$('#'+prefix+'text1').hide();
        $('#'+prefix+'inputPHLN').val(0);
        //$('#'+prefix+'inputPHLN2').val(null);
        $('#'+prefix+'inputKPPNph').val(null);
        $('#'+prefix+'inputRMPdp').val(0);
        //$('#'+prefix+'inputRMPdp2').val(null);
        $('#'+prefix+'inputKPPNrm').val(null);
        $('#'+prefix+'inputRPLNPdp').val(0);
        //$('#'+prefix+'inputRPLNPdp2').val(null);
        $('#'+prefix+'inputKPPNrp').val(null);
        $('select[name=' + prefix + 'register]').val('');
        $('select[name=' + prefix + 'registerpdp]').val('');
        //$('#'+prefix+'registerpdp').val(null);
        //$('#'+prefix+'register').val(null);
    }

    switch(sumbers){
        case '1':
            $('#'+prefix+'register').prop("disabled",true);
            hitung.prop("disabled",true);
            $('#'+prefix+'register').val('');
            break;
        case '2':
            //initComboboxSearch('xregister', 73);
            $('#'+prefix+'register').prop("disabled",false);
            hitung.prop("disabled",false);
            //$("input[name='"+prefix+"caraHitung']").val([2]);
            //setTimeout(function(){ handleRadioHitungEdit(prefix); }, 3000);
            break;
        case '3':
            $('#'+prefix+'register').prop("disabled",false);
            hitung.prop("disabled",true);
            break;
    }

    //new
    switch(prefix){
        case 'y':
            var idpaket = $('#yid_paket').val();
            if (idpaket === null || idpaket === ''){

            } else {
                var thang = $('#ythang_sel').val();
                var keg = $('#ykd_kegiatan_sel').val();
                var out = $('#ykd_output_sel').val();
                var sout = $('#ykd_sub_output_sel').val();
                var kom = $('#ykd_komponen_sel').val();
                var skom = $('#ykd_sub_komponen_sel').val();
                var akn = $('#ykdakun').val();
                var beban = sumber.split('_')[0];
                var jnsban = sumber.split('_')[1];
                var ctarik = sumber.split('_')[2];
                get_akun_update(idpaket, thang, user_satker, keg, out, sout, kom, skom, akn,
                    beban, jnsban, ctarik, 'y');
            }
            break;
        case 'w':
            var idpaket = $('#wid_paket').val();
            if (idpaket === null || idpaket === ''){

            } else {
                var thang = $('#wthang').val();
                var keg = $('#wkd_kegiatan').val();
                var out = $('#wkd_output').val();
                var sout = $('#wkd_sub_output').val();
                var kom = $('#wkd_komponen').val();
                var skom = $('#wkd_sub_komponen').val();
                var akn = $('#wkdakun').val();
                var sumber = $('#wsumber').val();
                var beban = sumber.split('_')[0];
                var jnsban = sumber.split('_')[1];
                var ctarik = sumber.split('_')[2];
                get_akun_update(idpaket, thang, user_satker, keg, out, sout, kom, skom, akn,
                    beban, jnsban, ctarik, 'w');
            }
            break;
    }
}

//update code
function handleKomponen(el, prefix){
    $('#'+prefix+'panelOutput').hide();
    $('#'+prefix+'panelSubOutput').hide();
    $('#'+prefix+'panelKomponen').show();
    var date = new Date();
    var now = date.getFullYear();
    $('#'+prefix+'dianggarkanThn1').text(parseInt(tahun_anggaran));
    $('#'+prefix+'dianggarkanThn2').text(parseInt(tahun_anggaran) + 1);
    $('#'+prefix+'dianggarkanThn3').text(parseInt(tahun_anggaran) + 2);
    $('#'+prefix+'dianggarkanThn4').text(parseInt(tahun_anggaran) + 3);
    var thang = null;
    var giat = null;
    var out = null;
    var sout = null;
    var kmp = null;
    if (prefix === 'x'){
        thang = $('#thang').val();
        giat = $('#kd_kegiatan').val();
        out = $('#kd_output').val();
        sout = $('#kd_sub_output').val();
        kmp = $('#kd_komponen').val();
    } else if (prefix === 'y'){
        thang = $('#ythang_sel').val();
        giat = $('#ykd_kegiatan_sel').val();
        out = $('#ykd_output_sel').val();
        sout = $('#ykd_sub_output_sel').val();
        kmp = $('#ykd_komponen_sel').val();
    } else {
        thang = $('#'+prefix+'thang').val();
        giat = $('#'+prefix+'kd_kegiatan').val();
        out = $('#'+prefix+'kd_output').val();
        sout = $('#'+prefix+'kd_sub_output').val();
        kmp = $('#'+prefix+'kd_komponen').val();
    }

    if (out === '994'){
        $('select[name="'+prefix+'inputJnsBiaya"]').val(1);
        //$('#'+prefix+'inputJnsBiaya').val(1);
    } else {
        //$('#'+prefix+'inputJnsBiaya').val(2);
        $('select[name="'+prefix+'inputJnsBiaya"]').val(2);
    }

    get_kmpnen(giat, out, sout, kmp, thang, prefix)
}
//update code
function get_rfsatuan(kdgiat, kdoutput, prefix) {
    var x = null;
    var obj_data_akun = {
        "kdgiat" : kdgiat,
        "kdoutput" : kdoutput
    };
    var url = "<?php echo base_url("pagu_tahapan/get_rfsatuan"); ?>";
    var params = {"data": obj_data_akun, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
        .done(function (data) {
            //x = data;
            //console.log(data);
            //console.log(prefix);
            if (data === '0' ){
                //$('#'+prefix+'textSatuan').val(data.satuan);
            } else {
                x = JSON.parse(data);
                $('#'+prefix+'textSatuan').text(x.satuan);
            }
        })
        .fail(function () {
            alert("error");
        });
    return x;
}
//update code
function get_output(kdgiat, kdoutput, thang, prefix) {
    var x = null;
    var obj_data_akun = {
        "kdgiat" : kdgiat,
        "kdoutput" : kdoutput,
        "thang" : thang
    };
    var url = "<?php echo base_url("pagu_tahapan/get_output"); ?>";
    var params = {"data": obj_data_akun, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
        .done(function (data) {
            //x = data;
            //console.log(data);
            //console.log(prefix);
            x = JSON.parse(data);
            if (data === '0' ){
                console.log(x.vol);
                var date = new Date();
                var now = date.getFullYear();
                $('#'+prefix+'inputTAAwal').val(now + 1);
                $('#'+prefix+'inputTAAkhir').val(null);
                $('#'+prefix+'inputVolKpjm1').val(null);
                //$('#'+prefix+'inputVolKpjm2').val((x.vol).split('.')[0]);
                $('#'+prefix+'inputVolKpjm2').val(parseFloat(x.vol));
                $('#'+prefix+'inputVolKpjm3').val(null);
                $('#'+prefix+'inputVolKpjm4').val(null);
                $('#'+prefix+'inputVolKpjm5').val(null);
            } else {
                console.log('set out');
                console.log(x.vol);
                $('#'+prefix+'inputTAAwal').val(x.thangawal);
                $('#'+prefix+'inputTAAkhir').val(x.thangakhir);
                $('#'+prefix+'inputVolKpjm1').val(parseFloat(x.volmin1));
                $('#'+prefix+'inputVolKpjm2').val(parseFloat(x.vol));
                $('#'+prefix+'inputVolKpjm3').val(parseFloat(x.volpls1));
                $('#'+prefix+'inputVolKpjm4').val(parseFloat(x.volpls2));
                $('#'+prefix+'inputVolKpjm5').val(parseFloat(x.volpls3));
//                $('#'+prefix+'inputVolKpjm1').val((x.volmin1).split('.')[0]);
//                $('#'+prefix+'inputVolKpjm2').val((x.vol).split('.')[0]);
//                $('#'+prefix+'inputVolKpjm3').val((x.volpls1).split('.')[0]);
//                $('#'+prefix+'inputVolKpjm4').val((x.volpls2).split('.')[0]);
//                $('#'+prefix+'inputVolKpjm5').val((x.volpls3).split('.')[0]);
            }
        })
        .fail(function () {
            alert("error");
        });
    return x;
}
//update code
function get_soutput(kdgiat, kdoutput, kdsoutput, thang, prefix) {
    var x = null;
    var obj_data_akun = {
        "kdgiat" : kdgiat,
        "kdoutput" : kdoutput,
        "thang" : thang,
        "kdsoutput" : kdsoutput
    };
    var url = "<?php echo base_url("pagu_tahapan/get_soutput"); ?>";
    var params = {"data": obj_data_akun, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
        .done(function (data) {
            //x = data;
            //console.log(data);
            //console.log(prefix);
            if (data === '0' ){
                $('#'+prefix+'inputSoutVolume').val(null);
            } else {
                console.log('set sout');
                x = JSON.parse(data);
                $('#'+prefix+'inputSoutVolume').val(parseFloat(x.volsout));
                //$('#'+prefix+'inputSoutVolume').val((x.volsout).split('.')[0]);
            }
        })
        .fail(function () {
            alert("error");
        });
    return x;
}

//update code
function get_kmpnen(kdgiat, kdoutput, kdsoutput, kdkmpnen, thang, prefix) {
    var x = null;
    var obj_data_akun = {
        "kdgiat" : kdgiat,
        "kdoutput" : kdoutput,
        "thang" : thang,
        "kdsoutput" : kdsoutput,
        "kdkmpnen" : kdkmpnen
    };
    var url = "<?php echo base_url("pagu_tahapan/get_kmpnen"); ?>";
    var params = {"data": obj_data_akun, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
    $.post(url, params)
        .done(function (data) {
            //x = data;
            //console.log(data);
            //console.log(prefix);
            if (data === '0' ){
                var out = null;
                if (prefix === 'x'){
                    out = $('#kd_output').val();
                } else if (prefix === 'y'){;
                    out = $('#ykd_output_sel').val();
                } else {
                    out = $('#'+prefix+'kd_output').val();
                }
                if (out === '994'){
                    $('select[name="'+prefix+'inputJnsBiaya"]').val(1);
                } else {
                    $('select[name="'+prefix+'inputJnsBiaya"]').val(2);
                }
                $('select[name="'+prefix+'inputSftBiaya"]').val(1);
                handleSftBiaya(1, prefix);
                $('#'+prefix+'inputDianggarkanThn1').prop('checked', false);
                $('#'+prefix+'inputDianggarkanThn2').prop('checked', false);
                $('#'+prefix+'inputDianggarkanThn3').prop('checked', false);
                $('#'+prefix+'inputDianggarkanThn4').prop('checked', false);
            } else {
                console.log('set kmpnen');
                x = JSON.parse(data);
                $('select[name="'+prefix+'inputJnsBiaya"]').val(x.kdbiaya);
                $('select[name="'+prefix+'inputSftBiaya"]').val(x.kdsbiaya);
                $("input[name='"+prefix+"inputIndexKpjm']").val([x.indekskali]);
                $("input[name='"+prefix+"inputIndexOutput']").val([x.indeksout]);
                if (x.n1 === '1'){
                    $('#'+prefix+'inputDianggarkanThn1').prop('checked', true);
                } else {
                    $('#'+prefix+'inputDianggarkanThn1').prop('checked', false);
                }
                if (x.n2 === '1'){
                    $('#'+prefix+'inputDianggarkanThn2').prop('checked', true);
                } else {
                    $('#'+prefix+'inputDianggarkanThn2').prop('checked', false);
                }
                if (x.n3 === '1'){
                    $('#'+prefix+'inputDianggarkanThn3').prop('checked', true);
                } else {
                    $('#'+prefix+'inputDianggarkanThn3').prop('checked', false);
                }
                if (x.n4 === '1'){
                    $('#'+prefix+'inputDianggarkanThn4').prop('checked', true);
                } else {
                    $('#'+prefix+'inputDianggarkanThn4').prop('checked', false);
                }
            }
        })
        .fail(function () {
            alert("error");
        });
    return x;
}

//update code
function handleSftBiaya(el, prefix){
    var sftBiaya = el.value;
    if (sftBiaya === '2'){
        $("input[name='"+prefix+"inputIndexKpjm']").prop('disabled', true);
        $("input[name='"+prefix+"inputIndexOutput']").prop('disabled', true);
        $("input[name='"+prefix+"inputIndexKpjm']").val([1]);
        $("input[name='"+prefix+"inputIndexOutput']").val([0]);
    } else {
        $("input[name='"+prefix+"inputIndexKpjm']").prop('disabled', false);
        $("input[name='"+prefix+"inputIndexOutput']").prop('disabled', false);
        $("input[name='"+prefix+"inputIndexKpjm']").val([0]);
        $("input[name='"+prefix+"inputIndexOutput']").val([0]);
    }
}

function handleBlok(el, prefix){
    var blok = el.value;
    console.log(blok);
    if (blok === null || blok === '' || blok === '#'){
        $("input[name='"+prefix+"itemblokir']").prop('disabled', true);
        $("input[name='"+prefix+"itemblokir']").prop('checked', false);
        $("#"+prefix+"urblokir").val('');

    } else {
        $("input[name='"+prefix+"itemblokir']").prop('disabled', false);
    }
}

function handleBlokir(el, prefix){
    console.log('blokir');
    console.log(el);
    var blok = null;
    if (el === null){
        blok = '1';
    } else if (el === 'kosong'){
        blok = '0';
    } else {
        blok = el.value;
    }
    var caraHitung = $("input[name='"+prefix+"caraHitung']:checked").val();
    var sumbers = $('#'+prefix+'sumber').val();
    //console.log(sumbers);
    var sumber = sumbers.split('_')[1];
    var jumlah = $("#"+prefix+"jumlah").val();
    var jumlahh = jumlah.split(',').join(''); //Total
    var inputPHLN = $("#"+prefix+"inputPHLN").val(); //prosen
    var inputRMPdp = $("#"+prefix+"inputRMPdp").val(); //prosen
    var inputRPLNPdp = $("#"+prefix+"inputRPLNPdp").val(); //prosen
    if (blok === '0'){
        if (caraHitung === '4'){
            $('#'+prefix+'panelNonSharing').show();
            $('#'+prefix+'inputBlokirPHLN').prop('disabled', false);
            $('#'+prefix+'inputBlokirRMPdp').prop('disabled', false);
            $('#'+prefix+'inputBlokirRPLNPdp').prop('disabled', true);
            $('#'+prefix+'inputNSRPLNPdp2').val(0);
        } else {
            $('#'+prefix+'panelNonSharing').hide();
        }
        $('#'+prefix+'panelPaguBlokir').hide();
        $('#'+prefix+'inputBlokirPHLN').val(0);
        $('#'+prefix+'inputBlokirRMPdp').val(0);
        $('#'+prefix+'inputBlokirRPLNPdp').val(0);
        $('#'+prefix+'inputRphBlokir').val(0);
    } else {
        $('#'+prefix+'panelNonSharing').show();
        $('#'+prefix+'panelPaguBlokir').show();
        if (sumber === '0'){
            $('#'+prefix+'panelNonSharing').hide();
            $('#'+prefix+'rphblok').show();
            $('#'+prefix+'blokirs1').hide();
            $('#'+prefix+'blokirs2').hide();
            $('#'+prefix+'blokirs3').hide();
            $('#'+prefix+'inputRphBlokir').prop('disabled', false);
        } else {
            if (caraHitung === '4'){
                $('#'+prefix+'panelNonSharing').show();
                $('#'+prefix+'rphblok').hide();
                $('#'+prefix+'blokirs1').show();
                $('#'+prefix+'blokirs2').show();
                $('#'+prefix+'blokirs3').show();
                $('#'+prefix+'inputNSPHLN').val(0);
                $('#'+prefix+'inputNSRMPdp').val(0);
                $('#'+prefix+'inputNSRPLNPdp').val(0);
                $('#'+prefix+'inputBlokirPHLN').prop('disabled', false);
                $('#'+prefix+'inputBlokirRMPdp').prop('disabled', false);
                $('#'+prefix+'inputBlokirRPLNPdp').prop('disabled', true);
            } else if ( caraHitung === '1'){
                $('#'+prefix+'panelNonSharing').hide();
                $('#'+prefix+'inputNSPHLN').val(inputPHLN);
                $('#'+prefix+'inputNSRMPdp').val(inputRMPdp);
                $('#'+prefix+'inputNSRPLNPdp').val(inputRPLNPdp);
                $('#'+prefix+'rphblok').show();
                $('#'+prefix+'blokirs1').hide();
                $('#'+prefix+'blokirs2').hide();
                $('#'+prefix+'blokirs3').hide();
                $('#'+prefix+'inputRphBlokir').prop('disabled', false);
                //$('#'+prefix+'inputNSPHLN').val(0);
                //$('#'+prefix+'inputNSRMPdp').val(0);
                //$('#'+prefix+'inputNSRPLNPdp').val(0);
            } else {
                $('#'+prefix+'panelNonSharing').show();
                $('#'+prefix+'inputNSPHLN').val(inputPHLN);
                $('#'+prefix+'inputNSRMPdp').val(inputRMPdp);
                $('#'+prefix+'inputNSRPLNPdp').val(inputRPLNPdp);
                $('#'+prefix+'inputNSPHLN2').prop('disabled', true);
                $('#'+prefix+'inputNSRMPdp2').prop('disabled', true);
                $('#'+prefix+'inputNSRPLNPdp2').prop('disabled', true);
                $('#'+prefix+'rphblok').hide();
                $('#'+prefix+'blokirs1').show();
                $('#'+prefix+'blokirs2').show();
                $('#'+prefix+'blokirs3').show();
                $('#'+prefix+'inputNSPHLN2').val(addCommas((parseInt(jumlahh) * parseFloat(inputPHLN))/100));
                $('#'+prefix+'inputNSRMPdp2').val(addCommas((parseInt(jumlahh) * parseFloat(inputRMPdp))/100));
                $('#'+prefix+'inputNSRPLNPdp2').val(addCommas((parseInt(jumlahh) * parseFloat(inputRPLNPdp))/100));
                $('#'+prefix+'Total').val(jumlah);
                $('#'+prefix+'inputBlokirPHLN').prop('disabled', false);
                $('#'+prefix+'inputBlokirRMPdp').prop('disabled', false);
                $('#'+prefix+'inputBlokirRPLNPdp').prop('disabled', false);
            }
        }
    }
}

function hitungBlokir(prefix){
    var phln = $('#'+prefix+'inputNSPHLN2').val();
        phln = (phln === null ||phln === '')? 0: (phln.split(',').join(''));
    var rm = $('#'+prefix+'inputNSRMPdp2').val();
        rm = (rm === null ||rm === '')? 0: (rm.split(',').join(''));
    var rpln = $('#'+prefix+'inputNSRPLNPdp2').val();
        rpln = (rpln === null ||rpln === '')? 0: (rpln.split(',').join(''));
    var BlokirPHLN = $("#"+prefix+"inputBlokirPHLN").val();
        BlokirPHLN = (BlokirPHLN === null ||BlokirPHLN === '')? 0: (BlokirPHLN.split(',').join(''));
    var BlokirRMPdp = $("#"+prefix+"inputBlokirRMPdp").val();
        BlokirRMPdp = (BlokirRMPdp === null ||BlokirRMPdp === '')? 0: (BlokirRMPdp.split(',').join(''));
    var BlokirRPLNPdp = $("#"+prefix+"inputBlokirRPLNPdp").val();
        BlokirRPLNPdp = (BlokirRPLNPdp === null ||BlokirRPLNPdp === '')? 0: (BlokirRPLNPdp.split(',').join(''));
    var RphBlokir = $("#"+prefix+"inputRphBlokir").val();
        RphBlokir = (RphBlokir === null ||RphBlokir === '')? 0: (RphBlokir.split(',').join(''));
    var jumlah = $("#"+prefix+"jumlah").val();
        jumlah = (jumlah === null ||jumlah === '')? 0: (jumlah.split(',').join(''));

    console.log(phln);
    console.log(BlokirPHLN);
    console.log(jumlah);

    if(parseInt(BlokirPHLN) > 0){
        if (parseInt(BlokirPHLN) > parseInt(phln)){
            $('#'+prefix+'textBlokir').text('Dana Blokir > Dana PHLN');
        } else {
            $('#'+prefix+'textBlokir').text('');
        }
    }
    if(parseInt(BlokirRMPdp) > 0){
        if (parseInt(BlokirRMPdp) > parseInt(rm)){
            $('#'+prefix+'textBlokir').text('Dana Blokir > Dana RMPdp');
        } else {
            $('#'+prefix+'textBlokir').text('');
        }
    }
    if(parseInt(BlokirRPLNPdp) > 0){
        if (parseInt(BlokirRPLNPdp) > parseInt(rpln)){
            $('#'+prefix+'textBlokir').text('Dana Blokir > Dana RPLNPdp');
        } else {
            $('#'+prefix+'textBlokir').text('');
        }
    }
    if(parseInt(RphBlokir) > 0){
        if (parseInt(RphBlokir) > parseInt(jumlah)){
            $('#'+prefix+'textBlokir').text('Dana Blokir > Jumlah');
        } else {
            $('#'+prefix+'textBlokir').text('');
        }
    }
}

function get_nmregister(register) {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_nmregister') ?>" + "/" + register,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (data) {
            x = data;
        },
        failure: function (errMsg) {
            alert(errMsg);
        }
    });
    return x;
}

//function handleRadioLokasi(el, prefix){
//    console.log('sta lokasi');
//    var radio = null;
//    if (el === null){
//        radio = '1';
//    } else {
//        radio = el.value;
//    }
//    console.log(prefix);
//    if (radio === '1'){
//        console.log('satu');
//        $('.staw').show();
//        $("#sta_awal").prop('disabled',false);
//        $("#sta_akhir").prop('disabled',false);
//        $("#longitude").prop('disabled',true);
//        $("#latitude").prop('disabled',true);
//        $("#longitude2").prop('disabled',true);
//        $("#latitude2").prop('disabled',true);
//        $("#sta_awal_sel").prop('disabled',false);
//        $("#sta_akhir_sel").prop('disabled',false);
//        $("#"+prefix+"sta_awal").prop('disabled',false);
//        $("#"+prefix+"sta_akhir").prop('disabled',false);
//        $("#"+prefix+"longitude").prop('disabled',true);
//        $("#"+prefix+"latitude").prop('disabled',true);
//        $("#"+prefix+"longitude2").prop('disabled',true);
//        $("#"+prefix+"latitude2").prop('disabled',true);
//        $("#longitude").val('');
//        $("#latitude").val('');
//        $("#longitude2").val('');
//        $("#latitude2").val('');
//        $("#"+prefix+"longitude").val('');
//        $("#"+prefix+"latitude").val('');
//        $("#"+prefix+"longitude2").val('');
//        $("#"+prefix+"latitude2").val('');
//    } else {
//        console.log('dua');
//        $('.staw').hide();
//        $("#"+prefix+"sta_awal_view").val('');
//        $("#"+prefix+"sta_akhir_view").val('');
//        //$("#sta_awal").prop('disabled',true);
//        //$("#sta_akhir").prop('disabled',true);
//        $("#longitude").prop('disabled',false);
//        $("#latitude").prop('disabled',false);
//        $("#longitude2").prop('disabled',false);
//        $("#latitude2").prop('disabled',false);
//        //$("#sta_awal_sel").prop('disabled',true);
//        //$("#sta_akhir_sel").prop('disabled',true);
//        //$("#"+prefix+"sta_awal").prop('disabled',true);
//        //$("#"+prefix+"sta_akhir").prop('disabled',true);
//        $("#"+prefix+"longitude").prop('disabled',false);
//        $("#"+prefix+"latitude").prop('disabled',false);
//        $("#"+prefix+"longitude2").prop('disabled',false);
//        $("#"+prefix+"latitude2").prop('disabled',false);
//        $("#sta_awal").val('');
//        $("#sta_akhir").val('');
//        $("#sta_awal_sel").val('');
//        $("#sta_akhir_sel").val('');
//        $("#"+prefix+"sta_awal").val('');
//        $("#"+prefix+"sta_akhir").val('');
//    }
//}

function handleHide(prefix){
    $('#'+prefix+'panelOutput').hide();
    $('#'+prefix+'panelSubOutput').hide();
    $('#'+prefix+'panelKomponen').hide();
}

function handleShow(ket, prefix){
    if (ket === 'out'){
        $('#'+prefix+'panelOutput').show();
        $('#'+prefix+'panelSubOutput').hide();
        $('#'+prefix+'panelKomponen').hide();
        $('#'+prefix+'Thn1').text(parseInt(tahun_anggaran) - 1);
        $('#'+prefix+'Thn2').text(parseInt(tahun_anggaran));
        $('#'+prefix+'Thn3').text(parseInt(tahun_anggaran) + 1);
        $('#'+prefix+'Thn4').text(parseInt(tahun_anggaran) + 2);
        $('#'+prefix+'Thn5').text(parseInt(tahun_anggaran) + 3);
    }
    if (ket === 'sout'){
        $('#'+prefix+'panelOutput').hide();
        $('#'+prefix+'panelSubOutput').show();
        $('#'+prefix+'panelKomponen').hide();
        var giat = null;
        var out = null;
        if (prefix === 'x'){
            giat = $('#kd_kegiatan').val();
            out = $('#kd_output').val();
        } else if ( prefix === 'y'){
            giat = $('#ykd_kegiatan_sel').val();
            out = $('#ykd_output_sel').val();
        } else {
            giat = $('#'+prefix+'kd_kegiatan').val();
            out = $('#'+prefix+'kd_output').val();
        }

        get_rfsatuan(giat, out, prefix);
    }
    if (ket === 'kom'){
        $('#'+prefix+'panelOutput').hide();
        $('#'+prefix+'panelSubOutput').hide();
        $('#'+prefix+'panelKomponen').show();
        $('#'+prefix+'dianggarkanThn1').text(parseInt(tahun_anggaran));
        $('#'+prefix+'dianggarkanThn2').text(parseInt(tahun_anggaran) + 1);
        $('#'+prefix+'dianggarkanThn3').text(parseInt(tahun_anggaran) + 2);
        $('#'+prefix+'dianggarkanThn4').text(parseInt(tahun_anggaran) + 3);
    }

    //update code
    var thang = null;
    var giat = null;
    var out = null;
    var sout = null;
    if (prefix === 'x'){
        thang = $('#thang').val();
        giat = $('#kd_kegiatan').val();
        out = $('#kd_output').val();
        sout = $('#kd_sub_output').val();
    } else if ( prefix === 'y'){
        thang = $('#ythang_sel').val();
        giat = $('#ykd_kegiatan_sel').val();
        out = $('#ykd_output_sel').val();
        sout = $('#ykd_sub_output_sel').val();
    } else {
        thang = $('#'+prefix+'thang').val();
        giat = $('#'+prefix+'kd_kegiatan').val();
        out = $('#'+prefix+'kd_output').val();
        sout = $('#'+prefix+'kd_sub_output').val();
    }
    if (thang !== null && giat !== null && out !== null && sout !== null){
        get_rfsatuan(giat, out, prefix);
    }
}

function toSTAawal(x, y, prefix){
    url = WGI_APP_BASE_URL + "bm_lrs/lrs_tools/lrs_coord_sta/"+x+"/"+y;
    $.get(url).done(function (data) {
        //alert(data);
        jdata = JSON.parse(data);
        $("#sta_awal").val(Math.round(jdata.at));
        $("#sta_awal_sel").val(Math.round(jdata.at));
        $("#"+prefix+"sta_awal").val(Math.round(jdata.at));
//        var ruas = $('#'+prefix+'id_ruas').val();
//        console.log(ruas);
//        if (jdata.routeId === ruas){
//            $("#sta_awal").val(Math.round(jdata.at));
//            $("#sta_awal_sel").val(Math.round(jdata.at));
//            $("#"+prefix+"sta_awal").val(Math.round(jdata.at));
//            $('#'+prefix+'textMaxx').text('');
//        } else {
//            $('#'+prefix+'textMaxx').text('STA tidak ada di ruas ini');
//        }
    })
    .fail(function () {
        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
    })
    .always(function () {
        // alert("finished");
    });
}

function toSTAakhir(x, y, prefix){
    url = WGI_APP_BASE_URL + "bm_lrs/lrs_tools/lrs_coord_sta/"+x+"/"+y;
    $.get(url).done(function (data) {
        //alert(data);
        jdata = JSON.parse(data);
        $("#sta_akhir").val(Math.round(jdata.at));
        $("#sta_akhir_sel").val(Math.round(jdata.at));
        $("#"+prefix+"sta_akhir").val(Math.round(jdata.at));
//        var ruas = $('#'+prefix+'id_ruas').val();
//        if (jdata.routeId === ruas){
//            $("#sta_akhir").val(Math.round(jdata.at));
//            $("#sta_akhir_sel").val(Math.round(jdata.at));
//            $("#"+prefix+"sta_akhir").val(Math.round(jdata.at));
//            $('#'+prefix+'textMaxx').text('');
//        } else {
//            $('#'+prefix+'textMaxx').text('STA tidak ada di ruas ini');
//        }
    })
    .fail(function () {
        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
    })
    .always(function () {
        // alert("finished");
    });
}

function staAwal(prefix){
    var timeout = null;
    clearTimeout(timeout);
    timeout = setTimeout(function() {
        var lon = null;
        var lat = null;
        if (prefix === 'x'){
            lon = $('#longitude').val();
            lat = $('#latitude').val();
        } else {
            lon = $('#'+prefix+'longitude').val();
            lat = $('#'+prefix+'latitude').val();
        }
        //console.log(lon);
        //console.log(lat);
        if(lon !== '' && lat !== ''){
            toSTAawal(lon, lat, prefix);
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                handleGeo(prefix);
            }, 1000);
        }
        if(lon === '' || lat === ''){
            $("#sta_awal").val('');
            $("#sta_awal_sel").val('');
            $("#"+prefix+"sta_awal").val('');
        }
    }, 5000);
}

function staAkhir(prefix){
    var timeout = null;
    clearTimeout(timeout);
    timeout = setTimeout(function() {
        var lon2 = null;
        var lat2 = null;
        if (prefix === 'x'){
            lon2 = $('#longitude2').val();
            lat2 = $('#latitude2').val();
        } else {
            lon2 = $('#'+prefix+'longitude2').val();
            lat2 = $('#'+prefix+'latitude2').val();
        }
        //console.log(lon2);
        //console.log(lat2);
        if (lon2 !== '' && lat2 !== ''){
            toSTAakhir(lon2, lat2, prefix);
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                handleGeo(prefix);
            }, 1000);
        }
        if(lon2 === '' || lat2 === ''){
            $("#sta_akhir").val('');
            $("#sta_akhir_sel").val('');
            $("#"+prefix+"sta_akhir").val('');
        }
    }, 5000);
}

function handleGeo(prefix){
    var ru = null;
    var sta = null;
    var ste = null;
    if (prefix === 'x'){
        ru = $("#xid_ruas").val();
        sta = $("#sta_awal").val();
        ste = $("#sta_akhir").val();
    } else if (prefix === 'y'){
        ru = $("#id_ruas").val();
        sta = $("#sta_awal_sel").val();
        ste = $("#sta_akhir_sel").val();
    } else {
        ru = $("#"+prefix+"id_ruas").val();
        sta = $("#"+prefix+"sta_awal").val();
        ste = $("#"+prefix+"sta_akhir").val();
    }

    if (ru !== null && sta !== null && ste !== null){
        getGEOMRuas(''+prefix+'geom', ru, sta, ste);
    }
}

function maxRuas(id_ruas, prefix){
    url = WGI_APP_BASE_URL + "bm_lrs/lrs_tools/lrs_route_len/"+id_ruas;
    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        //console.log(jdata);
        if(jdata.status === 'ok'){
            var rad = $("input[name='"+prefix+"radioVolume']:checked").val();
            console.log(rad);
            if ( rad === '2'){
                $('#'+prefix+'maxRuas').text('');
            } else {
                $('#'+prefix+'maxRuas').text('( Panjang ruas maksimum: ' + (Math.round((jdata.length)/1000)) + ' KM )');
            }
            $('#sta_awal').attr('max', (Math.round(jdata.length)));
            $('#sta_awal_sel').attr('max', (Math.round(jdata.length)));
            $('#'+prefix+'sta_awal').attr('max', (Math.round(jdata.length)));
            $('#sta_akhir').attr('max', (Math.round(jdata.length)));
            $('#sta_akhir_sel').attr('max', (Math.round(jdata.length)));
            $('#'+prefix+'sta_akhir').attr('max', (Math.round(jdata.length)));
        } else {
            $('#'+prefix+'maxRuas').text('');
            $('#sta_awal').attr('max', 0);
            $('#sta_awal_sel').attr('max', 0);
            $('#'+prefix+'sta_awal').attr('max', 0);
            $('#sta_akhir').attr('max', 0);
            $('#sta_akhir_sel').attr('max', 0);
            $('#'+prefix+'sta_akhir').attr('max', 0);
        }
    })
    .fail(function () {
        alert("Koneksi Jaringan terganggu / Data service tidak dapat diakses");
    })
    .always(function () {
        // alert("finished");
    });
}

function get_kabkota(prov, kab) {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_kabkota') ?>" + "/" + prov + "/" + kab,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (data) {
            x = data;
        },
        failure: function (errMsg) {
            alert(errMsg);
        }
    });
    return x;
}

function cek_header(id_paket) {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/cek_header') ?>" + "/" + id_paket,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (data) {
            x = data;
        },
        failure: function (errMsg) {
            alert(errMsg);
        }
    });
    return x;
}

function get_header2s(id_paket, kdakun, id_ppk, header2) {
    var x = null;
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/get_header2s') ?>" + "/" + id_paket + "/" + kdakun + "/" + id_ppk + "/" + header2,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (data) {
            x = data;
        },
        failure: function (errMsg) {
            alert(errMsg);
        }
    });
    return x;
}

//$(document).ajaxStop(function () {
//    $('#loading').hide();
//});
//
//$(document).ajaxStart(function () {
//    $('#loading').show();
//});

//function get_latlon(id_paket, id_usulan) {
//    var x = null;
//    $.ajax({
//    url: "<?php echo base_url('pagu_tahapan/get_latlon') ?>" + "/" + id_paket + "/" + id_usulan,
//        contentType: "application/json; charset=utf-8",
//        dataType: "json",
//        async: false,
//        success: function (data) {
//            x = data;
//        },
//        failure: function (errMsg) {
//            alert(errMsg);
//        }
//    });
//    return x;
//}
</script>
