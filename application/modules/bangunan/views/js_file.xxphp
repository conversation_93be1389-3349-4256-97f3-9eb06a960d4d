<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<script>
    var xhrdatairms = null;
    var xhrdatasipro = null;
    var xhrdatarams = null;
    var xhrdataeprogram = null;
    var xhrdatarenstra = null;
    var xhrdatausulandpr = null;
    var xhrdatausulanpemda = null;
    var tablex = null;
    //modal detail
    var xhrdataxirms = null;
    var xhrdatasxipro = null;
    var xhrdataxrams = null;
    var xhrdataxeprogram = null;
    var xhrdataxrenstra = null;
    var xhrdataxusulandpr = null;
    var xhrdatausxulanpemda = null;
    var tlist_paket = null;
    var user_satker = "<?php echo $this->session->users['kode_satker']; ?>";
    var prrr = "<?php echo $this->session->users['kdlokasi']; ?>";
    var id_user_get = "<?php echo $this->session->users['id_user']; ?>";
    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";
    var id_user_group = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var yearnow = "<?php echo $this->session->konfig_tahun_ang; ?>"
            var kd_satker = "<?php echo $this->session->users['kode_satker']; ?>";
    var data_cart = [];
    var obj_data_cart = {};
    var tahap = "<?php echo $this->session->konfig_tahapan; ?>";
    var kdtahap="<?php echo $this->session->konfig_kd_tahapan;?>";
    var tahun_anggaran="<?php echo $this->session->konfig_tahun_ang;?>";

    function add_to_chart(sa1thn_id, iDataIndex) {
//        var tag = $('.div-tags').val();
//        console.log(tag);
        var statement_tag = 'SIPRO::' + sa1thn_id;
        dtTagging(statement_tag);
//        if (tag === null || tag === ''){
//            console.log("isi");
//            var statement_tag = 'SIPRO::' + sa1thn_id;
//            dtTagging(statement_tag);
//        } else {
//            console.log("udah ada");
//        }
    var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
    var obj_arahan = {
    "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#kd_komponen").val(),
            "kd_sub_komponen": $("#kd_sub_komponen").val(),
            "nama_sub_komponen": $("#nama_sub_komponen").val(),
            "kd_jns_belanja": $("#kdgbkpk").val(),
            "kdakun": $("#kdakun").val(),
            "kdkppn": $("#kdkppn").val(),
            "id_ppk": $("#id_ppk").val(),
            "rc_ded_status": $("#rc_ded_status").val(),
            "rc_fs_status": $("#rc_fs_status").val(),
            "rc_lahan_status": $("#rc_lahan_status").val(),
            "rc_doklin_status": $("#rc_doklin_status").val(),
            "jnskontrak": $("#jnskontrak").val(),
            "id_ruas": $("#id_ruas").val(),
            "sta_awal": $("#sta_awal").val(),
            "sta_akhir": $("#sta_akhir").val(),
            "id_jembatan": $("#xid_jembatan").val(),
            "longitude": $("#longitude").val(),
            "latitude": $("#latitude").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#hargasat").val(),
            "jumlah": data_selected[10]
    };
    refreshComboboxOutput('xid_ruas', 70, 'KODE_KABKOTA', data_selected[20]);
    var kdgi=obj_arahan.kd_kegiatan;
    var kdo=obj_arahan.kd_output;
    var kdou=kdo.split('-')[1];
//    alert(kdou);
    if(kdgi=='2409' && (kdou=='014' || kdou=='015' || kdou=='016' || kdou=='017' || kdou=='018' || kdou=='951' || kdou=='970' || kdou=='994' || kdou=='999'))
               {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                     $(".longsa").hide();
                       $(".uraians").show();
                   //$("#fsk").hide();
                  // alert("ilang");
                  $("#VolumeKegiatan").show();
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").show();
                    $("input[name='xradioVolume']").val([3]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                    $('#'+prefix+'maxRuas').text('( Panjang ruas maksimum: ' + (Math.round(jdata.length)) + ' )');
               }
               //fisik
               else if(kdgi=='2409' && (kdou=='001' || kdou=='002' || kdou=='003' || kdou=='004' || kdou=='005' || kdou=='006' || kdou=='007' || kdou=='008' || kdou=='009' || kdou=='010' || kdou=='011' || kdou=='012' || kdou=='013'))
                {
                    //jembatan
                    if(kdou=='004' || kdou=='005' || kdou=='006' || kdou=='010')
                    {
                     $(".ruasjalan").show();
                     $(".ruasjalan").removeClass('col-md-6');
                     $(".ruasjalan").addClass('col-md-12');
                    $(".staw").hide();
                    $("#sta_awal").empty();
                    $("#sta_akhir").empty();
                    $(".jembatans").show();
                    $("#l1").text("Longitude (X1)")
                    $("#l2").text("Latitude (Y1)")
                    $("#longitudes").empty()
                    $("#latitudes").empty()
                    $("#longitude").empty()
                    $("#latitude").empty()
                    $(".longs").show();
                    $(".longsa").hide();
                    $("#VolumeKegiatan").show();
                    $(".id_radioRuas").hide();
                    $(".id_radioJembatan").show();
                    $(".id_radioHonor").hide();
                    $("input[name='xradioVolume']").val([2]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                    }
                    //ruas
                    else
                    {
                    $(".ruasjalan").show();
                   $(".ruasjalan").removeClass('col-md-12');
                     $(".ruasjalan").addClass('col-md-6');
                    $(".staw").show();
                    $(".jembatans").hide();
                    $("#xid_jembatan").empty()
                    $("#l1").text("Longitude (X1)")
                    $("#l2").text("Latitude (Y1)")
                     $("#l3").text("Longitude (X2)")
                    $("#l4").text("Latitude (Y2)")
                    $(".longs").show();
                    $(".longsa").show();
                    $("#VolumeKegiatan").show();
                    $(".id_radioRuas").show();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").hide();
                    $("input[name='xradioVolume']").val([1]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                    }
                }
                //tambah paket
                else if(kdgi !='2409')
                {
                   $(".ruasjalan").hide();
                    $(".staw").hide();
                    $(".jembatans").hide();
                    $(".longs").hide();
                    $(".uraians").show();
                     $(".longsa").hide();
                     $("#VolumeKegiatan").show();
                     $(".id_radioRuas").hide();
                    $(".id_radioJembatan").hide();
                    $(".id_radioHonor").show();
                    $("input[name='xradioVolume']").val([3]);
                    setTimeout(function(){handleRadioVolume('x'); }, 3000);
                }


    //cek jika kode prov dari rujukan != null atau kosong
    if (data_selected[19] != "" || typeof data_selected[19] != "object"){
    var pkdprov = data_selected[19];
    bind_prov_from_vprovsatker(kd_satker, pkdprov);
    }

    $("#thang").val(data_selected[1]);
    $("#kd_isu").val(data_selected[32]);
    $("#rc_ded_status").val(check_rc((obj_arahan.rc_DED)));
    $("#rc_fs_status").val(check_rc((obj_arahan.rc_FS)));
    $("#rc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
    $("#rc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
    $("#xvolume").val(data_selected[9]);
    if (isNull(obj_arahan.wps_kode) == "#") {

    } else {

    }
    var rIdprov = $("#id_rprov").val();
    refreshComboboxOutput('kabkot', 61, 'id_kabkot', obj_arahan.kabkot, obj_arahan.kabkot);
    $("#subkw").val(data_selected[36]);
    $("#kd_kegiatan").val(data_selected[16]);
    $(".decformat").val(obj_arahan.rpm);
    $("#totalpagu").val(obj_arahan.rpm);
    $("#rm").val(obj_arahan.rpm);
    $("#volume").val(obj_arahan.volume);
    $("#satuan").val(obj_arahan.satuan);
    $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
    var j = function () {
    var defer = $.Deferred();
    refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var a = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var b = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var c = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode;
    var selected = "";
    if(obj_arahan.kws_kode == null) {
    selected = "-1";
    } else {

    selected = obj_arahan.kws_kode;
    }
    refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    //wps_kode,kws_kode
    var d = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
    refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");
    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    c().then(d).then(j).then(a).then(b);
    var xhargasat = numberWithCommas($("#xhargasat").val());
    var newhargasat = xhargasat.split(".")[0];
    $("#xhargasat").val(newhargasat);
    var xjumlah = numberWithCommas($("#xjumlah").val());
    $("#xjumlah").val(numberWithCommas(xjumlah));
    $("#xrm").val(xjumlah);
    var xvolume = $("#xvolume").val();
    $("#xvolume").val(numberWithCommas(xvolume));
    }

    function bind_prov_from_vprovsatker(kode_satker, kdprov){
    var obj_vprovsatker = get_vprov_satker(kode_satker);
    $("#prov").empty();
    $("#prov").append("<option value='#'>Pilih</option>");
    for (var i = 0; i <= obj_vprovsatker.length - 1; i++) {
    var option_value = obj_vprovsatker[i].kdlokasi;
    if (kdprov == kdprov){
    var html_option = [
            "<option selected value=" + option_value + " >",
            obj_vprovsatker[i].nama_prov,
            "</option>"
    ].join("\n");
    } else{
    var html_option = [
            "<option value=" + option_value + " >",
            obj_vprovsatker[i].nama_prov,
            "</option>"
    ].join("\n");
    }

    $("#prov").append(html_option);
    }
    }

    function get_vprov_satker(kode_satker){
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_vprov_satker') ?>" + "/" + kode_satker;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }


    var data_cart_edit = [];
    var obj_data_cart_edit = {};
    function add_to_chart_edit(sa1thn_id, iDataIndex) {;
    var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
    var obj_arahan = {
    "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#zkd_komponen").val(),
            "kd_sub_komponen": $("#zkd_sub_komponen").val(),
            "nama_sub_komponen": $("#znama_sub_komponen").val(),
            "kd_jns_belanja": $("#zkdgbkpk").val(),
            "kdakun": $("#zkdakun").val(),
            "kdkppn": $("#zkdkppn").val(),
            "id_ppk": $("#zid_ppk").val(),
            "rc_ded_status": $("#zrc_ded_status").val(),
            "rc_fs_status": $("#zrc_fs_status").val(),
            "rc_lahan_status": $("#zrc_lahan_status").val(),
            "rc_doklin_status": $("#zrc_doklin_status").val(),
            "jnskontrak": $("#zjnskontrak").val(),
            "id_ruas": $("#zid_ruas").val(),
            "sta_awal": $("#zsta_awal").val(),
            "sta_akhir": $("#zsta_akhir").val(),
            "id_jembatan": $("#zxid_jembatan").val(),
            "longitude": $("#zlongitude").val(),
            "latitude": $("#zlatitude").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#zhargasat").val(),
            "jumlah": data_selected[10]
    };

    console.log("object arahan");
    console.log(obj_arahan);
    $("#zthang").val(data_selected[1]);
    $("#zkd_isu").val(data_selected[32]);
    $("#zrc_ded_status").val(check_rc((obj_arahan.rc_DED)));
    $("#zrc_fs_status").val(check_rc((obj_arahan.rc_FS)));
    $("#zrc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
    $("#zrc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
    $("#zvolume").val(data_selected[9]);

    if (isNull(obj_arahan.wps_kode) == "#") {

    } else {

    }
    var rIdprov = $("#id_rprov").val();
    refreshComboboxOutput('kabkot', 61, 'id_kabkot', obj_arahan.kabkot, obj_arahan.kabkot);

    $("#zsubkw").val(data_selected[36]);
    $("#zkd_kegiatan").val(data_selected[16]);
    $(".decformat").val(obj_arahan.rpm);
    $("#ztotalpagu").val(obj_arahan.rpm);
    $("#zrm").val(obj_arahan.rpm);
    $("#zvolume").val(obj_arahan.volume);
    $("#zsatuan").val(obj_arahan.satuan);
    $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
    var j = function () {
    var defer = $.Deferred();
    refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var a = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var b = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    var c = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode;

    var selected = "";
    if (obj_arahan.kws_kode == null) {
    selected = "-1";
    } else {

    selected = obj_arahan.kws_kode;
    }
    refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    //wps_kode,kws_kode
    var d = function () {
    var defer = $.Deferred();
    var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
    refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");

    setTimeout(function () {
    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
    }, 500);
    return defer;
    };
    c().then(d).then(j).then(a).then(b);
    data_cart_edit.push(obj_arahan);
    console.log("data cart")
            console.log(JSON.stringify(data_cart_edit));
    }

    //memanaggil API
    function get_data_wps() {

    var id_province = $("#id_rprov").val();
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_wps2') ?>" + "/" + id_province;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }


    function get_data_kawasan(kawasan_kode) {
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_kws2') ?>" + "/" + kawasan_kode;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function get_data_sub_kawasan(kawasan_kode) {
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_kws2') ?>" + "/" + kawasan_kode;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            console.log("javascript response")
                    console.log(typeof response)
                    x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }


    function get_ruas_by_province() {
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_ruas_by_province') ?>";
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
            x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function isNull(data) {
    var result = "";
    if (data == "-") {
    result = "#"; //--pilih--
    } else if (data == "") {
    result = "#";
    } else if (data == null) {
    result = "#";
    }

    return result;
    }


    function check_rc(data) {
    var result = "";
    if (isNaN(data) == false) {

    if (data < yearnow) {
    result = "siap";
    } else {
    result = "tidak_siap";
    }

    } else {

    if (data == "-") {
    result = "#"; //--pilih--
    } else if (data == "") {
    result = "#";
    } else if (data == null) {
    result = "#";
    }

    }
    return result;
    }

    function fnFormatDetails(table_id, html) {

    return "<table style='overflow=scroll' id=\"tlist_detail_" + table_id + "\">" +
            "<thead>" +
            "<tr>" +
            "<th>Rujukan</th>" +
            "<th>ID</th>" +
            // "<th>Tahun</th>" +
            // "<th>Paket</th>" +
            "<th>Uraian</th>" +
            "<th>Jenis Beban</th>" +
              "<th>Akun</th>" +
//            "<th>Ruas</th>" +,
            // "<th>STA Awal</th>" +
            // "<th>STA Akhir</th>" +
            // "<th>Koord. X</th>" +
            // "<th>Koord. Y</th>" +
            // "<th>Koord. X2</th>" +
            //  "<th>Koord. Y2</th>" +
            "<th>Volume</th>" +
            "<th>Satuan</th>" +


            "<th>Jumlah</th>" +
            "<th>Action</th>" +
            "</tr>" +
            "</thead>" +
            "</table>";
    }

    function bind_wps_by_province() {
    var obj_wps = get_data_wps();
    $("#wps_kode").empty();
    $("#wps_kode").append("<option value='#'>Pilih</option>");
    for (var i = 0; i <= obj_wps.length - 1; i++) {
    var option_value = obj_wps[i].wps_kode + "::" + obj_wps[i].kws_kode
            var html_option = [
                    "<option value=" + option_value + " >",
                    obj_wps[i].wps_nama,
                    "</option>"
            ].join("\n");
    $("#wps_kode").append(html_option);
    }
    }

    function bind_kawasan_by_kws_kode(element) {

        if(element.value=='#')
            {
                $("#kws_kode").prop("disabled",true);
                $("#subkw").prop("disabled",true);



            }
            else
                {
                $("#kws_kode").prop("disabled",false);
                       $("#subkw").prop("disabled",true);
//                $("#subkw").prop("disabled",false);
    var kawasan_kode = element.value.split('::')[1];
    var obj_kawasan = get_data_kawasan(kawasan_kode);
    $("#kws_kode").empty();
    $("#subkw").empty();

    $("#kws_kode").append("<option value='#'>--Pilih--</option>");
    for (var i = 0; i <= obj_kawasan.length - 1; i++) {
    var option_value = obj_kawasan[i].kws_kode + "::" + obj_kawasan[i].subkawasan_nama;
    var html_option = [
            "<option value=" + option_value + " >",
            obj_kawasan[i].kws_nama,
            "</option>"
    ].join("\n");
    $("#kws_kode").append(html_option);
    }
    bind_subkawasan(kawasan_kode);
                }

    }

    function get_data_subkawasan(kawasan_kode){
    var x = null;
    var ajaxurl = "<?php echo base_url('pagu_tahapan/get_subkws2') ?>" + "/" + kawasan_kode;
    $.ajax({
    type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {

            x = response;
            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    return x;
    }

    function bind_subkawasan(kawasan_kode) {
                if(kawasan_kode.value=='#')
            {
                //$("#kws_kode").prop("disabled",true);
                $("#subkw").prop("disabled",true);

            }
        else
            {
//        alert(kawasan_kode.value);
                 $("#subkw").prop("disabled",false);
    var obj_subkawasan = get_data_subkawasan(kawasan_kode);
//    $("#subkw").append("<option value='#'>--Pilih--</option>");
    for (var i = 0; i <= obj_subkawasan.length; i++) {
    var option_value = obj_subkawasan[i].subkawasan_nama;
    var html_option = [
            "<option value='" + option_value + "' >",
            obj_subkawasan[i].subkawasan_nama,
            "</option>",
    ].join("\n");
    $("#subkw").append(html_option);
    }
    }
    }

    var iTableCounter = 1;
    var oTable;
    var oInnerTable;
    var TableHtml;
    function count_jumlah(element) {
    var hargasatuan = element.value.replace(/\D/g, '');
    //modal lain
    var volume = $("#volume").val();
    var jumlah = parseFloat(hargasatuan) * volume;
    $("#yjumlah").val(jumlah);
    //modal tambah
    var xvolume = $("#xvolume").val();
    var xjumlah = parseFloat(hargasatuan) * parseFloat(xvolume);
    $("#xjumlah").val(xjumlah);
    var wvolume = $("#wvolume").val();
    var wjumlah = parseFloat(hargasatuan) * parseFloat(wvolume);
    $("#wjumlah").val(wjumlah);
    //formating number
    $("#xhargasat").val(numberWithCommas(hargasatuan));
    $("#xjumlah").val(numberWithCommas($("#xjumlah").val()));
    $("#xrm").val(numberWithCommas($("#xjumlah").val()));
    $("#yhargasat").val(numberWithCommas(hargasatuan));
    $("#yjumlah").val(numberWithCommas($("#yjumlah").val()));
    $("#yrm").val(numberWithCommas($("#yjumlah").val()));
    $("#whargasat").val(numberWithCommas(hargasatuan));
    $("#wjumlah").val(numberWithCommas($("#wjumlah").val()));
    //modal tambah item
    $("#rm").val(numberWithCommas($("#yjumlah").val()));
    }

    function get_sum_batas(p) {
    $("#conte").show();
    var x = p.value;
    var z = 'kosong';
    alert(x);
            $.ajax({
            url: "<?php echo base_url('pagu_tahapan/sum_batas') ?>/" + x + "/" + z,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                    alert(data);
                            $("#box-bts-prov").empty();
                    $("#box-bts-prov").append(data);
                            $("#btspagu").empty();
                    $("#btspagu").append(data);
                            $("#textbtspagu").text("Total Batas Pagu Provinsi");
                    },
                    failure: function (errMsg) {
                    alert(errMsg);
                    }
            });
    }

    function get_sum_batas_output(p) {
    $("#conte").show();
    var x = p.value;
    var z = $("#kd_prov").val();
    alert(x);
            $.ajax({
            url: "<?php echo base_url('pagu_tahapan/sum_batas') ?>/" + z + "/" + x,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                    alert(data);
                            $("#box-bts-output").empty();
                    $("#box-bts-output").append(data);
                            $("#btspagu").empty();
                    $("#btspagu").append(data);
                            $("#textbtspagu").text("Total Batas Pagu Output");
                    },
                    failure: function (errMsg) {
                    alert(errMsg);
                    }
            });
    }

    function filters() {
    var x = $("#kd_prov").val();
    var z = $("#outs").val();
    if (z == '' || z == 'undefined' || z == '#')
    {
    var l = 'abc';
    } else
    {
    var l = $("#outs").val();
    }
    var yuhu = '';
    $.ajax({
    url: "<?php echo base_url('pagu_tahapan/sum_usulan') ?>/" + x + "/" + l,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
            alert(data);
            yuhu = data;

            },
            failure: function (errMsg) {
            alert(errMsg);
            }
    });
    alert(yuhu);
    if (parseInt($("#btspagu").text()) < yuhu)
    {
    var ale = "<div class='alert alert-danger'> <strong>Danger!</strong> Indicates a dangerous or potentially negative action.</div>";

            $("#colo").css({"color": "red"});
    } else if (parseInt($("#btspagu").text()) == 0 && yuhu == 0)
    {
    $("#conte").css({"display": "none"});
    $("#alertss").css({"display": "none"});
    }
    else
    {
    var ale = "<div class='alert alert-success'> <strong>Success!</strong> Indicates a successful or positive action.</div>";
            $("#colo").css({"color": "green"});
    }
    $("#alertss").empty();
    setTimeout(function () {
    $("#alertss").append(ale);
    }, 2000);
    var tlist_paket = $('#tlist_paket').DataTable();
    tlist_paket.ajax.reload();
    }


    function dataTablepaket() {

    tlist_paket = $("#tlist_paket").DataTable({
                  //  "initComplete": function(settings, json) {
                 //alert( 'DataTables has finished its initialisation.' );
//                 $("#tlist_paket").wrap( "<div class='double-scroll-tlist-paket'></div>" );
//                 $('.double-scroll-tlist-paket').doubleScroll();
//                 $('#tlist_paket').doubleScroll({
//                            resetOnWindowResize: true
//                 });
                 //alert("99999");
            // },
            // "scrollY":600,
            // "scrollX":true,
            "draw": 0,
            "fixedHeader": true,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "deferRender": true,
            "ajax": {
            url: "<?php echo base_url(); ?>pagu_tahapan/ssp_paket",
                    type: "POST",
                    "iTotalRecords":  8500,
                    "iTotalDisplayRecords": 5,
                    "data": function (