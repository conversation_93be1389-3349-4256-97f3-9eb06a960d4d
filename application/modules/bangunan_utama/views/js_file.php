<script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/wayjs/way.js"></script>
<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;
    function clear_input() {
        $("#formData :input").val("");
    }


    
    function listing() {
        table = $('#dt-server-processing').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>bangunan_utama/ssp_paket","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
                // d.thang = $('#fthang').val();
                
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full) {
                        return full[0];
                    }
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                },
                {
                    "aTargets": [2],
                    "mRender": function (data, type, row) {
                        var id = row[0];
                        var html_button = [
                            "<button onclick= dtTambahRow('edit','" + id + "') class='btn btn-primary btn-xs'>",
                            "Edit",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "Hapus",
                            "</button>",
                            "<button onclick= dtTambahRow('detail','" + id + "') class='btn btn-warning btn-xs'>",
                            "Detail",
                            "</button>",
                            //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                // "paginate": {
                //     "first": "<i class='fast backward ui icon'></i>",
                //     "last": "<i class='fast forward ui icon'></i>",
                //     "next": "<i class='step forward ui icon'></i>",
                //     "previous": "<i class='step backward ui icon'></i>"
                // },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }

    function dtTambahRow(kat,id) {

    $(".error").text('')
    $(".error").css({'font-size':'14px','color':'#353c4e'})
    $(".form-tambah .form-group input").prop('readonly',false)
    $(".form-tambah .form-group input").css({"border":"1px solid #7889bd8f","background":"#e9ecef45"})
    $(".form-tambah .form-group select").prop('disabled',false)
    $(".form-tambah .form-group select").css({"border":"1px solid #7889bd8f","background":"#e9ecef45"})
    $(".form-tambah .form-group textarea").prop('readonly',false)
    $(".form-tambah .form-group textarea").css({"border":"1px solid #7889bd8f","background":"#e9ecef45"})
    $(".form-tambah .form-group button").prop('disabled',false)
    $(".form-tambah .form-group button").css({"border":"1px solid #7889bd8f","background":"#e9ecef45"})
    $(".form-tambah .form-group").css({"border-bottom":"0px"})
     $(".form-tambah .form-group input").val('')
     $(".form-tambah .form-group select").val('') 
     $("#kd_bangunan_utama").prop("readonly",true    )
        if(kat=='tambah'){
             $("#id").val('')
             
        }else{
              var dt = tampil_data('r_jns_bgn_utama','id_jnsbgnut',id);
                // if(dt[0].path != null){
                    
                // }
                //  $('#file_npwp').val();
                //  alert(subs_img)
                                            
                $('#kd_bangunan_utama').val(dt[0].id_jnsbgnut);
                $('#nm_bangunan_utama').val(dt[0].nama_jnsbgnut);
                $('#id').val(id);
             

        }


        if(kat=='detail'){
            $(".form-tambah .form-group input").prop('readonly',true)
            $(".form-tambah .form-group input").css({"border":"0px","background":"#e9ecef45"})
            $(".form-tambah .form-group select").prop('disabled',true)
            $(".form-tambah .form-group select").css({"border":"0px","background":"#e9ecef45"})
            $(".form-tambah .form-group textarea").prop('readonly',true)
            $(".form-tambah .form-group textarea").css({"border":"0px","background":"#e9ecef45"})
            $(".form-tambah .form-group button").prop('disabled',true)
            $(".form-tambah .form-group button").css({"border":"0px","background":"#e9ecef45"})
            $(".form-tambah .form-group").css({"border-bottom":"1px solid #80808054"})
        }

                $(".decformat").keyup(function (event) {
                if (event.which >= 37 && event.which <= 40)
                        return;
                // format number
                $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                });
                });
  

                $('.form-group').removeClass('has-error'); // clear error class
                $('.help-block').empty(); // clear error string
                $('#modal-tambah').modal('show');
                $('.tbhItem').text('Form bangunan_utama');
                // $("#urutans").empty();
                // $("#urutans").val("2");
                
                //--- end dt tambah row ---
}

    function dtDeleteRow(id){
      
        swal({
            title: "Anda yakin menghapus data ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
            var url=''
            url = "<?php echo base_url(); ?>" + "bangunan_utama/ajax_delete/" + id;
                $.get(url).done(function (data) {
                    if(data==0){
                    swal({
                            title: "",
                            text: "Berhasil Menghapus Data",
                            icon: "success",
                            showConfirmButton: false,
                            timer:3000,
                            type:"success"
                        });
                    table.ajax.reload();
                    }
                })
            } else {
            swal("Anda Tidak Menjadi Menghapus Data");
            }
        });
        }

    function hitung(yuhu,hasil){
        var total=0;
        var yuhu = yuhu.split('+')
        var satu =  $("#"+yuhu[0]).val()
        var dua =  $("#"+yuhu[1]).val()
            if (isNaN(parseInt(satu))) {
              satu = 0
              }
            if (isNaN(parseInt(dua))) {
              dua = 0
              }
              var to = parseInt(satu)+parseInt(dua)
        $("#"+hasil).val(to)
        // for (let i = 0; i < satu.length; i++) {
        //     var satu =  $("#"+satu[i]).val()
        //     if (isNaN(parseInt(satu))) {
        //       satu = 0
        //   }
        //     parseInt(total) += satu;
        //     } 
        //     alert(total)
      


    }
function isNumber(evt,a) {
    
        evt = (evt) ? evt : window.event;
        var charCode = (evt.which) ? evt.which : evt.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    }




   

    
    function tampil_data(table,colum,id){
        var url=''
        var tadata=''
        urls = "<?php echo base_url(); ?>bangunan_utama/tampildata/"+table+"/"+colum+"/"+id;
        $.ajax({
                    url: urls,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                    tadata = data;
                    },
                    failure: function (errMsg) {
                    alert(errMsg);
                    }
            });
            return tadata;
        }
    $(document).ready(function () {
        // bind_combo_thang(thangs);
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");

        $("#submit").submit(function(e){
            
     e.preventDefault()
     // ;
      if($(".form-tambah").valid()==true){
    var data = $('.form-tambah').serialize();
    console.log(new FormData(this))
    $.ajax({
      type: 'POST',
      url: "<?php echo base_url('bangunan_utama/insert_data');?>",
      data: new FormData(this),
      processData:false,
      contentType:false,
      cache:false,
      async:false,
      success: function(data) {
        var ops=''
        if(data.split('_')[1]=='insert'){
          ops='Menambah';
        }else{
          ops='Merubah';
        }
        if(data.split('_')[0]==0){
          swal({
                icon: "success",
                text: "Berhasil "+ops+" Data",
                showConfirmButton: false,
                timer:2000,
                type:"success"
              });
              table.ajax.reload();
              $('#modal-tambah').modal('hide');
        }else{
          swal({
                title: "",
                text: "Gagal "+ops+" Data",
                showConfirmButton: false,
                timer:1000,
                icon:"warning"
              });
        }

      }
    });
 }
    });
      $(".form-tambah").validate({
          ignore: ':not(select:visible,button:visible, input:visible, textarea:visible, .selectpicker)',
          rules: {
            kd_bangunan_utama: { required: true  },
            nm_bangunan_utama: { required: true  },
            keterangan: { required: false  },
          }
        });
        
        
    });













</script>