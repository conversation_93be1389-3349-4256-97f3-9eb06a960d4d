<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
//require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Bangunan_utama extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        // $this->load->model('M_pagu_paket');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }
       

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $bangunan_utama = $this->session->users['kd_satker'];
        $title = " Bangunan Utama ";

        // $js_file2 = $this->load->view('bangunan_utama/js_file2', '', true);

        $js_file = $this->load->view('bangunan_utama/js_file', '', true);
        $modal_tambah = $this->load->view('bangunan_utama/modal_tambah', '', true);
        $modal_download = $this->load->view('bangunan_utama/modal_download', '', true);
        // if (!empty($this->session->users['kd_bujt'])) {
        //     $kd_prov_irmsv3 = $this->get_prov_irmsv3($this->session->users['kd_bujt']);
        // }

        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_download" => $modal_download,
            "title" => $title,
            "jv_script" => $js_file
        );
        // print_r($data);
        $this->load->view('index', $data);
    }

    public function ssp_paket() {
       
        
         $table = 'r_jns_bgn_utama';
         $primaryKey = 'id_jnsbgnut'; //test
 
         $columns = array(
             array('db' => 'id_jnsbgnut', 'dt' => 0),
             array('db' => 'nama_jnsbgnut', 'dt' => 1)
    
         );
 
         datatable_ssp($table, $primaryKey, $columns);
 
     }
 
 
     function tampildata($tab='',$colum='',$val_colum=''){
     

          $data=$this->db->get_where($tab,array($colum=>str_replace('_', ' ', $val_colum)))->result_array();
         
          echo json_encode($data);
        }
    
function insert_data(){
    $id=$this->input->post('id', true);
     $kd_bangunan_utama=$this->input->post('kd_bangunan_utama', true);
    $nm_bangunan_utama=$this->input->post('nm_bangunan_utama', true);
    // $keterangan=$this->input->post('keterangan');
    $data=array(
                'nama_jnsbgnut'=>$nm_bangunan_utama
              );
    if($id==''){
      $this->db->set('created_at', 'NOW()', FALSE);
      $this->db->set('created_by', $this->session->users['id_user'], FALSE);
      $x=$this->db->insert('r_jns_bgn_utama',$data);
      $z="insert";
    }else{
        
      $this->db->set('updated_at', 'NOW()', FALSE);
      $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
      $this->db->where('id_jnsbgnut', $id);
      $x=$this->db->update('r_jns_bgn_utama', $data);
      $z="update";
    }
    if($x){
      echo "0_".$z;
    }else{
    $this->db->last_query();
        // $errMess = $this->db->_error_message();
      echo "1_".$z;
    }
  }
  public function ajax_delete($id) {
     $this->db->where("id_jnsbgnut",$id);
     $dt = $this->db->delete('r_jns_bgn_utama');
    if($dt){
     echo 0;
     }else{
         echo "gagal";
     }
 }

}