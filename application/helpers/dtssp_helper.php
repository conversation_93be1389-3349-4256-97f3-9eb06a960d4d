<?php

/**
 * WGI Datatable SSP helper
 * 
 */
defined('BASEPATH') OR exit('No direct script access allowed');


// ------------------------------------------------------------------------

if (!function_exists('datatable_ssp')) {


    function datatable_ssp($table, $primarykey, $columns, $where=NULL) {
        /*         * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
         * If you just want to use the basic configuration for DataTables with PHP
         * server-side, there is no need to edit below this line.
         */


        // SQL server connection information
        $sql_details = array(
            'user' => WGI_DB_LRS_USER,
            'pass' => WGI_DB_LRS_PWD,
            'db' => WGI_DB_LRS,
            'host' => WGI_DB_LRS_HOST,
            'port' => WGI_DB_LRS_PORT
        );

        require( WGI_APP_BASE_FOLDER.'application/libraries/SspHandler.php' );

        if ($where !== NULL) {
            echo json_encode(
                  //  SSP::complex($_GET, $sql_details, $table, $primarykey, $columns, null, $where)
                    SSP::complex($_POST, $sql_details, $table, $primarykey, $columns, null, $where)
            );
        } else {
            echo json_encode(
//                    SSP::complex($_GET, $sql_details, $table, $primarykey, $columns, null, $where)
                    SSP::simple($_POST, $sql_details, $table, $primarykey, $columns)
            );
        }
    }

}
