<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome Compatibility Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Chrome Compatibility Test for dtTambahRow() and tampil_data()</h1>
    
    <div class="test-section">
        <h2>Test 1: Inline onclick Attribute Syntax</h2>
        <p>Testing if onclick attributes with proper quotes work in Chrome:</p>
        <button class="test-button" onclick="testFunction('edit', '123')">Test Edit Button</button>
        <button class="test-button" onclick="testFunction('detail', '456')">Test Detail Button</button>
        <div id="test1-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Event Delegation</h2>
        <p>Testing event delegation for dynamically generated content:</p>
        <div id="dynamic-content"></div>
        <button onclick="generateDynamicContent()">Generate Dynamic Buttons</button>
        <div id="test2-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Asynchronous AJAX Simulation</h2>
        <p>Testing asynchronous data loading:</p>
        <button onclick="testAsyncData()">Test Async Data Loading</button>
        <div id="test3-result"></div>
    </div>

    <div class="test-section">
        <h2>Browser Information</h2>
        <div id="browser-info"></div>
    </div>

    <script>
        // Test functions
        function testFunction(action, id) {
            const result = document.getElementById('test1-result');
            result.innerHTML = `<div class="success">✓ Function called successfully: ${action} with ID ${id}</div>`;
            console.log('testFunction called:', action, id);
        }

        function generateDynamicContent() {
            const container = document.getElementById('dynamic-content');
            container.innerHTML = `
                <span class="test-button" onclick="testFunction('dynamic', '789')">Dynamic Button 1</span>
                <span class="test-button" onclick="testFunction('dynamic', '101')">Dynamic Button 2</span>
            `;
            
            // Test event delegation
            $(document).off('click', '[onclick*="testFunction"]').on('click', '[onclick*="testFunction"]', function(e) {
                e.preventDefault();
                const onclickAttr = $(this).attr('onclick');
                if (onclickAttr) {
                    try {
                        const match = onclickAttr.match(/testFunction\('([^']+)',\s*'([^']+)'\)/);
                        if (match) {
                            const action = match[1];
                            const id = match[2];
                            testFunction(action, id);
                            document.getElementById('test2-result').innerHTML = 
                                `<div class="success">✓ Event delegation working: ${action} with ID ${id}</div>`;
                        }
                    } catch (error) {
                        document.getElementById('test2-result').innerHTML = 
                            `<div class="error">✗ Event delegation failed: ${error.message}</div>`;
                    }
                }
            });
        }

        function testAsyncData() {
            const result = document.getElementById('test3-result');
            result.innerHTML = '<div class="info">Loading data...</div>';
            
            // Simulate async data loading
            setTimeout(() => {
                const mockData = [
                    { id: 1, name: 'Test Item 1', status: 'active' },
                    { id: 2, name: 'Test Item 2', status: 'inactive' }
                ];
                
                // Simulate callback-based async function
                simulateTampilData('test_table', 'id', '123', function(data) {
                    if (data) {
                        result.innerHTML = `<div class="success">✓ Async data loaded successfully: ${JSON.stringify(data)}</div>`;
                    } else {
                        result.innerHTML = `<div class="error">✗ Failed to load async data</div>`;
                    }
                });
            }, 1000);
        }

        function simulateTampilData(table, column, id, callback) {
            // Simulate the improved tampil_data function
            const mockAjax = {
                url: `/api/${table}/${column}/${id}`,
                async: true,
                success: function(data) {
                    callback(data);
                },
                error: function(error) {
                    console.error('Simulated AJAX Error:', error);
                    callback(null);
                }
            };
            
            // Simulate successful response
            setTimeout(() => {
                mockAjax.success({ id: id, table: table, column: column, loaded: true });
            }, 500);
        }

        // Display browser information
        function displayBrowserInfo() {
            const info = document.getElementById('browser-info');
            const userAgent = navigator.userAgent;
            let browserName = 'Unknown';
            
            if (userAgent.indexOf('Chrome') > -1) {
                browserName = 'Chrome';
            } else if (userAgent.indexOf('Firefox') > -1) {
                browserName = 'Firefox';
            } else if (userAgent.indexOf('Safari') > -1) {
                browserName = 'Safari';
            } else if (userAgent.indexOf('Edge') > -1) {
                browserName = 'Edge';
            }
            
            info.innerHTML = `
                <strong>Browser:</strong> ${browserName}<br>
                <strong>User Agent:</strong> ${userAgent}<br>
                <strong>jQuery Version:</strong> ${typeof $ !== 'undefined' ? $.fn.jquery : 'Not loaded'}
            `;
        }

        // Initialize tests when page loads
        $(document).ready(function() {
            displayBrowserInfo();
            console.log('Chrome compatibility test page loaded');
        });
    </script>
</body>
</html>
